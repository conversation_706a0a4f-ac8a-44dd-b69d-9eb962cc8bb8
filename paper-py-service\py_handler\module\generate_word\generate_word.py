"""
Word 文档生成器的核心实现模块。
提供以下功能：
- 文本和段落处理
- 数学公式渲染
- 表格生成
- 图片插入
- 文档格式和样式设置
"""
import os
import json
from io import BytesIO
from flask import request, send_file, current_app
from docx import Document
from docx.enum.text import WD_BREAK
from .routes import generate_app
import re
from pydoc import doc
from docx.shared import Inches,Pt
from flask import Flask, Blueprint
from docx.oxml.shared import OxmlElement
from docx.oxml.shared import qn
import requests
from docx.shared import Pt
from docx.oxml import parse_xml
from docx.oxml.ns import nsdecls
from latex2word import LatexToWordElement
# from latex2mathml.converter import convert  
from docx.shared import Inches,Pt,RGBColor


def add_caption(paragraph, caption_text, is_table=False):
    """
    # 添加图注或表注
    # param paragraph: 段落对象
    # param caption_text: 注释文本
    # param is_table: True为表注，False为图注
    """
    caption_paragraph = paragraph.insert_paragraph_before()
    caption_run = caption_paragraph.add_run(caption_text)
    caption_run.font.size = Pt(10.5)  # 设置字号
    caption_paragraph.alignment = 1    # 居中对齐
    # TODO: 暂时不添加域代码，需要调整章节等级。较为麻烦。
    # caption_paragraph = paragraph.insert_paragraph_before()
    
    # # 创建题注域代码
    # run = caption_paragraph.add_run()
    # run.text = "表" if is_table else "图"  # 先添加"图"或"表"的文字
    
    # # 添加章节号域代码
    # fldChar = OxmlElement('w:fldChar')
    # fldChar.set(qn('w:fldCharType'), 'begin')
    # run._element.append(fldChar)
    
    # instrText = OxmlElement('w:instrText')
    # instrText.text = ' STYLEREF 1 \\s '  # 添加章节号
    # run._element.append(instrText)
    
    # fldChar = OxmlElement('w:fldChar')
    # fldChar.set(qn('w:fldCharType'), 'separate')
    # run._element.append(fldChar)
    
    # t = OxmlElement('w:t')
    # t.text = "1"  # 默认章节号
    # run._element.append(t)
    
    # fldChar = OxmlElement('w:fldChar')
    # fldChar.set(qn('w:fldCharType'), 'end')
    # run._element.append(fldChar)
    
    # # 添加点号
    # run = caption_paragraph.add_run(".")
    
    # # 添加序号域代码
    # run = caption_paragraph.add_run()
    # fldChar = OxmlElement('w:fldChar')
    # fldChar.set(qn('w:fldCharType'), 'begin')
    # run._element.append(fldChar)
    
    # instrText = OxmlElement('w:instrText')
    # if is_table:
    #     instrText.text = ' SEQ 表 \\* ARABIC '
    # else:
    #     instrText.text = ' SEQ 图 \\* ARABIC '
    # run._element.append(instrText)
    
    # fldChar = OxmlElement('w:fldChar')
    # fldChar.set(qn('w:fldCharType'), 'separate')
    # run._element.append(fldChar)
    
    # t = OxmlElement('w:t')
    # t.text = "1"  # 默认序号
    # run._element.append(t)
    
    # fldChar = OxmlElement('w:fldChar')
    # fldChar.set(qn('w:fldCharType'), 'end')
    # run._element.append(fldChar)
    
    # # 添加标题文本
    # caption_run = caption_paragraph.add_run(f"  {caption_text}")
    # caption_run.font.size = Pt(10.5)  # 设置字号
    # caption_paragraph.alignment = 1    # 居中对齐
    
    
    
def add_equation(paragraph, latex):
    """
    # 将LaTeX公式转换为Office Math ML并添加到段落中
    # param paragraph: 段落对象
    # param latex: LaTeX公式文本
    """
    try:
        # 剔除$符号
        latex = latex.strip('$')
        print(f"尝试转换公式：{latex}")
        latex_element = LatexToWordElement(latex)
        latex_element.add_latex_to_paragraph(paragraph)
        # 设置公式字体大小
        for run in paragraph.runs:
            run.font.size = Pt(10.5)  # 统一公式字号
            
    except Exception as e:
        print(f"公式转换失败: {str(e)}")
        # 转换失败时添加原始LaTeX并标记
        run = paragraph.add_run(f"[转换失败] ${latex}$")
        run.font.color.rgb = RGBColor(255, 0, 0)  # 使用红色标记失败的公式
        
def handle_single_line_equation(paragraph, content):
    """
    # 处理单行公式
    # param paragraph: 段落对象
    # param content: 包含公式的文本内容
    # return: 处理后的段落对象
    """
    new_paragraph = paragraph.insert_paragraph_before('', style='Normal')
    
    # 如果内容只包含公式，则居中对齐且不缩进
    if content.strip().startswith('$') and content.strip().endswith('$'):
        new_paragraph.alignment = 1  # 居中对齐
    else:
        new_paragraph.paragraph_format.first_line_indent = Inches(0.3)
    
    parts = content.split('$')
    for i, part in enumerate(parts):
        if i % 2 == 0:  # 普通文本
            if part:
                run = new_paragraph.add_run(part)
                run.font.size = Pt(10.5)
        else:  # 公式
            add_equation(new_paragraph, part.strip())
            
    return new_paragraph

def handle_multiline_equation(paragraph, equation_lines):
    """
    # 处理多行公式
    # param paragraph: 段落对象
    # param equation_lines: 公式内容行列表
    # return: 处理后的段落对象
    """
    equation_text = '\n'.join(equation_lines)
    new_paragraph = paragraph.insert_paragraph_before()
    new_paragraph.alignment = 1  # 居中对齐
    add_equation(new_paragraph, equation_text)
    return new_paragraph


def insert_content_paragraphs(document, current_paragraph, content,key):
    if content == '':
        return
    
    # 根据\n分割内容
    content_list = content.split('\n')
    current_table_lines = []
    is_in_table = False
    multiline_equation = []
    is_multiline_equation = False

    for content_item in content_list:
        if not content_item.strip() and not is_in_table:
            continue
        
        # 处理行内混合内容（文字和公式）
        if '$' in content_item:
            # First handle double dollar equations
            parts = re.split(r'(\$\$.*?\$\$)', content_item)
            new_parts = []
            for part in parts:
                if not part.startswith('$$'):
                    # Further split single dollar parts
                    single_parts = re.split(r'(\$.*?\$)', part)
                    new_parts.extend(single_parts)
                else:
                    new_parts.append(part)
            
            for part in new_parts:
                part = part.strip()
                if not part:
                    continue
                if part.startswith('$$') and part.endswith('$$'):
                    # 处理双美元公式部分
                    equation = part[2:-2].strip()
                    if equation:
                        new_paragraph = current_paragraph.insert_paragraph_before()
                        new_paragraph.alignment = 1  # 居中对齐
                        add_equation(new_paragraph, equation)
                elif part.startswith('$') and part.endswith('$'):
                    # 处理单美元公式部分
                    equation = part[1:-1].strip()
                    if equation:
                        add_equation(current_paragraph, equation)
                else:
                    # 处理文字部分
                    if part.strip():
                        run = current_paragraph.add_run(part)
                        run.font.size = Pt(10.5)
            continue

        # 处理多行公式开始
        if content_item.strip().startswith('$$'):
            is_multiline_equation = True
            equation = content_item.strip()[2:]
            if equation:
                multiline_equation.append(equation)
            continue
            
        # 处理多行公式结束
        if is_multiline_equation and content_item.strip().endswith('$$'):
            equation = content_item.strip()[:-2]
            if equation:
                multiline_equation.append(equation)
            if multiline_equation:
                handle_multiline_equation(current_paragraph, multiline_equation)
            multiline_equation = []
            is_multiline_equation = False
            continue
            
        # 收集多行公式内容
        if is_multiline_equation:
            multiline_equation.append(content_item.strip())
            continue
            
        # 检查是否是markdown图片语法
        img_match = re.match(r'!\[(.*?)\]\((.*?)\)', content_item)
        if img_match:
            caption_text = img_match.group(1)
            image_url = img_match.group(2)
            add_image_to_doc(document, current_paragraph, image_url, caption_text)
            continue
            
        # 检查是否是表格行
        if content_item.strip().startswith('|'):
            current_table_lines.append(content_item)
            is_in_table = True
            continue
            
        # 如果不是表格行，且之前在处理表格
        if is_in_table and not content_item.strip().startswith('|'):
            # 处理收集到的表格
            if current_table_lines:
                table_data = parse_markdown_table('\n'.join(current_table_lines))
                # 查找表格标题（如果有）
                caption_text = None
                if content_item.startswith('表'):
                    caption_text = content_item.strip()
                current_paragraph = add_table_to_doc(document, current_paragraph, table_data, caption_text)
                current_table_lines = []
            is_in_table = False
        
        # 处理普通文本
        if not is_in_table:
            new_paragraph = current_paragraph.insert_paragraph_before('', style='Normal')
            new_paragraph.text = content_item
            new_paragraph.paragraph_format.first_line_indent = Inches(0.3)
            # 如果是title 设置为居中对齐 一级标题
            if key == "title":
                new_paragraph.alignment = 1
                new_paragraph.style = 'Heading 1'
    
    return current_paragraph

def parse_markdown_table(table_text):
    """
    # 解析markdown表格文本为二维数组
    """
    lines = table_text.strip().split('\n')
    rows = []
    
    for line in lines:
        # 跳过分隔行 (---|---)
        if line.strip().startswith('|') and all(c in '|-:' for c in line.strip().strip('|')):
            continue
        
        # 如果是只包含破折号的行，也跳过
        if line.strip() == '----':
            continue
            
        # 处理表格行
        cells = [cell.strip().replace('<br>', '\n') for cell in line.strip('|').split('|')]
        rows.append(cells)
    
    return rows

# 添加表格-三线表样式
def add_table_to_doc(doc, paragraph, table_data, caption_text=None):
    """
    # 在文档中添加表格并维持光标位置
    """
    if not table_data:
        return
        
    try:
        # 1. 先创建段落放置表注
        caption_paragraph = None
        if caption_text:
            caption_paragraph = paragraph.insert_paragraph_before()
            add_caption(caption_paragraph, caption_text, is_table=True)
            
        # 2. 在当前段落之前创建新段落用于放置表格
        table_paragraph = paragraph.insert_paragraph_before()
        
        # 3. 在新段落中添加表格
        table = doc.add_table(rows=len(table_data), cols=len(table_data[0]))
        
        # 4. 设置三线表样式
        # 清除所有边框
        for row in table.rows:
            for cell in row.cells:
                tcPr = cell._tc.get_or_add_tcPr()
                # 先删除现有的边框设置
                existing_borders = tcPr.find(".//w:tcBorders", namespaces=tcPr.nsmap)
                if existing_borders is not None:
                    tcPr.remove(existing_borders)
                # 添加新的边框设置
                tcBorders = parse_xml(f'<w:tcBorders {nsdecls("w")}>'
                                   f'<w:top w:val="none"/>'
                                   f'<w:left w:val="none"/>'
                                   f'<w:bottom w:val="none"/>'
                                   f'<w:right w:val="none"/>'
                                   f'</w:tcBorders>')
                tcPr.append(tcBorders)

        # 添加顶部双线
        for cell in table.rows[0].cells:
            tcPr = cell._tc.get_or_add_tcPr()
            tcBorders = tcPr.find(".//w:tcBorders", namespaces=tcPr.nsmap)
            if tcBorders is None:
                tcBorders = parse_xml(f'<w:tcBorders {nsdecls("w")}/>')
                tcPr.append(tcBorders)
            top_border = parse_xml(f'<w:top {nsdecls("w")} w:val="double" w:sz="4" w:space="0"/>')
            existing_top = tcBorders.find(".//w:top", namespaces=tcPr.nsmap)
            if existing_top is not None:
                tcBorders.remove(existing_top)
            tcBorders.append(top_border)

        # 添加表头底部单线
        for cell in table.rows[0].cells:
            tcPr = cell._tc.get_or_add_tcPr()
            tcBorders = tcPr.find(".//w:tcBorders", namespaces=tcPr.nsmap)
            if tcBorders is None:
                tcBorders = parse_xml(f'<w:tcBorders {nsdecls("w")}/>')
                tcPr.append(tcBorders)
            bottom_border = parse_xml(f'<w:bottom {nsdecls("w")} w:val="single" w:sz="4" w:space="0"/>')
            existing_bottom = tcBorders.find(".//w:bottom", namespaces=tcPr.nsmap)
            if existing_bottom is not None:
                tcBorders.remove(existing_bottom)
            tcBorders.append(bottom_border)

        # 添加表格底部单线
        for cell in table.rows[-1].cells:
            tcPr = cell._tc.get_or_add_tcPr()
            tcBorders = tcPr.find(".//w:tcBorders", namespaces=tcPr.nsmap)
            if tcBorders is None:
                tcBorders = parse_xml(f'<w:tcBorders {nsdecls("w")}/>')
                tcPr.append(tcBorders)
            bottom_border = parse_xml(f'<w:bottom {nsdecls("w")} w:val="single" w:sz="4" w:space="0"/>')
            existing_bottom = tcBorders.find(".//w:bottom", namespaces=tcPr.nsmap)
            if existing_bottom is not None:
                tcBorders.remove(existing_bottom)
            tcBorders.append(bottom_border)

        # 填充表格内容并设置字体
        for i, row in enumerate(table_data):
            for j, cell in enumerate(row):
                # 获取单元格
                table_cell = table.cell(i, j)
                # 清除单元格现有内容
                for private_paragraph in table_cell.paragraphs:
                    private_paragraph.clear()  # 清除段落中的所有内容
                
                # 添加新内容
                cell_text = cell.strip() if cell else ''
                private_paragraph = table_cell.paragraphs[0]
                private_paragraph.alignment = 1  # 居中对齐
                run = private_paragraph.add_run(cell_text)
                run.font.size = Pt(10.5)
        
        
        # 5. 将表格放置在正确的位置
        if caption_paragraph:
            # 如果有表注，将表格插入到表注之后
            caption_paragraph._p.addnext(table._element)
        else:
            # 如果没有表注，将表格插入到新段落之前
            table_paragraph._p.addprevious(table._element)
        
        # 6. 删除空的表格段落
        table_paragraph._p.getparent().remove(table_paragraph._p)
        
        # 7. 返回原始段落，保持光标位置
        return paragraph
            
    except Exception as e:
        current_app.logger.error(f"添加表格失败: {str(e)}")
        print(f"错误类型: {type(e).__name__}")
        return paragraph
    
def add_image_to_doc(doc, paragraph, image_url,caption_text=None):
    """
    # 从URL添加图片到文档
    """
    try:
        # 添加调试信息
        print(f"尝试添加图片，URL: {image_url}")
        if not image_url:
            print("跳过空URL的图片")
            return
        max_width = Inches(6.0)
        max_height = Inches(4.0)
        
        # 添加请求头，模拟浏览器行为
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        response = requests.get(image_url, headers=headers, verify=False)  # verify=False 用于本地测试
        if response.status_code != 200:
            print(f"获取图片失败，状态码: {response.status_code}")
            return
            
        image_stream = BytesIO(response.content)
        
        # 添加图片到文档
        width = Inches(6.0)  # 默认宽度6英寸
        image_paragraph = paragraph.insert_paragraph_before()
        image_paragraph.alignment = 1
        image = image_paragraph.add_run().add_picture(image_stream)
        
        width = image.width
        height = image.height
        
        # 计算缩放比例
        width_ratio = max_width / width if width > max_width else 1
        height_ratio = max_height / height if height > max_height else 1
        
        # 使用较小的缩放比例，保持长宽比
        scale_ratio = min(width_ratio, height_ratio)
        
        # 应用新的尺寸
        image.width = int(width * scale_ratio)
        image.height = int(height * scale_ratio)
        
        # 添加图注
        if caption_text:
            add_caption(paragraph, caption_text)
            
    except Exception as e:
        current_app.logger.error(f"添加图片失败: {str(e)}")
        print(f"详细错误信息: ", e.__class__.__name__)


# 插入目录
def insert_toc(paragraph):
    run = paragraph.add_run()
    # 添加目录域代码
    fldChar = OxmlElement('w:fldChar')
    fldChar.set(qn('w:fldCharType'), 'begin')
    run._element.append(fldChar)
    
    instrText = OxmlElement('w:instrText')
    instrText.text = 'TOC \\o "1-3" \\h \\z \\u'
    run._element.append(instrText)
    
    fldChar = OxmlElement('w:fldChar')
    fldChar.set(qn('w:fldCharType'), 'separate')
    run._element.append(fldChar)
    
    fldChar = OxmlElement('w:t')
    fldChar.text = "右键点击更新域"
    run._element.append(fldChar)
    
    fldChar = OxmlElement('w:fldChar')
    fldChar.set(qn('w:fldCharType'), 'end')
    run._element.append(fldChar)

@generate_app.route('/document', methods=['POST'])
def generate_document():
    try:
        # 获取请求中的数据
        data = request.json
        if not data:
            return {"error": "No JSON data received"}, 400
        
        print("Received data:", data)  # 添加调试日志
        
        # 打开模板文档
        current_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        template_path = os.path.join(current_dir, "static", "paper.docx")
        
        print(f"Template path: {template_path}")  # 添加调试日志
        
        if not os.path.exists(template_path):
            return {"error": f"Template file not found at {template_path}"}, 500
            
        doc = Document(template_path)
        doc_stream = None
        
        try:
            # Your existing document processing code here
            paragraphs_to_remove = []

            print("Total paragraphs:", len(doc.paragraphs))  # 添加调试日志

            # 遍历文档中的所有段落
            for paragraph_index, paragraph in enumerate(doc.paragraphs):
                # 空段落不处理
                if paragraph.text.strip() == '':
                    continue
                
                print(f"Processing paragraph {paragraph_index}: {paragraph.text}")  # 添加调试日志
                
                # 遍历数据中的所有键值对
                for key, value in data.items():
                    # 如果段落中不包含占位符，则跳过
                    placeholder = f"{{{{{key}}}}}"
                    if placeholder not in paragraph.text:
                        continue
                    
                    print(f"Found placeholder: {placeholder} for key: {key}")  # 添加调试日志
                    
                    # 如果value是JSON字符串，尝试解析它
                    if isinstance(value, str):
                        try:
                            parsed_value = json.loads(value)
                            print(f"Parsed JSON value for {key}")  # 添加调试日志
                        except ValueError:
                            parsed_value = value
                    else:
                        parsed_value = value

                    # 关键词不创建新段落
                    if key in ["keywords","en_keywords","title"]:
                        paragraph.text = paragraph.text.replace(placeholder, str(parsed_value))
                        print(f"Replaced {key} with value")  # 添加调试日志
                        continue
                    
                    # 剔除占位
                    paragraph.text = paragraph.text.replace(placeholder, '')
                    paragraphs_to_remove.append(paragraph)

                    if isinstance(parsed_value, list) or key == "content":
                        print(f"Processing list content for {key}")  # 添加调试日志
                        # "数组"
                        for index,item in enumerate(parsed_value):
                            level = item.get('level', 1)
                            target = item.get("target", '')
                            content = item.get('content', '')


                            # 如果是 level 1，在添加新段落之前添加分页符
                            if level == 1 and index != 0:
                                paragraph.insert_paragraph_before().add_run().add_break(WD_BREAK.PAGE)

                            # 提纲
                            # 在当前段落前添加新段落
                            new_paragraph = paragraph.insert_paragraph_before('', style=f'Heading {level}')
                            new_paragraph.text = target

                            if level == 1:
                                # 每个章节下，再插入一个空行
                                paragraph.insert_paragraph_before('')

                            # 正文
                            insert_content_paragraphs(doc, paragraph, content,key)

                            # 每个小章节尾部插入一个空行,最后一个不插入
                            if level != 1 and index != len(parsed_value) - 1:
                                paragraph.insert_paragraph_before('')

                    elif isinstance(parsed_value, dict):
                        # "对象"
                        insert_content_paragraphs(doc, paragraph, parsed_value,key)
                    else:
                        insert_content_paragraphs(doc, paragraph, value,key)


            # 循环结束后，删除标记的空段落
            for para in paragraphs_to_remove:
                p = para._element
                p.getparent().remove(p)
            
            print("Saving document to BytesIO stream")  # 添加调试日志
            doc_stream = BytesIO()
            doc.save(doc_stream)
            doc_stream.seek(0)
            
            title = data.get('title', '')
            print(f"Document title: {title}")  # 添加调试日志
            
            # 验证文件内容
            content = doc_stream.getvalue()
            print(f"Document size: {len(content)} bytes")  # 添加调试日志
            
            if len(content) == 0:
                return {"error": "Generated document is empty"}, 500
            
            response = send_file(
                doc_stream,
                mimetype='application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                as_attachment=True,
                download_name=title + '.docx' if title else 'document.docx'
            )
            
            print("Document response prepared successfully")  # 添加调试日志
            
            # 添加回调以确保文件流在响应发送后被关闭
            @response.call_on_close
            def cleanup():
                if doc_stream:
                    try:
                        doc_stream.close()
                        print("Document stream closed")  # 添加调试日志
                    except:
                        print("Error closing document stream")  # 添加调试日志
                        pass
            
            return response
                
        except Exception as e:
            print(f"Error generating document: {str(e)}")  # 添加调试日志
            if doc_stream:
                try:
                    doc_stream.close()
                    print("Document stream closed after error")  # 添加调试日志
                except:
                    print("Error closing document stream after error")  # 添加调试日志
                    pass
            return {"error": f"生成文档失败: {str(e)}"}, 500
                
    except Exception as e:
        # Log error before creating response
        try:
            current_app.logger.error(f"Error in generate_document: {str(e)}")
        except:
            pass  # Ignore logging errors
        return {"error": str(e)}, 500
