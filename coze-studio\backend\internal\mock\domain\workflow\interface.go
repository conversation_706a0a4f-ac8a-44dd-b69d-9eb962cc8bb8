// Code generated by MockGen. DO NOT EDIT.
// Source: interface.go
//
// Generated by this command:
//
//	mockgen -destination ../../internal/mock/domain/workflow/interface.go --package mockWorkflow -source interface.go
//

// Package mockWorkflow is a generated GoMock package.
package mockWorkflow

import (
	context "context"
	reflect "reflect"

	model "github.com/cloudwego/eino/components/model"
	compose "github.com/cloudwego/eino/compose"
	schema "github.com/cloudwego/eino/schema"
	workflow "github.com/coze-dev/coze-studio/backend/api/model/ocean/cloud/workflow"
	workflow0 "github.com/coze-dev/coze-studio/backend/domain/workflow"
	entity "github.com/coze-dev/coze-studio/backend/domain/workflow/entity"
	vo "github.com/coze-dev/coze-studio/backend/domain/workflow/entity/vo"
	gomock "go.uber.org/mock/gomock"
)

// MockService is a mock of Service interface.
type MockService struct {
	ctrl     *gomock.Controller
	recorder *MockServiceMockRecorder
}

// MockServiceMockRecorder is the mock recorder for MockService.
type MockServiceMockRecorder struct {
	mock *MockService
}

// NewMockService creates a new mock instance.
func NewMockService(ctrl *gomock.Controller) *MockService {
	mock := &MockService{ctrl: ctrl}
	mock.recorder = &MockServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockService) EXPECT() *MockServiceMockRecorder {
	return m.recorder
}

// AsyncExecute mocks base method.
func (m *MockService) AsyncExecute(ctx context.Context, config vo.ExecuteConfig, input map[string]any) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AsyncExecute", ctx, config, input)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AsyncExecute indicates an expected call of AsyncExecute.
func (mr *MockServiceMockRecorder) AsyncExecute(ctx, config, input any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AsyncExecute", reflect.TypeOf((*MockService)(nil).AsyncExecute), ctx, config, input)
}

// AsyncExecuteNode mocks base method.
func (m *MockService) AsyncExecuteNode(ctx context.Context, nodeID string, config vo.ExecuteConfig, input map[string]any) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AsyncExecuteNode", ctx, nodeID, config, input)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AsyncExecuteNode indicates an expected call of AsyncExecuteNode.
func (mr *MockServiceMockRecorder) AsyncExecuteNode(ctx, nodeID, config, input any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AsyncExecuteNode", reflect.TypeOf((*MockService)(nil).AsyncExecuteNode), ctx, nodeID, config, input)
}

// AsyncResume mocks base method.
func (m *MockService) AsyncResume(ctx context.Context, req *entity.ResumeRequest, config vo.ExecuteConfig) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AsyncResume", ctx, req, config)
	ret0, _ := ret[0].(error)
	return ret0
}

// AsyncResume indicates an expected call of AsyncResume.
func (mr *MockServiceMockRecorder) AsyncResume(ctx, req, config any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AsyncResume", reflect.TypeOf((*MockService)(nil).AsyncResume), ctx, req, config)
}

// Cancel mocks base method.
func (m *MockService) Cancel(ctx context.Context, wfExeID, wfID, spaceID int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Cancel", ctx, wfExeID, wfID, spaceID)
	ret0, _ := ret[0].(error)
	return ret0
}

// Cancel indicates an expected call of Cancel.
func (mr *MockServiceMockRecorder) Cancel(ctx, wfExeID, wfID, spaceID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Cancel", reflect.TypeOf((*MockService)(nil).Cancel), ctx, wfExeID, wfID, spaceID)
}

// CopyWorkflow mocks base method.
func (m *MockService) CopyWorkflow(ctx context.Context, workflowID int64, policy vo.CopyWorkflowPolicy) (*entity.Workflow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CopyWorkflow", ctx, workflowID, policy)
	ret0, _ := ret[0].(*entity.Workflow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CopyWorkflow indicates an expected call of CopyWorkflow.
func (mr *MockServiceMockRecorder) CopyWorkflow(ctx, workflowID, policy any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CopyWorkflow", reflect.TypeOf((*MockService)(nil).CopyWorkflow), ctx, workflowID, policy)
}

// CopyWorkflowFromAppToLibrary mocks base method.
func (m *MockService) CopyWorkflowFromAppToLibrary(ctx context.Context, workflowID, appID int64, related vo.ExternalResourceRelated) (map[int64]entity.IDVersionPair, []*vo.ValidateIssue, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CopyWorkflowFromAppToLibrary", ctx, workflowID, appID, related)
	ret0, _ := ret[0].(map[int64]entity.IDVersionPair)
	ret1, _ := ret[1].([]*vo.ValidateIssue)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// CopyWorkflowFromAppToLibrary indicates an expected call of CopyWorkflowFromAppToLibrary.
func (mr *MockServiceMockRecorder) CopyWorkflowFromAppToLibrary(ctx, workflowID, appID, related any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CopyWorkflowFromAppToLibrary", reflect.TypeOf((*MockService)(nil).CopyWorkflowFromAppToLibrary), ctx, workflowID, appID, related)
}

// Create mocks base method.
func (m *MockService) Create(ctx context.Context, meta *vo.MetaCreate) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, meta)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockServiceMockRecorder) Create(ctx, meta any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockService)(nil).Create), ctx, meta)
}

// Delete mocks base method.
func (m *MockService) Delete(ctx context.Context, policy *vo.DeletePolicy) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", ctx, policy)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockServiceMockRecorder) Delete(ctx, policy any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockService)(nil).Delete), ctx, policy)
}

// DuplicateWorkflowsByAppID mocks base method.
func (m *MockService) DuplicateWorkflowsByAppID(ctx context.Context, sourceAPPID, targetAppID int64, related vo.ExternalResourceRelated) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DuplicateWorkflowsByAppID", ctx, sourceAPPID, targetAppID, related)
	ret0, _ := ret[0].(error)
	return ret0
}

// DuplicateWorkflowsByAppID indicates an expected call of DuplicateWorkflowsByAppID.
func (mr *MockServiceMockRecorder) DuplicateWorkflowsByAppID(ctx, sourceAPPID, targetAppID, related any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DuplicateWorkflowsByAppID", reflect.TypeOf((*MockService)(nil).DuplicateWorkflowsByAppID), ctx, sourceAPPID, targetAppID, related)
}

// Get mocks base method.
func (m *MockService) Get(ctx context.Context, policy *vo.GetPolicy) (*entity.Workflow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get", ctx, policy)
	ret0, _ := ret[0].(*entity.Workflow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockServiceMockRecorder) Get(ctx, policy any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockService)(nil).Get), ctx, policy)
}

// GetExecution mocks base method.
func (m *MockService) GetExecution(ctx context.Context, wfExe *entity.WorkflowExecution, includeNodes bool) (*entity.WorkflowExecution, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetExecution", ctx, wfExe, includeNodes)
	ret0, _ := ret[0].(*entity.WorkflowExecution)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExecution indicates an expected call of GetExecution.
func (mr *MockServiceMockRecorder) GetExecution(ctx, wfExe, includeNodes any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExecution", reflect.TypeOf((*MockService)(nil).GetExecution), ctx, wfExe, includeNodes)
}

// GetLatestNodeDebugInput mocks base method.
func (m *MockService) GetLatestNodeDebugInput(ctx context.Context, wfID int64, nodeID string, userID int64) (*entity.NodeExecution, *entity.NodeExecution, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLatestNodeDebugInput", ctx, wfID, nodeID, userID)
	ret0, _ := ret[0].(*entity.NodeExecution)
	ret1, _ := ret[1].(*entity.NodeExecution)
	ret2, _ := ret[2].(bool)
	ret3, _ := ret[3].(error)
	return ret0, ret1, ret2, ret3
}

// GetLatestNodeDebugInput indicates an expected call of GetLatestNodeDebugInput.
func (mr *MockServiceMockRecorder) GetLatestNodeDebugInput(ctx, wfID, nodeID, userID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLatestNodeDebugInput", reflect.TypeOf((*MockService)(nil).GetLatestNodeDebugInput), ctx, wfID, nodeID, userID)
}

// GetLatestTestRunInput mocks base method.
func (m *MockService) GetLatestTestRunInput(ctx context.Context, wfID, userID int64) (*entity.NodeExecution, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLatestTestRunInput", ctx, wfID, userID)
	ret0, _ := ret[0].(*entity.NodeExecution)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetLatestTestRunInput indicates an expected call of GetLatestTestRunInput.
func (mr *MockServiceMockRecorder) GetLatestTestRunInput(ctx, wfID, userID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLatestTestRunInput", reflect.TypeOf((*MockService)(nil).GetLatestTestRunInput), ctx, wfID, userID)
}

// GetNodeExecution mocks base method.
func (m *MockService) GetNodeExecution(ctx context.Context, exeID int64, nodeID string) (*entity.NodeExecution, *entity.NodeExecution, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNodeExecution", ctx, exeID, nodeID)
	ret0, _ := ret[0].(*entity.NodeExecution)
	ret1, _ := ret[1].(*entity.NodeExecution)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetNodeExecution indicates an expected call of GetNodeExecution.
func (mr *MockServiceMockRecorder) GetNodeExecution(ctx, exeID, nodeID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNodeExecution", reflect.TypeOf((*MockService)(nil).GetNodeExecution), ctx, exeID, nodeID)
}

// GetWorkflowDependenceResource mocks base method.
func (m *MockService) GetWorkflowDependenceResource(ctx context.Context, workflowID int64) (*vo.DependenceResource, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWorkflowDependenceResource", ctx, workflowID)
	ret0, _ := ret[0].(*vo.DependenceResource)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWorkflowDependenceResource indicates an expected call of GetWorkflowDependenceResource.
func (mr *MockServiceMockRecorder) GetWorkflowDependenceResource(ctx, workflowID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWorkflowDependenceResource", reflect.TypeOf((*MockService)(nil).GetWorkflowDependenceResource), ctx, workflowID)
}

// GetWorkflowReference mocks base method.
func (m *MockService) GetWorkflowReference(ctx context.Context, id int64) (map[int64]*vo.Meta, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWorkflowReference", ctx, id)
	ret0, _ := ret[0].(map[int64]*vo.Meta)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWorkflowReference indicates an expected call of GetWorkflowReference.
func (mr *MockServiceMockRecorder) GetWorkflowReference(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWorkflowReference", reflect.TypeOf((*MockService)(nil).GetWorkflowReference), ctx, id)
}

// ListNodeMeta mocks base method.
func (m *MockService) ListNodeMeta(ctx context.Context, nodeTypes map[entity.NodeType]bool) (map[string][]*entity.NodeTypeMeta, []entity.Category, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListNodeMeta", ctx, nodeTypes)
	ret0, _ := ret[0].(map[string][]*entity.NodeTypeMeta)
	ret1, _ := ret[1].([]entity.Category)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ListNodeMeta indicates an expected call of ListNodeMeta.
func (mr *MockServiceMockRecorder) ListNodeMeta(ctx, nodeTypes any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListNodeMeta", reflect.TypeOf((*MockService)(nil).ListNodeMeta), ctx, nodeTypes)
}

// MGet mocks base method.
func (m *MockService) MGet(ctx context.Context, policy *vo.MGetPolicy) ([]*entity.Workflow, int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MGet", ctx, policy)
	ret0, _ := ret[0].([]*entity.Workflow)
	ret1, _ := ret[1].(int64)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// MGet indicates an expected call of MGet.
func (mr *MockServiceMockRecorder) MGet(ctx, policy any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MGet", reflect.TypeOf((*MockService)(nil).MGet), ctx, policy)
}

// Publish mocks base method.
func (m *MockService) Publish(ctx context.Context, policy *vo.PublishPolicy) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Publish", ctx, policy)
	ret0, _ := ret[0].(error)
	return ret0
}

// Publish indicates an expected call of Publish.
func (mr *MockServiceMockRecorder) Publish(ctx, policy any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Publish", reflect.TypeOf((*MockService)(nil).Publish), ctx, policy)
}

// QueryNodeProperties mocks base method.
func (m *MockService) QueryNodeProperties(ctx context.Context, id int64) (map[string]*vo.NodeProperty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryNodeProperties", ctx, id)
	ret0, _ := ret[0].(map[string]*vo.NodeProperty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryNodeProperties indicates an expected call of QueryNodeProperties.
func (mr *MockServiceMockRecorder) QueryNodeProperties(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryNodeProperties", reflect.TypeOf((*MockService)(nil).QueryNodeProperties), ctx, id)
}

// ReleaseApplicationWorkflows mocks base method.
func (m *MockService) ReleaseApplicationWorkflows(ctx context.Context, appID int64, config *vo.ReleaseWorkflowConfig) ([]*vo.ValidateIssue, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReleaseApplicationWorkflows", ctx, appID, config)
	ret0, _ := ret[0].([]*vo.ValidateIssue)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReleaseApplicationWorkflows indicates an expected call of ReleaseApplicationWorkflows.
func (mr *MockServiceMockRecorder) ReleaseApplicationWorkflows(ctx, appID, config any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReleaseApplicationWorkflows", reflect.TypeOf((*MockService)(nil).ReleaseApplicationWorkflows), ctx, appID, config)
}

// Save mocks base method.
func (m *MockService) Save(ctx context.Context, id int64, schema string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Save", ctx, id, schema)
	ret0, _ := ret[0].(error)
	return ret0
}

// Save indicates an expected call of Save.
func (mr *MockServiceMockRecorder) Save(ctx, id, schema any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Save", reflect.TypeOf((*MockService)(nil).Save), ctx, id, schema)
}

// StreamExecute mocks base method.
func (m *MockService) StreamExecute(ctx context.Context, config vo.ExecuteConfig, input map[string]any) (*schema.StreamReader[*entity.Message], error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StreamExecute", ctx, config, input)
	ret0, _ := ret[0].(*schema.StreamReader[*entity.Message])
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StreamExecute indicates an expected call of StreamExecute.
func (mr *MockServiceMockRecorder) StreamExecute(ctx, config, input any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StreamExecute", reflect.TypeOf((*MockService)(nil).StreamExecute), ctx, config, input)
}

// StreamResume mocks base method.
func (m *MockService) StreamResume(ctx context.Context, req *entity.ResumeRequest, config vo.ExecuteConfig) (*schema.StreamReader[*entity.Message], error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StreamResume", ctx, req, config)
	ret0, _ := ret[0].(*schema.StreamReader[*entity.Message])
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StreamResume indicates an expected call of StreamResume.
func (mr *MockServiceMockRecorder) StreamResume(ctx, req, config any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StreamResume", reflect.TypeOf((*MockService)(nil).StreamResume), ctx, req, config)
}

// SyncExecute mocks base method.
func (m *MockService) SyncExecute(ctx context.Context, config vo.ExecuteConfig, input map[string]any) (*entity.WorkflowExecution, vo.TerminatePlan, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SyncExecute", ctx, config, input)
	ret0, _ := ret[0].(*entity.WorkflowExecution)
	ret1, _ := ret[1].(vo.TerminatePlan)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// SyncExecute indicates an expected call of SyncExecute.
func (mr *MockServiceMockRecorder) SyncExecute(ctx, config, input any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SyncExecute", reflect.TypeOf((*MockService)(nil).SyncExecute), ctx, config, input)
}

// SyncRelatedWorkflowResources mocks base method.
func (m *MockService) SyncRelatedWorkflowResources(ctx context.Context, appID int64, relatedWorkflows map[int64]entity.IDVersionPair, related vo.ExternalResourceRelated) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SyncRelatedWorkflowResources", ctx, appID, relatedWorkflows, related)
	ret0, _ := ret[0].(error)
	return ret0
}

// SyncRelatedWorkflowResources indicates an expected call of SyncRelatedWorkflowResources.
func (mr *MockServiceMockRecorder) SyncRelatedWorkflowResources(ctx, appID, relatedWorkflows, related any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SyncRelatedWorkflowResources", reflect.TypeOf((*MockService)(nil).SyncRelatedWorkflowResources), ctx, appID, relatedWorkflows, related)
}

// UpdateMeta mocks base method.
func (m *MockService) UpdateMeta(ctx context.Context, id int64, metaUpdate *vo.MetaUpdate) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateMeta", ctx, id, metaUpdate)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateMeta indicates an expected call of UpdateMeta.
func (mr *MockServiceMockRecorder) UpdateMeta(ctx, id, metaUpdate any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateMeta", reflect.TypeOf((*MockService)(nil).UpdateMeta), ctx, id, metaUpdate)
}

// ValidateTree mocks base method.
func (m *MockService) ValidateTree(ctx context.Context, id int64, validateConfig vo.ValidateTreeConfig) ([]*workflow.ValidateTreeInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValidateTree", ctx, id, validateConfig)
	ret0, _ := ret[0].([]*workflow.ValidateTreeInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ValidateTree indicates an expected call of ValidateTree.
func (mr *MockServiceMockRecorder) ValidateTree(ctx, id, validateConfig any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateTree", reflect.TypeOf((*MockService)(nil).ValidateTree), ctx, id, validateConfig)
}

// WithExecuteConfig mocks base method.
func (m *MockService) WithExecuteConfig(cfg vo.ExecuteConfig) compose.Option {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithExecuteConfig", cfg)
	ret0, _ := ret[0].(compose.Option)
	return ret0
}

// WithExecuteConfig indicates an expected call of WithExecuteConfig.
func (mr *MockServiceMockRecorder) WithExecuteConfig(cfg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithExecuteConfig", reflect.TypeOf((*MockService)(nil).WithExecuteConfig), cfg)
}

// WithMessagePipe mocks base method.
func (m *MockService) WithMessagePipe() (compose.Option, *schema.StreamReader[*entity.Message]) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithMessagePipe")
	ret0, _ := ret[0].(compose.Option)
	ret1, _ := ret[1].(*schema.StreamReader[*entity.Message])
	return ret0, ret1
}

// WithMessagePipe indicates an expected call of WithMessagePipe.
func (mr *MockServiceMockRecorder) WithMessagePipe() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithMessagePipe", reflect.TypeOf((*MockService)(nil).WithMessagePipe))
}

// WithResumeToolWorkflow mocks base method.
func (m *MockService) WithResumeToolWorkflow(resumingEvent *entity.ToolInterruptEvent, resumeData string, allInterruptEvents map[string]*entity.ToolInterruptEvent) compose.Option {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithResumeToolWorkflow", resumingEvent, resumeData, allInterruptEvents)
	ret0, _ := ret[0].(compose.Option)
	return ret0
}

// WithResumeToolWorkflow indicates an expected call of WithResumeToolWorkflow.
func (mr *MockServiceMockRecorder) WithResumeToolWorkflow(resumingEvent, resumeData, allInterruptEvents any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithResumeToolWorkflow", reflect.TypeOf((*MockService)(nil).WithResumeToolWorkflow), resumingEvent, resumeData, allInterruptEvents)
}

// WorkflowAsModelTool mocks base method.
func (m *MockService) WorkflowAsModelTool(ctx context.Context, policies []*vo.GetPolicy) ([]workflow0.ToolFromWorkflow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WorkflowAsModelTool", ctx, policies)
	ret0, _ := ret[0].([]workflow0.ToolFromWorkflow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// WorkflowAsModelTool indicates an expected call of WorkflowAsModelTool.
func (mr *MockServiceMockRecorder) WorkflowAsModelTool(ctx, policies any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WorkflowAsModelTool", reflect.TypeOf((*MockService)(nil).WorkflowAsModelTool), ctx, policies)
}

// MockRepository is a mock of Repository interface.
type MockRepository struct {
	ctrl     *gomock.Controller
	recorder *MockRepositoryMockRecorder
}

// MockRepositoryMockRecorder is the mock recorder for MockRepository.
type MockRepositoryMockRecorder struct {
	mock *MockRepository
}

// NewMockRepository creates a new mock instance.
func NewMockRepository(ctrl *gomock.Controller) *MockRepository {
	mock := &MockRepository{ctrl: ctrl}
	mock.recorder = &MockRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRepository) EXPECT() *MockRepositoryMockRecorder {
	return m.recorder
}

// BatchCreateConnectorWorkflowVersion mocks base method.
func (m *MockRepository) BatchCreateConnectorWorkflowVersion(ctx context.Context, appID, connectorID int64, workflowIDs []int64, version string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchCreateConnectorWorkflowVersion", ctx, appID, connectorID, workflowIDs, version)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchCreateConnectorWorkflowVersion indicates an expected call of BatchCreateConnectorWorkflowVersion.
func (mr *MockRepositoryMockRecorder) BatchCreateConnectorWorkflowVersion(ctx, appID, connectorID, workflowIDs, version any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchCreateConnectorWorkflowVersion", reflect.TypeOf((*MockRepository)(nil).BatchCreateConnectorWorkflowVersion), ctx, appID, connectorID, workflowIDs, version)
}

// CancelAllRunningNodes mocks base method.
func (m *MockRepository) CancelAllRunningNodes(ctx context.Context, wfExeID int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CancelAllRunningNodes", ctx, wfExeID)
	ret0, _ := ret[0].(error)
	return ret0
}

// CancelAllRunningNodes indicates an expected call of CancelAllRunningNodes.
func (mr *MockRepositoryMockRecorder) CancelAllRunningNodes(ctx, wfExeID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CancelAllRunningNodes", reflect.TypeOf((*MockRepository)(nil).CancelAllRunningNodes), ctx, wfExeID)
}

// CopyWorkflow mocks base method.
func (m *MockRepository) CopyWorkflow(ctx context.Context, workflowID int64, policy vo.CopyWorkflowPolicy) (*entity.Workflow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CopyWorkflow", ctx, workflowID, policy)
	ret0, _ := ret[0].(*entity.Workflow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CopyWorkflow indicates an expected call of CopyWorkflow.
func (mr *MockRepositoryMockRecorder) CopyWorkflow(ctx, workflowID, policy any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CopyWorkflow", reflect.TypeOf((*MockRepository)(nil).CopyWorkflow), ctx, workflowID, policy)
}

// CreateMeta mocks base method.
func (m *MockRepository) CreateMeta(ctx context.Context, meta *vo.Meta) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateMeta", ctx, meta)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateMeta indicates an expected call of CreateMeta.
func (mr *MockRepositoryMockRecorder) CreateMeta(ctx, meta any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateMeta", reflect.TypeOf((*MockRepository)(nil).CreateMeta), ctx, meta)
}

// CreateNodeExecution mocks base method.
func (m *MockRepository) CreateNodeExecution(ctx context.Context, execution *entity.NodeExecution) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateNodeExecution", ctx, execution)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateNodeExecution indicates an expected call of CreateNodeExecution.
func (mr *MockRepositoryMockRecorder) CreateNodeExecution(ctx, execution any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateNodeExecution", reflect.TypeOf((*MockRepository)(nil).CreateNodeExecution), ctx, execution)
}

// CreateOrUpdateDraft mocks base method.
func (m *MockRepository) CreateOrUpdateDraft(ctx context.Context, id int64, draft *vo.DraftInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateOrUpdateDraft", ctx, id, draft)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateOrUpdateDraft indicates an expected call of CreateOrUpdateDraft.
func (mr *MockRepositoryMockRecorder) CreateOrUpdateDraft(ctx, id, draft any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateOrUpdateDraft", reflect.TypeOf((*MockRepository)(nil).CreateOrUpdateDraft), ctx, id, draft)
}

// CreateSnapshotIfNeeded mocks base method.
func (m *MockRepository) CreateSnapshotIfNeeded(ctx context.Context, id int64, commitID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateSnapshotIfNeeded", ctx, id, commitID)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateSnapshotIfNeeded indicates an expected call of CreateSnapshotIfNeeded.
func (mr *MockRepositoryMockRecorder) CreateSnapshotIfNeeded(ctx, id, commitID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateSnapshotIfNeeded", reflect.TypeOf((*MockRepository)(nil).CreateSnapshotIfNeeded), ctx, id, commitID)
}

// CreateVersion mocks base method.
func (m *MockRepository) CreateVersion(ctx context.Context, id int64, info *vo.VersionInfo, newRefs map[entity.WorkflowReferenceKey]struct{}) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateVersion", ctx, id, info, newRefs)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateVersion indicates an expected call of CreateVersion.
func (mr *MockRepositoryMockRecorder) CreateVersion(ctx, id, info, newRefs any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateVersion", reflect.TypeOf((*MockRepository)(nil).CreateVersion), ctx, id, info, newRefs)
}

// CreateWorkflowExecution mocks base method.
func (m *MockRepository) CreateWorkflowExecution(ctx context.Context, execution *entity.WorkflowExecution) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateWorkflowExecution", ctx, execution)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateWorkflowExecution indicates an expected call of CreateWorkflowExecution.
func (mr *MockRepositoryMockRecorder) CreateWorkflowExecution(ctx, execution any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateWorkflowExecution", reflect.TypeOf((*MockRepository)(nil).CreateWorkflowExecution), ctx, execution)
}

// Delete mocks base method.
func (m *MockRepository) Delete(ctx context.Context, id int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", ctx, id)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockRepositoryMockRecorder) Delete(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockRepository)(nil).Delete), ctx, id)
}

// DraftV2 mocks base method.
func (m *MockRepository) DraftV2(ctx context.Context, id int64, commitID string) (*vo.DraftInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DraftV2", ctx, id, commitID)
	ret0, _ := ret[0].(*vo.DraftInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DraftV2 indicates an expected call of DraftV2.
func (mr *MockRepositoryMockRecorder) DraftV2(ctx, id, commitID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DraftV2", reflect.TypeOf((*MockRepository)(nil).DraftV2), ctx, id, commitID)
}

// GenID mocks base method.
func (m *MockRepository) GenID(ctx context.Context) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenID", ctx)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenID indicates an expected call of GenID.
func (mr *MockRepositoryMockRecorder) GenID(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenID", reflect.TypeOf((*MockRepository)(nil).GenID), ctx)
}

// GenMultiIDs mocks base method.
func (m *MockRepository) GenMultiIDs(ctx context.Context, counts int) ([]int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenMultiIDs", ctx, counts)
	ret0, _ := ret[0].([]int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenMultiIDs indicates an expected call of GenMultiIDs.
func (mr *MockRepositoryMockRecorder) GenMultiIDs(ctx, counts any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenMultiIDs", reflect.TypeOf((*MockRepository)(nil).GenMultiIDs), ctx, counts)
}

// Get mocks base method.
func (m *MockRepository) Get(ctx context.Context, checkPointID string) ([]byte, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get", ctx, checkPointID)
	ret0, _ := ret[0].([]byte)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// Get indicates an expected call of Get.
func (mr *MockRepositoryMockRecorder) Get(ctx, checkPointID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockRepository)(nil).Get), ctx, checkPointID)
}

// GetDraftWorkflowsByAppID mocks base method.
func (m *MockRepository) GetDraftWorkflowsByAppID(ctx context.Context, AppID int64) (map[int64]*vo.DraftInfo, map[int64]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDraftWorkflowsByAppID", ctx, AppID)
	ret0, _ := ret[0].(map[int64]*vo.DraftInfo)
	ret1, _ := ret[1].(map[int64]string)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetDraftWorkflowsByAppID indicates an expected call of GetDraftWorkflowsByAppID.
func (mr *MockRepositoryMockRecorder) GetDraftWorkflowsByAppID(ctx, AppID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDraftWorkflowsByAppID", reflect.TypeOf((*MockRepository)(nil).GetDraftWorkflowsByAppID), ctx, AppID)
}

// GetEntity mocks base method.
func (m *MockRepository) GetEntity(ctx context.Context, policy *vo.GetPolicy) (*entity.Workflow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEntity", ctx, policy)
	ret0, _ := ret[0].(*entity.Workflow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEntity indicates an expected call of GetEntity.
func (mr *MockRepositoryMockRecorder) GetEntity(ctx, policy any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEntity", reflect.TypeOf((*MockRepository)(nil).GetEntity), ctx, policy)
}

// GetFirstInterruptEvent mocks base method.
func (m *MockRepository) GetFirstInterruptEvent(ctx context.Context, wfExeID int64) (*entity.InterruptEvent, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFirstInterruptEvent", ctx, wfExeID)
	ret0, _ := ret[0].(*entity.InterruptEvent)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetFirstInterruptEvent indicates an expected call of GetFirstInterruptEvent.
func (mr *MockRepositoryMockRecorder) GetFirstInterruptEvent(ctx, wfExeID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFirstInterruptEvent", reflect.TypeOf((*MockRepository)(nil).GetFirstInterruptEvent), ctx, wfExeID)
}

// GetKnowledgeRecallChatModel mocks base method.
func (m *MockRepository) GetKnowledgeRecallChatModel() model.BaseChatModel {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetKnowledgeRecallChatModel")
	ret0, _ := ret[0].(model.BaseChatModel)
	return ret0
}

// GetKnowledgeRecallChatModel indicates an expected call of GetKnowledgeRecallChatModel.
func (mr *MockRepositoryMockRecorder) GetKnowledgeRecallChatModel() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetKnowledgeRecallChatModel", reflect.TypeOf((*MockRepository)(nil).GetKnowledgeRecallChatModel))
}

// GetLatestVersion mocks base method.
func (m *MockRepository) GetLatestVersion(ctx context.Context, id int64) (*vo.VersionInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLatestVersion", ctx, id)
	ret0, _ := ret[0].(*vo.VersionInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLatestVersion indicates an expected call of GetLatestVersion.
func (mr *MockRepositoryMockRecorder) GetLatestVersion(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLatestVersion", reflect.TypeOf((*MockRepository)(nil).GetLatestVersion), ctx, id)
}

// GetMeta mocks base method.
func (m *MockRepository) GetMeta(ctx context.Context, id int64) (*vo.Meta, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMeta", ctx, id)
	ret0, _ := ret[0].(*vo.Meta)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMeta indicates an expected call of GetMeta.
func (mr *MockRepositoryMockRecorder) GetMeta(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMeta", reflect.TypeOf((*MockRepository)(nil).GetMeta), ctx, id)
}

// GetNodeDebugLatestExeID mocks base method.
func (m *MockRepository) GetNodeDebugLatestExeID(ctx context.Context, wfID int64, nodeID string, uID int64) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNodeDebugLatestExeID", ctx, wfID, nodeID, uID)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNodeDebugLatestExeID indicates an expected call of GetNodeDebugLatestExeID.
func (mr *MockRepositoryMockRecorder) GetNodeDebugLatestExeID(ctx, wfID, nodeID, uID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNodeDebugLatestExeID", reflect.TypeOf((*MockRepository)(nil).GetNodeDebugLatestExeID), ctx, wfID, nodeID, uID)
}

// GetNodeExecution mocks base method.
func (m *MockRepository) GetNodeExecution(ctx context.Context, wfExeID int64, nodeID string) (*entity.NodeExecution, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNodeExecution", ctx, wfExeID, nodeID)
	ret0, _ := ret[0].(*entity.NodeExecution)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetNodeExecution indicates an expected call of GetNodeExecution.
func (mr *MockRepositoryMockRecorder) GetNodeExecution(ctx, wfExeID, nodeID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNodeExecution", reflect.TypeOf((*MockRepository)(nil).GetNodeExecution), ctx, wfExeID, nodeID)
}

// GetNodeExecutionByParent mocks base method.
func (m *MockRepository) GetNodeExecutionByParent(ctx context.Context, wfExeID int64, parentNodeID string) ([]*entity.NodeExecution, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNodeExecutionByParent", ctx, wfExeID, parentNodeID)
	ret0, _ := ret[0].([]*entity.NodeExecution)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNodeExecutionByParent indicates an expected call of GetNodeExecutionByParent.
func (mr *MockRepositoryMockRecorder) GetNodeExecutionByParent(ctx, wfExeID, parentNodeID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNodeExecutionByParent", reflect.TypeOf((*MockRepository)(nil).GetNodeExecutionByParent), ctx, wfExeID, parentNodeID)
}

// GetNodeExecutionsByWfExeID mocks base method.
func (m *MockRepository) GetNodeExecutionsByWfExeID(ctx context.Context, wfExeID int64) ([]*entity.NodeExecution, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNodeExecutionsByWfExeID", ctx, wfExeID)
	ret0, _ := ret[0].([]*entity.NodeExecution)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNodeExecutionsByWfExeID indicates an expected call of GetNodeExecutionsByWfExeID.
func (mr *MockRepositoryMockRecorder) GetNodeExecutionsByWfExeID(ctx, wfExeID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNodeExecutionsByWfExeID", reflect.TypeOf((*MockRepository)(nil).GetNodeExecutionsByWfExeID), ctx, wfExeID)
}

// GetTestRunLatestExeID mocks base method.
func (m *MockRepository) GetTestRunLatestExeID(ctx context.Context, wfID, uID int64) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTestRunLatestExeID", ctx, wfID, uID)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTestRunLatestExeID indicates an expected call of GetTestRunLatestExeID.
func (mr *MockRepositoryMockRecorder) GetTestRunLatestExeID(ctx, wfID, uID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTestRunLatestExeID", reflect.TypeOf((*MockRepository)(nil).GetTestRunLatestExeID), ctx, wfID, uID)
}

// GetVersion mocks base method.
func (m *MockRepository) GetVersion(ctx context.Context, id int64, version string) (*vo.VersionInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetVersion", ctx, id, version)
	ret0, _ := ret[0].(*vo.VersionInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVersion indicates an expected call of GetVersion.
func (mr *MockRepositoryMockRecorder) GetVersion(ctx, id, version any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVersion", reflect.TypeOf((*MockRepository)(nil).GetVersion), ctx, id, version)
}

// GetWorkflowCancelFlag mocks base method.
func (m *MockRepository) GetWorkflowCancelFlag(ctx context.Context, wfExeID int64) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWorkflowCancelFlag", ctx, wfExeID)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWorkflowCancelFlag indicates an expected call of GetWorkflowCancelFlag.
func (mr *MockRepositoryMockRecorder) GetWorkflowCancelFlag(ctx, wfExeID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWorkflowCancelFlag", reflect.TypeOf((*MockRepository)(nil).GetWorkflowCancelFlag), ctx, wfExeID)
}

// GetWorkflowExecution mocks base method.
func (m *MockRepository) GetWorkflowExecution(ctx context.Context, id int64) (*entity.WorkflowExecution, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWorkflowExecution", ctx, id)
	ret0, _ := ret[0].(*entity.WorkflowExecution)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetWorkflowExecution indicates an expected call of GetWorkflowExecution.
func (mr *MockRepositoryMockRecorder) GetWorkflowExecution(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWorkflowExecution", reflect.TypeOf((*MockRepository)(nil).GetWorkflowExecution), ctx, id)
}

// IsApplicationConnectorWorkflowVersion mocks base method.
func (m *MockRepository) IsApplicationConnectorWorkflowVersion(ctx context.Context, connectorID, workflowID int64, version string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsApplicationConnectorWorkflowVersion", ctx, connectorID, workflowID, version)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsApplicationConnectorWorkflowVersion indicates an expected call of IsApplicationConnectorWorkflowVersion.
func (mr *MockRepositoryMockRecorder) IsApplicationConnectorWorkflowVersion(ctx, connectorID, workflowID, version any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsApplicationConnectorWorkflowVersion", reflect.TypeOf((*MockRepository)(nil).IsApplicationConnectorWorkflowVersion), ctx, connectorID, workflowID, version)
}

// ListInterruptEvents mocks base method.
func (m *MockRepository) ListInterruptEvents(ctx context.Context, wfExeID int64) ([]*entity.InterruptEvent, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListInterruptEvents", ctx, wfExeID)
	ret0, _ := ret[0].([]*entity.InterruptEvent)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListInterruptEvents indicates an expected call of ListInterruptEvents.
func (mr *MockRepositoryMockRecorder) ListInterruptEvents(ctx, wfExeID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListInterruptEvents", reflect.TypeOf((*MockRepository)(nil).ListInterruptEvents), ctx, wfExeID)
}

// MDelete mocks base method.
func (m *MockRepository) MDelete(ctx context.Context, ids []int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MDelete", ctx, ids)
	ret0, _ := ret[0].(error)
	return ret0
}

// MDelete indicates an expected call of MDelete.
func (mr *MockRepositoryMockRecorder) MDelete(ctx, ids any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MDelete", reflect.TypeOf((*MockRepository)(nil).MDelete), ctx, ids)
}

// MGetDrafts mocks base method.
func (m *MockRepository) MGetDrafts(ctx context.Context, policy *vo.MGetPolicy) ([]*entity.Workflow, int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MGetDrafts", ctx, policy)
	ret0, _ := ret[0].([]*entity.Workflow)
	ret1, _ := ret[1].(int64)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// MGetDrafts indicates an expected call of MGetDrafts.
func (mr *MockRepositoryMockRecorder) MGetDrafts(ctx, policy any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MGetDrafts", reflect.TypeOf((*MockRepository)(nil).MGetDrafts), ctx, policy)
}

// MGetLatestVersion mocks base method.
func (m *MockRepository) MGetLatestVersion(ctx context.Context, policy *vo.MGetPolicy) ([]*entity.Workflow, int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MGetLatestVersion", ctx, policy)
	ret0, _ := ret[0].([]*entity.Workflow)
	ret1, _ := ret[1].(int64)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// MGetLatestVersion indicates an expected call of MGetLatestVersion.
func (mr *MockRepositoryMockRecorder) MGetLatestVersion(ctx, policy any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MGetLatestVersion", reflect.TypeOf((*MockRepository)(nil).MGetLatestVersion), ctx, policy)
}

// MGetMetas mocks base method.
func (m *MockRepository) MGetMetas(ctx context.Context, query *vo.MetaQuery) (map[int64]*vo.Meta, int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MGetMetas", ctx, query)
	ret0, _ := ret[0].(map[int64]*vo.Meta)
	ret1, _ := ret[1].(int64)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// MGetMetas indicates an expected call of MGetMetas.
func (mr *MockRepositoryMockRecorder) MGetMetas(ctx, query any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MGetMetas", reflect.TypeOf((*MockRepository)(nil).MGetMetas), ctx, query)
}

// MGetReferences mocks base method.
func (m *MockRepository) MGetReferences(ctx context.Context, policy *vo.MGetReferencePolicy) ([]*entity.WorkflowReference, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MGetReferences", ctx, policy)
	ret0, _ := ret[0].([]*entity.WorkflowReference)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MGetReferences indicates an expected call of MGetReferences.
func (mr *MockRepositoryMockRecorder) MGetReferences(ctx, policy any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MGetReferences", reflect.TypeOf((*MockRepository)(nil).MGetReferences), ctx, policy)
}

// PopFirstInterruptEvent mocks base method.
func (m *MockRepository) PopFirstInterruptEvent(ctx context.Context, wfExeID int64) (*entity.InterruptEvent, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PopFirstInterruptEvent", ctx, wfExeID)
	ret0, _ := ret[0].(*entity.InterruptEvent)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// PopFirstInterruptEvent indicates an expected call of PopFirstInterruptEvent.
func (mr *MockRepositoryMockRecorder) PopFirstInterruptEvent(ctx, wfExeID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PopFirstInterruptEvent", reflect.TypeOf((*MockRepository)(nil).PopFirstInterruptEvent), ctx, wfExeID)
}

// SaveInterruptEvents mocks base method.
func (m *MockRepository) SaveInterruptEvents(ctx context.Context, wfExeID int64, events []*entity.InterruptEvent) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveInterruptEvents", ctx, wfExeID, events)
	ret0, _ := ret[0].(error)
	return ret0
}

// SaveInterruptEvents indicates an expected call of SaveInterruptEvents.
func (mr *MockRepositoryMockRecorder) SaveInterruptEvents(ctx, wfExeID, events any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveInterruptEvents", reflect.TypeOf((*MockRepository)(nil).SaveInterruptEvents), ctx, wfExeID, events)
}

// Set mocks base method.
func (m *MockRepository) Set(ctx context.Context, checkPointID string, checkPoint []byte) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Set", ctx, checkPointID, checkPoint)
	ret0, _ := ret[0].(error)
	return ret0
}

// Set indicates an expected call of Set.
func (mr *MockRepositoryMockRecorder) Set(ctx, checkPointID, checkPoint any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Set", reflect.TypeOf((*MockRepository)(nil).Set), ctx, checkPointID, checkPoint)
}

// SetNodeDebugLatestExeID mocks base method.
func (m *MockRepository) SetNodeDebugLatestExeID(ctx context.Context, wfID int64, nodeID string, uID, exeID int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetNodeDebugLatestExeID", ctx, wfID, nodeID, uID, exeID)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetNodeDebugLatestExeID indicates an expected call of SetNodeDebugLatestExeID.
func (mr *MockRepositoryMockRecorder) SetNodeDebugLatestExeID(ctx, wfID, nodeID, uID, exeID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetNodeDebugLatestExeID", reflect.TypeOf((*MockRepository)(nil).SetNodeDebugLatestExeID), ctx, wfID, nodeID, uID, exeID)
}

// SetTestRunLatestExeID mocks base method.
func (m *MockRepository) SetTestRunLatestExeID(ctx context.Context, wfID, uID, exeID int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetTestRunLatestExeID", ctx, wfID, uID, exeID)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetTestRunLatestExeID indicates an expected call of SetTestRunLatestExeID.
func (mr *MockRepositoryMockRecorder) SetTestRunLatestExeID(ctx, wfID, uID, exeID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetTestRunLatestExeID", reflect.TypeOf((*MockRepository)(nil).SetTestRunLatestExeID), ctx, wfID, uID, exeID)
}

// SetWorkflowCancelFlag mocks base method.
func (m *MockRepository) SetWorkflowCancelFlag(ctx context.Context, wfExeID int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetWorkflowCancelFlag", ctx, wfExeID)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetWorkflowCancelFlag indicates an expected call of SetWorkflowCancelFlag.
func (mr *MockRepositoryMockRecorder) SetWorkflowCancelFlag(ctx, wfExeID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetWorkflowCancelFlag", reflect.TypeOf((*MockRepository)(nil).SetWorkflowCancelFlag), ctx, wfExeID)
}

// TryLockWorkflowExecution mocks base method.
func (m *MockRepository) TryLockWorkflowExecution(ctx context.Context, wfExeID, resumingEventID int64) (bool, entity.WorkflowExecuteStatus, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TryLockWorkflowExecution", ctx, wfExeID, resumingEventID)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(entity.WorkflowExecuteStatus)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// TryLockWorkflowExecution indicates an expected call of TryLockWorkflowExecution.
func (mr *MockRepositoryMockRecorder) TryLockWorkflowExecution(ctx, wfExeID, resumingEventID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TryLockWorkflowExecution", reflect.TypeOf((*MockRepository)(nil).TryLockWorkflowExecution), ctx, wfExeID, resumingEventID)
}

// UpdateFirstInterruptEvent mocks base method.
func (m *MockRepository) UpdateFirstInterruptEvent(ctx context.Context, wfExeID int64, event *entity.InterruptEvent) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateFirstInterruptEvent", ctx, wfExeID, event)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateFirstInterruptEvent indicates an expected call of UpdateFirstInterruptEvent.
func (mr *MockRepositoryMockRecorder) UpdateFirstInterruptEvent(ctx, wfExeID, event any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateFirstInterruptEvent", reflect.TypeOf((*MockRepository)(nil).UpdateFirstInterruptEvent), ctx, wfExeID, event)
}

// UpdateMeta mocks base method.
func (m *MockRepository) UpdateMeta(ctx context.Context, id int64, metaUpdate *vo.MetaUpdate) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateMeta", ctx, id, metaUpdate)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateMeta indicates an expected call of UpdateMeta.
func (mr *MockRepositoryMockRecorder) UpdateMeta(ctx, id, metaUpdate any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateMeta", reflect.TypeOf((*MockRepository)(nil).UpdateMeta), ctx, id, metaUpdate)
}

// UpdateNodeExecution mocks base method.
func (m *MockRepository) UpdateNodeExecution(ctx context.Context, execution *entity.NodeExecution) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateNodeExecution", ctx, execution)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateNodeExecution indicates an expected call of UpdateNodeExecution.
func (mr *MockRepositoryMockRecorder) UpdateNodeExecution(ctx, execution any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateNodeExecution", reflect.TypeOf((*MockRepository)(nil).UpdateNodeExecution), ctx, execution)
}

// UpdateNodeExecutionStreaming mocks base method.
func (m *MockRepository) UpdateNodeExecutionStreaming(ctx context.Context, execution *entity.NodeExecution) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateNodeExecutionStreaming", ctx, execution)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateNodeExecutionStreaming indicates an expected call of UpdateNodeExecutionStreaming.
func (mr *MockRepositoryMockRecorder) UpdateNodeExecutionStreaming(ctx, execution any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateNodeExecutionStreaming", reflect.TypeOf((*MockRepository)(nil).UpdateNodeExecutionStreaming), ctx, execution)
}

// UpdateWorkflowDraftTestRunSuccess mocks base method.
func (m *MockRepository) UpdateWorkflowDraftTestRunSuccess(ctx context.Context, id int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateWorkflowDraftTestRunSuccess", ctx, id)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateWorkflowDraftTestRunSuccess indicates an expected call of UpdateWorkflowDraftTestRunSuccess.
func (mr *MockRepositoryMockRecorder) UpdateWorkflowDraftTestRunSuccess(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateWorkflowDraftTestRunSuccess", reflect.TypeOf((*MockRepository)(nil).UpdateWorkflowDraftTestRunSuccess), ctx, id)
}

// UpdateWorkflowExecution mocks base method.
func (m *MockRepository) UpdateWorkflowExecution(ctx context.Context, execution *entity.WorkflowExecution, allowedStatus []entity.WorkflowExecuteStatus) (int64, entity.WorkflowExecuteStatus, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateWorkflowExecution", ctx, execution, allowedStatus)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(entity.WorkflowExecuteStatus)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// UpdateWorkflowExecution indicates an expected call of UpdateWorkflowExecution.
func (mr *MockRepositoryMockRecorder) UpdateWorkflowExecution(ctx, execution, allowedStatus any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateWorkflowExecution", reflect.TypeOf((*MockRepository)(nil).UpdateWorkflowExecution), ctx, execution, allowedStatus)
}

// WorkflowAsTool mocks base method.
func (m *MockRepository) WorkflowAsTool(ctx context.Context, policy vo.GetPolicy, wfToolConfig vo.WorkflowToolConfig) (workflow0.ToolFromWorkflow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WorkflowAsTool", ctx, policy, wfToolConfig)
	ret0, _ := ret[0].(workflow0.ToolFromWorkflow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// WorkflowAsTool indicates an expected call of WorkflowAsTool.
func (mr *MockRepositoryMockRecorder) WorkflowAsTool(ctx, policy, wfToolConfig any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WorkflowAsTool", reflect.TypeOf((*MockRepository)(nil).WorkflowAsTool), ctx, policy, wfToolConfig)
}
