from flask import Flask
import logging

from py_handler.module.generate_word.routes import generate_app
from py_handler.module.analyze.routes import analyze_app
from py_handler.config import config

def create_app(config_name='default'):
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    app = Flask("generate-tools")
    app.config.from_object(config[config_name])
    
    # 生成论文文件
    app.register_blueprint(generate_app, url_prefix="/generate")

    # 解析PDF
    app.register_blueprint(analyze_app, url_prefix="/analyze")
    
    return app