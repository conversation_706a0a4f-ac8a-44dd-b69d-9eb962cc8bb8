{"name": "streamx", "version": "2.20.1", "description": "An iteration of the Node.js core streams with a series of improvements", "main": "index.js", "dependencies": {"fast-fifo": "^1.3.2", "queue-tick": "^1.0.1", "text-decoder": "^1.1.0"}, "devDependencies": {"b4a": "^1.6.6", "brittle": "^3.1.1", "end-of-stream": "^1.4.4", "standard": "^17.0.0"}, "optionalDependencies": {"bare-events": "^2.2.0"}, "files": ["index.js"], "imports": {"events": {"bare": "bare-events", "default": "events"}}, "scripts": {"test": "standard && brittle test/*.js"}, "repository": {"type": "git", "url": "https://github.com/mafintosh/streamx.git"}, "author": "<PERSON> (@mafintosh)", "license": "MIT", "bugs": {"url": "https://github.com/mafintosh/streamx/issues"}, "homepage": "https://github.com/mafintosh/streamx"}