// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameDataCopyTask = "data_copy_task"

// DataCopyTask data copy task record
type DataCopyTask struct {
	MasterTaskID  string `gorm:"column:master_task_id;comment:task id" json:"master_task_id"`                                       // task id
	OriginDataID  int64  `gorm:"column:origin_data_id;not null;comment:origin data id" json:"origin_data_id"`                       // origin data id
	TargetDataID  int64  `gorm:"column:target_data_id;not null;comment:target data id" json:"target_data_id"`                       // target data id
	OriginSpaceID int64  `gorm:"column:origin_space_id;not null;comment:origin space id" json:"origin_space_id"`                    // origin space id
	TargetSpaceID int64  `gorm:"column:target_space_id;not null;comment:target space id" json:"target_space_id"`                    // target space id
	OriginUserID  int64  `gorm:"column:origin_user_id;not null;comment:origin user id" json:"origin_user_id"`                       // origin user id
	TargetUserID  int64  `gorm:"column:target_user_id;comment:target user id" json:"target_user_id"`                                // target user id
	OriginAppID   int64  `gorm:"column:origin_app_id;not null;comment:origin app id" json:"origin_app_id"`                          // origin app id
	TargetAppID   int64  `gorm:"column:target_app_id;not null;comment:target app id" json:"target_app_id"`                          // target app id
	DataType      int32  `gorm:"column:data_type;not null;comment:data type 1:knowledge, 2:database" json:"data_type"`              // data type 1:knowledge, 2:database
	ExtInfo       string `gorm:"column:ext_info;not null;comment:ext" json:"ext_info"`                                              // ext
	StartTime     int64  `gorm:"column:start_time;comment:task start time" json:"start_time"`                                       // task start time
	FinishTime    int64  `gorm:"column:finish_time;comment:task finish time" json:"finish_time"`                                    // task finish time
	Status        int32  `gorm:"column:status;not null;default:1;comment:1: Create 2: Running 3: Success 4: Failure" json:"status"` // 1: Create 2: Running 3: Success 4: Failure
	ErrorMsg      string `gorm:"column:error_msg;comment:error msg" json:"error_msg"`                                               // error msg
	ID            int64  `gorm:"column:id;primaryKey;autoIncrement:true;comment:ID" json:"id"`                                      // ID
}

// TableName DataCopyTask's table name
func (*DataCopyTask) TableName() string {
	return TableNameDataCopyTask
}
