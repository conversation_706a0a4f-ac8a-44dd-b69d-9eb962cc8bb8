import io
import sys
from contextlib import redirect_stdout, redirect_stderr
from flask import jsonify, request, send_file
import matplotlib
matplotlib.use('Agg')  
import matplotlib.pyplot as plt
from typing import Tuple, Optional
from .routes import generate_app
import networkx as nx
import concurrent.futures

def execute_python_plot(code: str, title: str = "") -> Tuple[bool, Optional[bytes], Optional[str]]:
    """
    执行Python代码并返回图片二进制数据
    """
    stdout = io.StringIO()
    stderr = io.StringIO()
    
    def execute_plot():
        try:
            # 清除之前的图形
            plt.clf()
            
            # 执行代码
            with redirect_stdout(stdout), redirect_stderr(stderr):
                exec(code)
                
            # 设置标题
            # plt.title("")
                
            # 将图片保存到内存中
            img_buffer = io.BytesIO()
            plt.savefig(img_buffer, format='png', dpi=300, bbox_inches='tight')       
            plt.close()
            img_buffer.seek(0)
            return img_buffer.getvalue()
        except Exception as e:
            plt.close()
            raise e
    
    try:
        # 使用线程池执行代码，设置超时时间为20秒
        with concurrent.futures.ThreadPoolExecutor() as executor:
            future = executor.submit(execute_plot)
            img_buffer = future.result(timeout=20)
            
        return True, img_buffer, None
        
    except concurrent.futures.TimeoutError:
        plt.close()
        return False, None, "代码执行超时（超过20秒）"
    except Exception as e:
        plt.close()
        error_msg = f"执行错误: {str(e)}\n"
        error_msg += f"标准错误: {stderr.getvalue()}"
        return False, None, error_msg
    finally:
        stdout.close()
        stderr.close()

@generate_app.route('/chart', methods=['POST'])
def generate_chart():
    # 获取请求中的数据
    data = request.json
    code = data.get('code', '')
    title = data.get('title', '')

    # 使用 with 语句确保资源正确关闭
    with io.StringIO() as stdout:
        with redirect_stdout(stdout):
            print("接收到的代码:", code)  
            print("接收到的标题:", title)  
            
            success, img_buffer, error = execute_python_plot(code, title)
            if success:
                print("图表生成成功")  
                # 将 bytes 转换为 BytesIO 对象
                img_io = io.BytesIO(img_buffer)
                return send_file(img_io, mimetype='image/png', as_attachment=True, download_name=f'{title}.png')
            else:
                print("图表生成失败:", error)  
                return jsonify({'error': error})
    

# 使用示例
# if __name__ == "__main__":
#     # 测试代码
#     test_code = """
# import matplotlib.pyplot as plt\nimport networkx as nx\n\nplt.rcParams['font.sans-serif'] = ['SimHei']\nplt.rcParams['axes.unicode_minus'] = False\n\n# 创建一个空的无向图\nG = nx.Graph()\n\n# 定义节点\nG.add_nodes_from([\n    ('A', {'label': '视觉刺激'}),\n    ('B', {'label': '听觉刺激'}),\n    ('C', {'label': '触觉刺激'}),\n    ('D', {'label': '嗅觉刺激'}),\n    ('E', {'label': '多感官协同作用'})\n])\n\n# 定义边和权重，代表多感官之间的协同作用\nG.add_weighted_edges_from([\n    ('A', 'E', 0.6),\n    ('B', 'E', 0.7),\n    ('C', 'E', 0.5),\n    ('D', 'E', 0.4)\n])\n\n# 提取每个节点的标签\nlabels = nx.get_node_attributes(G, 'label')\n\n# 定义布局\npos = nx.spring_layout(G)\n\n# 画节点\nnx.draw_networkx_nodes(G, pos, node_size=7000, node_color='lightblue')\n\n# 画边\nnx.draw_networkx_edges(G, pos, width=2, edge_color='gray')\n\n# 画节点标签\nnx.draw_networkx_labels(G, pos, labels, font_size=12)\n
#     """
    
#     success, img_data, error = execute_python_plot(test_code, "展示多感官促醒理论的概念图")
#     if success:
#         # 测试：将二进制数据保存为文件
#         with open("test_plot.png", "wb") as f:
#             f.write(img_data)
#         print("图片数据已生成")
#     else:
#         print(f"发生错误：{error}")