# ===========================================
# 服务隔离架构 - 每个服务独立的基础设施
# ===========================================

# Paper Editor PostgreSQL
apiVersion: apps/v1
kind: Deployment
metadata:
  name: paper-editor-postgres
  namespace: paper-services
  labels:
    app: paper-editor-postgres
    service: paper-editor-api
spec:
  replicas: 1
  selector:
    matchLabels:
      app: paper-editor-postgres
  template:
    metadata:
      labels:
        app: paper-editor-postgres
    spec:
      containers:
      - name: postgres
        image: postgres:15-alpine
        ports:
        - containerPort: 5432
        env:
        - name: POSTGRES_USER
          value: "postgres"
        - name: POSTGRES_PASSWORD
          value: "paper_editor_pass"
        - name: POSTGRES_DB
          value: "paper_editor"
        - name: PGDATA
          value: "/var/lib/postgresql/data/pgdata"
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
      volumes:
      - name: postgres-storage
        persistentVolumeClaim:
          claimName: paper-editor-postgres-pvc
---
# Paper Editor Redis
apiVersion: apps/v1
kind: Deployment
metadata:
  name: paper-editor-redis
  namespace: paper-services
  labels:
    app: paper-editor-redis
    service: paper-editor-api
spec:
  replicas: 1
  selector:
    matchLabels:
      app: paper-editor-redis
  template:
    metadata:
      labels:
        app: paper-editor-redis
    spec:
      containers:
      - name: redis
        image: redis:6-alpine
        ports:
        - containerPort: 6379
        env:
        - name: REDISCLI_AUTH
          value: "paper_editor_redis"
        command: ["redis-server", "--requirepass", "paper_editor_redis"]
        volumeMounts:
        - name: redis-storage
          mountPath: /data
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
      volumes:
      - name: redis-storage
        persistentVolumeClaim:
          claimName: paper-editor-redis-pvc
---
# Dify PostgreSQL
apiVersion: apps/v1
kind: Deployment
metadata:
  name: dify-postgres
  namespace: paper-services
  labels:
    app: dify-postgres
    service: dify
spec:
  replicas: 1
  selector:
    matchLabels:
      app: dify-postgres
  template:
    metadata:
      labels:
        app: dify-postgres
    spec:
      containers:
      - name: postgres
        image: postgres:15-alpine
        ports:
        - containerPort: 5432
        env:
        - name: POSTGRES_USER
          value: "postgres"
        - name: POSTGRES_PASSWORD
          value: "difyai123456"
        - name: POSTGRES_DB
          value: "dify"
        - name: PGDATA
          value: "/var/lib/postgresql/data/pgdata"
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
      volumes:
      - name: postgres-storage
        persistentVolumeClaim:
          claimName: dify-postgres-pvc
---
# Dify Redis
apiVersion: apps/v1
kind: Deployment
metadata:
  name: dify-redis
  namespace: paper-services
  labels:
    app: dify-redis
    service: dify
spec:
  replicas: 1
  selector:
    matchLabels:
      app: dify-redis
  template:
    metadata:
      labels:
        app: dify-redis
    spec:
      containers:
      - name: redis
        image: redis:6-alpine
        ports:
        - containerPort: 6379
        env:
        - name: REDISCLI_AUTH
          value: "difyai123456"
        command: ["redis-server", "--requirepass", "difyai123456"]
        volumeMounts:
        - name: redis-storage
          mountPath: /data
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "200m"
      volumes:
      - name: redis-storage
        persistentVolumeClaim:
          claimName: dify-redis-pvc
---
# ===========================================
# Services 服务配置
# ===========================================

# Paper Editor PostgreSQL Service
apiVersion: v1
kind: Service
metadata:
  name: paper-editor-postgres
  namespace: paper-services
  labels:
    app: paper-editor-postgres
spec:
  selector:
    app: paper-editor-postgres
  ports:
    - protocol: TCP
      port: 5432
      targetPort: 5432
  type: ClusterIP
---
# Paper Editor Redis Service
apiVersion: v1
kind: Service
metadata:
  name: paper-editor-redis
  namespace: paper-services
  labels:
    app: paper-editor-redis
spec:
  selector:
    app: paper-editor-redis
  ports:
    - protocol: TCP
      port: 6379
      targetPort: 6379
  type: ClusterIP
---
# Dify PostgreSQL Service
apiVersion: v1
kind: Service
metadata:
  name: dify-postgres
  namespace: paper-services
  labels:
    app: dify-postgres
spec:
  selector:
    app: dify-postgres
  ports:
    - protocol: TCP
      port: 5432
      targetPort: 5432
  type: ClusterIP
---
# Dify Redis Service
apiVersion: v1
kind: Service
metadata:
  name: dify-redis
  namespace: paper-services
  labels:
    app: dify-redis
spec:
  selector:
    app: dify-redis
  ports:
    - protocol: TCP
      port: 6379
      targetPort: 6379
  type: ClusterIP
