// Code generated by thriftgo (0.4.1). DO NOT EDIT.

package testcase

import (
	"fmt"
	"github.com/apache/thrift/lib/go/thrift"
	"github.com/coze-dev/coze-studio/backend/api/model/flow/devops/debugger/domain/infra"
)

type CaseDataBase struct {
	// Do not fill in when adding, fill in when updating
	CaseID      *int64  `thrift:"caseID,1,optional" json:"caseID,string" form:"caseID" query:"caseID"`
	Name        *string `thrift:"name,2,optional" form:"name" json:"name,omitempty" query:"name"`
	Description *string `thrift:"description,3,optional" form:"description" json:"description,omitempty" query:"description"`
	// Input information in JSON format
	Input     *string `thrift:"input,4,optional" form:"input" json:"input,omitempty" query:"input"`
	IsDefault *bool   `thrift:"isDefault,5,optional" form:"isDefault" json:"isDefault,omitempty" query:"isDefault"`
}

func NewCaseDataBase() *CaseDataBase {
	return &CaseDataBase{}
}

func (p *CaseDataBase) InitDefault() {
}

var CaseDataBase_CaseID_DEFAULT int64

func (p *CaseDataBase) GetCaseID() (v int64) {
	if !p.IsSetCaseID() {
		return CaseDataBase_CaseID_DEFAULT
	}
	return *p.CaseID
}

var CaseDataBase_Name_DEFAULT string

func (p *CaseDataBase) GetName() (v string) {
	if !p.IsSetName() {
		return CaseDataBase_Name_DEFAULT
	}
	return *p.Name
}

var CaseDataBase_Description_DEFAULT string

func (p *CaseDataBase) GetDescription() (v string) {
	if !p.IsSetDescription() {
		return CaseDataBase_Description_DEFAULT
	}
	return *p.Description
}

var CaseDataBase_Input_DEFAULT string

func (p *CaseDataBase) GetInput() (v string) {
	if !p.IsSetInput() {
		return CaseDataBase_Input_DEFAULT
	}
	return *p.Input
}

var CaseDataBase_IsDefault_DEFAULT bool

func (p *CaseDataBase) GetIsDefault() (v bool) {
	if !p.IsSetIsDefault() {
		return CaseDataBase_IsDefault_DEFAULT
	}
	return *p.IsDefault
}

var fieldIDToName_CaseDataBase = map[int16]string{
	1: "caseID",
	2: "name",
	3: "description",
	4: "input",
	5: "isDefault",
}

func (p *CaseDataBase) IsSetCaseID() bool {
	return p.CaseID != nil
}

func (p *CaseDataBase) IsSetName() bool {
	return p.Name != nil
}

func (p *CaseDataBase) IsSetDescription() bool {
	return p.Description != nil
}

func (p *CaseDataBase) IsSetInput() bool {
	return p.Input != nil
}

func (p *CaseDataBase) IsSetIsDefault() bool {
	return p.IsDefault != nil
}

func (p *CaseDataBase) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CaseDataBase[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *CaseDataBase) ReadField1(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.CaseID = _field
	return nil
}
func (p *CaseDataBase) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Name = _field
	return nil
}
func (p *CaseDataBase) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Description = _field
	return nil
}
func (p *CaseDataBase) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Input = _field
	return nil
}
func (p *CaseDataBase) ReadField5(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.IsDefault = _field
	return nil
}

func (p *CaseDataBase) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("CaseDataBase"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CaseDataBase) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetCaseID() {
		if err = oprot.WriteFieldBegin("caseID", thrift.I64, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.CaseID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *CaseDataBase) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetName() {
		if err = oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Name); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *CaseDataBase) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetDescription() {
		if err = oprot.WriteFieldBegin("description", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Description); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *CaseDataBase) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetInput() {
		if err = oprot.WriteFieldBegin("input", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Input); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *CaseDataBase) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetIsDefault() {
		if err = oprot.WriteFieldBegin("isDefault", thrift.BOOL, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.IsDefault); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *CaseDataBase) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CaseDataBase(%+v)", *p)

}

type CaseDataDetail struct {
	CaseBase        *CaseDataBase `thrift:"caseBase,1,optional" form:"caseBase" json:"caseBase,omitempty" query:"caseBase"`
	CreatorID       *string       `thrift:"creatorID,2,optional" form:"creatorID" json:"creatorID,omitempty" query:"creatorID"`
	CreateTimeInSec *int64        `thrift:"createTimeInSec,3,optional" form:"createTimeInSec" json:"createTimeInSec,omitempty" query:"createTimeInSec"`
	UpdateTimeInSec *int64        `thrift:"updateTimeInSec,4,optional" form:"updateTimeInSec" json:"updateTimeInSec,omitempty" query:"updateTimeInSec"`
	// Schema incompatibility
	SchemaIncompatible *bool          `thrift:"schemaIncompatible,5,optional" form:"schemaIncompatible" json:"schemaIncompatible,omitempty" query:"schemaIncompatible"`
	Updater            *infra.Creator `thrift:"updater,6,optional" form:"updater" json:"updater,omitempty" query:"updater"`
}

func NewCaseDataDetail() *CaseDataDetail {
	return &CaseDataDetail{}
}

func (p *CaseDataDetail) InitDefault() {
}

var CaseDataDetail_CaseBase_DEFAULT *CaseDataBase

func (p *CaseDataDetail) GetCaseBase() (v *CaseDataBase) {
	if !p.IsSetCaseBase() {
		return CaseDataDetail_CaseBase_DEFAULT
	}
	return p.CaseBase
}

var CaseDataDetail_CreatorID_DEFAULT string

func (p *CaseDataDetail) GetCreatorID() (v string) {
	if !p.IsSetCreatorID() {
		return CaseDataDetail_CreatorID_DEFAULT
	}
	return *p.CreatorID
}

var CaseDataDetail_CreateTimeInSec_DEFAULT int64

func (p *CaseDataDetail) GetCreateTimeInSec() (v int64) {
	if !p.IsSetCreateTimeInSec() {
		return CaseDataDetail_CreateTimeInSec_DEFAULT
	}
	return *p.CreateTimeInSec
}

var CaseDataDetail_UpdateTimeInSec_DEFAULT int64

func (p *CaseDataDetail) GetUpdateTimeInSec() (v int64) {
	if !p.IsSetUpdateTimeInSec() {
		return CaseDataDetail_UpdateTimeInSec_DEFAULT
	}
	return *p.UpdateTimeInSec
}

var CaseDataDetail_SchemaIncompatible_DEFAULT bool

func (p *CaseDataDetail) GetSchemaIncompatible() (v bool) {
	if !p.IsSetSchemaIncompatible() {
		return CaseDataDetail_SchemaIncompatible_DEFAULT
	}
	return *p.SchemaIncompatible
}

var CaseDataDetail_Updater_DEFAULT *infra.Creator

func (p *CaseDataDetail) GetUpdater() (v *infra.Creator) {
	if !p.IsSetUpdater() {
		return CaseDataDetail_Updater_DEFAULT
	}
	return p.Updater
}

var fieldIDToName_CaseDataDetail = map[int16]string{
	1: "caseBase",
	2: "creatorID",
	3: "createTimeInSec",
	4: "updateTimeInSec",
	5: "schemaIncompatible",
	6: "updater",
}

func (p *CaseDataDetail) IsSetCaseBase() bool {
	return p.CaseBase != nil
}

func (p *CaseDataDetail) IsSetCreatorID() bool {
	return p.CreatorID != nil
}

func (p *CaseDataDetail) IsSetCreateTimeInSec() bool {
	return p.CreateTimeInSec != nil
}

func (p *CaseDataDetail) IsSetUpdateTimeInSec() bool {
	return p.UpdateTimeInSec != nil
}

func (p *CaseDataDetail) IsSetSchemaIncompatible() bool {
	return p.SchemaIncompatible != nil
}

func (p *CaseDataDetail) IsSetUpdater() bool {
	return p.Updater != nil
}

func (p *CaseDataDetail) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CaseDataDetail[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *CaseDataDetail) ReadField1(iprot thrift.TProtocol) error {
	_field := NewCaseDataBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.CaseBase = _field
	return nil
}
func (p *CaseDataDetail) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.CreatorID = _field
	return nil
}
func (p *CaseDataDetail) ReadField3(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.CreateTimeInSec = _field
	return nil
}
func (p *CaseDataDetail) ReadField4(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.UpdateTimeInSec = _field
	return nil
}
func (p *CaseDataDetail) ReadField5(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.SchemaIncompatible = _field
	return nil
}
func (p *CaseDataDetail) ReadField6(iprot thrift.TProtocol) error {
	_field := infra.NewCreator()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Updater = _field
	return nil
}

func (p *CaseDataDetail) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("CaseDataDetail"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CaseDataDetail) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetCaseBase() {
		if err = oprot.WriteFieldBegin("caseBase", thrift.STRUCT, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.CaseBase.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *CaseDataDetail) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetCreatorID() {
		if err = oprot.WriteFieldBegin("creatorID", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.CreatorID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *CaseDataDetail) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetCreateTimeInSec() {
		if err = oprot.WriteFieldBegin("createTimeInSec", thrift.I64, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.CreateTimeInSec); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *CaseDataDetail) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetUpdateTimeInSec() {
		if err = oprot.WriteFieldBegin("updateTimeInSec", thrift.I64, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.UpdateTimeInSec); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *CaseDataDetail) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetSchemaIncompatible() {
		if err = oprot.WriteFieldBegin("schemaIncompatible", thrift.BOOL, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.SchemaIncompatible); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *CaseDataDetail) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetUpdater() {
		if err = oprot.WriteFieldBegin("updater", thrift.STRUCT, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Updater.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *CaseDataDetail) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CaseDataDetail(%+v)", *p)

}
