from flask import Blueprint, request, jsonify
from scholarly import scholarly
import datetime

app = Blueprint('googleScholar', __name__)

# Google Scholar 搜索接口
@app.route('/search', methods=['GET'])
def search_scholar():
    query = request.args.get('query', '')
    if not query:
        return jsonify({'error': 'Query parameter is missing'}), 400
    
    # 获取当前年份
    current_year = datetime.datetime.now().year
    # 设置搜索的起始年份（4年前）
    start_year = current_year - 3

    # 修改搜索查询，限制年份范围
    search_query = scholarly.search_pubs(query, year_low=start_year)
    results = []
    
    for result in search_query:
        # 提取并过滤结果
        pub_year = int(result['bib'].get('pub_year', '0'))
        if start_year <= pub_year <= current_year:
            paper = {
                'title': result['bib']['title'],
                'author': result['bib']['author'],
                'pub_year': pub_year,
                'abstract': result['bib'].get('abstract', '没有可用的摘要')
            }
            results.append(paper)
            if len(results) >= 5:
                break

    return jsonify(results)
