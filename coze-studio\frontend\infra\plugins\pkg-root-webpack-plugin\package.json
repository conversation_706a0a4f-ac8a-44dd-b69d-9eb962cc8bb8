{"name": "@coze-arch/pkg-root-webpack-plugin", "version": "1.0.0", "description": "> 用于支持 `@` 根目录引用的插件", "keywords": [], "license": "Apache-2.0", "author": "<EMAIL>", "main": "lib/index.js", "types": "lib/index.d.ts", "scripts": {"build": "tsc -b ./tsconfig.build.json --force", "dev": "tsc -w", "lint": "eslint ./ --cache", "test": "vitest --run --passWithNoTests", "test:cov": "npm run test -- --coverage"}, "dependencies": {"@coze-arch/pkg-root-webpack-plugin-origin": "npm:@coze-arch/pkg-root-webpack-plugin@1.0.0-alpha.48aa2e", "@rushstack/rush-sdk": "5.100.2"}, "devDependencies": {"@babel/core": "^7.20.2", "@coze-arch/eslint-config": "workspace:*", "@coze-arch/ts-config": "workspace:*", "@coze-arch/vitest-config": "workspace:*", "@types/micromatch": "^4.0.1", "@types/node": "^18", "@vitest/coverage-v8": "~3.0.5", "babel-loader": "~9.1.0", "enhanced-resolve": "~5.12.0", "pkg-install": "~1.0.0", "vitest": "~3.0.5", "webpack": "~5.89.0"}}