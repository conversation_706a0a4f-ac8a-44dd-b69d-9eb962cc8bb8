2025/08/04-13:39:34.279692 45 RocksDB version: 6.29.5
2025/08/04-13:39:34.280341 45 Git sha 0
2025/08/04-13:39:34.280350 45 Compile date 2024-11-15 11:22:58
2025/08/04-13:39:34.280357 45 DB SUMMARY
2025/08/04-13:39:34.280359 45 DB Session ID:  54R3F314YINJHB7F4TBP
2025/08/04-13:39:34.287013 45 CURRENT file:  CURRENT
2025/08/04-13:39:34.287024 45 IDENTITY file:  IDENTITY
2025/08/04-13:39:34.287803 45 MANIFEST file:  MANIFEST-000035 size: 172 Bytes
2025/08/04-13:39:34.287813 45 SST files in /var/lib/milvus/rdb_data_meta_kv dir, Total Num: 1, files: 000009.sst 
2025/08/04-13:39:34.287819 45 Write Ahead Log file in /var/lib/milvus/rdb_data_meta_kv: 000011.log size: 0 ; 000016.log size: 0 ; 000021.log size: 0 ; 000026.log size: 0 ; 000031.log size: 0 ; 000036.log size: 243224 ; 
2025/08/04-13:39:34.287823 45                         Options.error_if_exists: 0
2025/08/04-13:39:34.287824 45                       Options.create_if_missing: 1
2025/08/04-13:39:34.287825 45                         Options.paranoid_checks: 1
2025/08/04-13:39:34.287825 45             Options.flush_verify_memtable_count: 1
2025/08/04-13:39:34.287826 45                               Options.track_and_verify_wals_in_manifest: 0
2025/08/04-13:39:34.287826 45                                     Options.env: 0x7feb3fa7bd00
2025/08/04-13:39:34.287827 45                                      Options.fs: PosixFileSystem
2025/08/04-13:39:34.287828 45                                Options.info_log: 0x7fea63690050
2025/08/04-13:39:34.287829 45                Options.max_file_opening_threads: 16
2025/08/04-13:39:34.287829 45                              Options.statistics: (nil)
2025/08/04-13:39:34.287830 45                               Options.use_fsync: 0
2025/08/04-13:39:34.287831 45                       Options.max_log_file_size: 0
2025/08/04-13:39:34.287831 45                  Options.max_manifest_file_size: 1073741824
2025/08/04-13:39:34.287832 45                   Options.log_file_time_to_roll: 0
2025/08/04-13:39:34.287833 45                       Options.keep_log_file_num: 1000
2025/08/04-13:39:34.287833 45                    Options.recycle_log_file_num: 0
2025/08/04-13:39:34.287834 45                         Options.allow_fallocate: 1
2025/08/04-13:39:34.287834 45                        Options.allow_mmap_reads: 0
2025/08/04-13:39:34.287835 45                       Options.allow_mmap_writes: 0
2025/08/04-13:39:34.287836 45                        Options.use_direct_reads: 0
2025/08/04-13:39:34.287836 45                        Options.use_direct_io_for_flush_and_compaction: 0
2025/08/04-13:39:34.287837 45          Options.create_missing_column_families: 0
2025/08/04-13:39:34.287837 45                              Options.db_log_dir: 
2025/08/04-13:39:34.287838 45                                 Options.wal_dir: 
2025/08/04-13:39:34.287838 45                Options.table_cache_numshardbits: 6
2025/08/04-13:39:34.287839 45                         Options.WAL_ttl_seconds: 0
2025/08/04-13:39:34.287840 45                       Options.WAL_size_limit_MB: 0
2025/08/04-13:39:34.287840 45                        Options.max_write_batch_group_size_bytes: 1048576
2025/08/04-13:39:34.287841 45             Options.manifest_preallocation_size: 4194304
2025/08/04-13:39:34.287841 45                     Options.is_fd_close_on_exec: 1
2025/08/04-13:39:34.287842 45                   Options.advise_random_on_open: 1
2025/08/04-13:39:34.287842 45                   Options.experimental_mempurge_threshold: 0.000000
2025/08/04-13:39:34.287852 45                    Options.db_write_buffer_size: 0
2025/08/04-13:39:34.287852 45                    Options.write_buffer_manager: 0x7fea65a400a0
2025/08/04-13:39:34.287853 45         Options.access_hint_on_compaction_start: 1
2025/08/04-13:39:34.287854 45  Options.new_table_reader_for_compaction_inputs: 0
2025/08/04-13:39:34.287854 45           Options.random_access_max_buffer_size: 1048576
2025/08/04-13:39:34.287883 45                      Options.use_adaptive_mutex: 0
2025/08/04-13:39:34.287884 45                            Options.rate_limiter: (nil)
2025/08/04-13:39:34.287890 45     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/08/04-13:39:34.288214 45                       Options.wal_recovery_mode: 2
2025/08/04-13:39:34.288216 45                  Options.enable_thread_tracking: 0
2025/08/04-13:39:34.288217 45                  Options.enable_pipelined_write: 0
2025/08/04-13:39:34.288218 45                  Options.unordered_write: 0
2025/08/04-13:39:34.288218 45         Options.allow_concurrent_memtable_write: 1
2025/08/04-13:39:34.288219 45      Options.enable_write_thread_adaptive_yield: 1
2025/08/04-13:39:34.288219 45             Options.write_thread_max_yield_usec: 100
2025/08/04-13:39:34.288220 45            Options.write_thread_slow_yield_usec: 3
2025/08/04-13:39:34.288221 45                               Options.row_cache: None
2025/08/04-13:39:34.288221 45                              Options.wal_filter: None
2025/08/04-13:39:34.288222 45             Options.avoid_flush_during_recovery: 0
2025/08/04-13:39:34.288222 45             Options.allow_ingest_behind: 0
2025/08/04-13:39:34.288223 45             Options.preserve_deletes: 0
2025/08/04-13:39:34.288224 45             Options.two_write_queues: 0
2025/08/04-13:39:34.288224 45             Options.manual_wal_flush: 0
2025/08/04-13:39:34.288225 45             Options.atomic_flush: 0
2025/08/04-13:39:34.288225 45             Options.avoid_unnecessary_blocking_io: 0
2025/08/04-13:39:34.288226 45                 Options.persist_stats_to_disk: 0
2025/08/04-13:39:34.288226 45                 Options.write_dbid_to_manifest: 0
2025/08/04-13:39:34.288227 45                 Options.log_readahead_size: 0
2025/08/04-13:39:34.288228 45                 Options.file_checksum_gen_factory: Unknown
2025/08/04-13:39:34.288228 45                 Options.best_efforts_recovery: 0
2025/08/04-13:39:34.288229 45                Options.max_bgerror_resume_count: 2147483647
2025/08/04-13:39:34.288229 45            Options.bgerror_resume_retry_interval: 1000000
2025/08/04-13:39:34.288230 45             Options.allow_data_in_errors: 0
2025/08/04-13:39:34.288230 45             Options.db_host_id: __hostname__
2025/08/04-13:39:34.288234 45             Options.max_background_jobs: 2
2025/08/04-13:39:34.288235 45             Options.max_background_compactions: -1
2025/08/04-13:39:34.288235 45             Options.max_subcompactions: 1
2025/08/04-13:39:34.288236 45             Options.avoid_flush_during_shutdown: 0
2025/08/04-13:39:34.288236 45           Options.writable_file_max_buffer_size: 1048576
2025/08/04-13:39:34.288237 45             Options.delayed_write_rate : 16777216
2025/08/04-13:39:34.288238 45             Options.max_total_wal_size: 0
2025/08/04-13:39:34.288238 45             Options.delete_obsolete_files_period_micros: 21600000000
2025/08/04-13:39:34.288239 45                   Options.stats_dump_period_sec: 600
2025/08/04-13:39:34.288240 45                 Options.stats_persist_period_sec: 600
2025/08/04-13:39:34.288240 45                 Options.stats_history_buffer_size: 1048576
2025/08/04-13:39:34.288241 45                          Options.max_open_files: -1
2025/08/04-13:39:34.288241 45                          Options.bytes_per_sync: 0
2025/08/04-13:39:34.288242 45                      Options.wal_bytes_per_sync: 0
2025/08/04-13:39:34.288242 45                   Options.strict_bytes_per_sync: 0
2025/08/04-13:39:34.288243 45       Options.compaction_readahead_size: 0
2025/08/04-13:39:34.288244 45                  Options.max_background_flushes: 1
2025/08/04-13:39:34.288244 45 Compression algorithms supported:
2025/08/04-13:39:34.288246 45 	kZSTD supported: 1
2025/08/04-13:39:34.288247 45 	kXpressCompression supported: 0
2025/08/04-13:39:34.288248 45 	kBZip2Compression supported: 0
2025/08/04-13:39:34.288249 45 	kZSTDNotFinalCompression supported: 1
2025/08/04-13:39:34.288249 45 	kLZ4Compression supported: 0
2025/08/04-13:39:34.288250 45 	kZlibCompression supported: 0
2025/08/04-13:39:34.288251 45 	kLZ4HCCompression supported: 0
2025/08/04-13:39:34.288251 45 	kSnappyCompression supported: 0
2025/08/04-13:39:34.288256 45 Fast CRC32 supported: Not supported on x86
2025/08/04-13:39:34.294041 45 [db/version_set.cc:4888] Recovering from manifest file: /var/lib/milvus/rdb_data_meta_kv/MANIFEST-000035
2025/08/04-13:39:34.296028 45 [db/column_family.cc:605] --------------- Options for column family [default]:
2025/08/04-13:39:34.296036 45               Options.comparator: leveldb.BytewiseComparator
2025/08/04-13:39:34.296037 45           Options.merge_operator: None
2025/08/04-13:39:34.296039 45        Options.compaction_filter: None
2025/08/04-13:39:34.296039 45        Options.compaction_filter_factory: None
2025/08/04-13:39:34.296040 45  Options.sst_partitioner_factory: None
2025/08/04-13:39:34.296041 45         Options.memtable_factory: SkipListFactory
2025/08/04-13:39:34.296042 45            Options.table_factory: BlockBasedTable
2025/08/04-13:39:34.296069 45            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7fea65b000c0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0x7fea65a40010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 1001872834
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: (nil)
  persistent_cache: (nil)
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2025/08/04-13:39:34.296070 45        Options.write_buffer_size: 67108864
2025/08/04-13:39:34.296071 45  Options.max_write_buffer_number: 2
2025/08/04-13:39:34.296073 45        Options.compression[0]: NoCompression
2025/08/04-13:39:34.296073 45        Options.compression[1]: NoCompression
2025/08/04-13:39:34.296074 45        Options.compression[2]: ZSTD
2025/08/04-13:39:34.296075 45        Options.compression[3]: ZSTD
2025/08/04-13:39:34.296075 45        Options.compression[4]: ZSTD
2025/08/04-13:39:34.296076 45                  Options.bottommost_compression: Disabled
2025/08/04-13:39:34.296077 45       Options.prefix_extractor: nullptr
2025/08/04-13:39:34.296077 45   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/04-13:39:34.296078 45             Options.num_levels: 5
2025/08/04-13:39:34.296078 45        Options.min_write_buffer_number_to_merge: 1
2025/08/04-13:39:34.296079 45     Options.max_write_buffer_number_to_maintain: 0
2025/08/04-13:39:34.296080 45     Options.max_write_buffer_size_to_maintain: 0
2025/08/04-13:39:34.296080 45            Options.bottommost_compression_opts.window_bits: -14
2025/08/04-13:39:34.296081 45                  Options.bottommost_compression_opts.level: 32767
2025/08/04-13:39:34.296082 45               Options.bottommost_compression_opts.strategy: 0
2025/08/04-13:39:34.296082 45         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/04-13:39:34.296083 45         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/04-13:39:34.296083 45         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/04-13:39:34.296084 45                  Options.bottommost_compression_opts.enabled: false
2025/08/04-13:39:34.296085 45         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/04-13:39:34.296085 45            Options.compression_opts.window_bits: -14
2025/08/04-13:39:34.296086 45                  Options.compression_opts.level: 32767
2025/08/04-13:39:34.296087 45               Options.compression_opts.strategy: 0
2025/08/04-13:39:34.296087 45         Options.compression_opts.max_dict_bytes: 0
2025/08/04-13:39:34.296238 45         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/04-13:39:34.296239 45         Options.compression_opts.parallel_threads: 1
2025/08/04-13:39:34.296240 45                  Options.compression_opts.enabled: false
2025/08/04-13:39:34.296241 45         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/04-13:39:34.296241 45      Options.level0_file_num_compaction_trigger: 4
2025/08/04-13:39:34.296242 45          Options.level0_slowdown_writes_trigger: 20
2025/08/04-13:39:34.296243 45              Options.level0_stop_writes_trigger: 36
2025/08/04-13:39:34.296243 45                   Options.target_file_size_base: 67108864
2025/08/04-13:39:34.296244 45             Options.target_file_size_multiplier: 2
2025/08/04-13:39:34.296245 45                Options.max_bytes_for_level_base: 268435456
2025/08/04-13:39:34.296245 45 Options.level_compaction_dynamic_level_bytes: 0
2025/08/04-13:39:34.296246 45          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/04-13:39:34.296247 45 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/04-13:39:34.296248 45 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/04-13:39:34.296249 45 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/04-13:39:34.296249 45 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/04-13:39:34.296250 45 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/04-13:39:34.296251 45 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/04-13:39:34.296251 45 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/04-13:39:34.296252 45       Options.max_sequential_skip_in_iterations: 8
2025/08/04-13:39:34.296252 45                    Options.max_compaction_bytes: 1677721600
2025/08/04-13:39:34.296253 45                        Options.arena_block_size: 1048576
2025/08/04-13:39:34.296254 45   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/04-13:39:34.296254 45   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/04-13:39:34.296255 45       Options.rate_limit_delay_max_milliseconds: 100
2025/08/04-13:39:34.296255 45                Options.disable_auto_compactions: 0
2025/08/04-13:39:34.296258 45                        Options.compaction_style: kCompactionStyleLevel
2025/08/04-13:39:34.296259 45                          Options.compaction_pri: kMinOverlappingRatio
2025/08/04-13:39:34.296259 45 Options.compaction_options_universal.size_ratio: 1
2025/08/04-13:39:34.296260 45 Options.compaction_options_universal.min_merge_width: 2
2025/08/04-13:39:34.296260 45 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/04-13:39:34.296261 45 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/04-13:39:34.296262 45 Options.compaction_options_universal.compression_size_percent: -1
2025/08/04-13:39:34.296263 45 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/04-13:39:34.296263 45 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/04-13:39:34.296264 45 Options.compaction_options_fifo.allow_compaction: 0
2025/08/04-13:39:34.296269 45                   Options.table_properties_collectors: 
2025/08/04-13:39:34.296270 45                   Options.inplace_update_support: 0
2025/08/04-13:39:34.296271 45                 Options.inplace_update_num_locks: 10000
2025/08/04-13:39:34.296271 45               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/04-13:39:34.296272 45               Options.memtable_whole_key_filtering: 0
2025/08/04-13:39:34.296273 45   Options.memtable_huge_page_size: 0
2025/08/04-13:39:34.296274 45                           Options.bloom_locality: 0
2025/08/04-13:39:34.296274 45                    Options.max_successive_merges: 0
2025/08/04-13:39:34.296275 45                Options.optimize_filters_for_hits: 0
2025/08/04-13:39:34.296275 45                Options.paranoid_file_checks: 0
2025/08/04-13:39:34.296276 45                Options.force_consistency_checks: 1
2025/08/04-13:39:34.296276 45                Options.report_bg_io_stats: 0
2025/08/04-13:39:34.296478 45                               Options.ttl: 2592000
2025/08/04-13:39:34.296481 45          Options.periodic_compaction_seconds: 0
2025/08/04-13:39:34.296482 45                       Options.enable_blob_files: false
2025/08/04-13:39:34.296483 45                           Options.min_blob_size: 0
2025/08/04-13:39:34.296484 45                          Options.blob_file_size: 268435456
2025/08/04-13:39:34.296485 45                   Options.blob_compression_type: NoCompression
2025/08/04-13:39:34.296485 45          Options.enable_blob_garbage_collection: false
2025/08/04-13:39:34.296486 45      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/04-13:39:34.296488 45 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/04-13:39:34.296489 45          Options.blob_compaction_readahead_size: 0
2025/08/04-13:39:34.300488 45 [db/version_set.cc:4928] Recovered from manifest file:/var/lib/milvus/rdb_data_meta_kv/MANIFEST-000035 succeeded,manifest_file_number is 35, next_file_number is 37, last_sequence is 34, log_number is 32,prev_log_number is 0,max_column_family is 0,min_log_number_to_keep is 0
2025/08/04-13:39:34.300497 45 [db/version_set.cc:4943] Column family [default] (ID 0), log number is 32
2025/08/04-13:39:34.304265 45 [db/version_set.cc:4409] Creating manifest 39
2025/08/04-13:39:34.327388 45 EVENT_LOG_v1 {"time_micros": 1754314774327370, "job": 1, "event": "recovery_started", "wal_files": [11, 16, 21, 26, 31, 36]}
2025/08/04-13:39:34.327396 45 [db/db_impl/db_impl_open.cc:874] Skipping log #11 since it is older than min log to keep #32
2025/08/04-13:39:34.327397 45 [db/db_impl/db_impl_open.cc:874] Skipping log #16 since it is older than min log to keep #32
2025/08/04-13:39:34.327398 45 [db/db_impl/db_impl_open.cc:874] Skipping log #21 since it is older than min log to keep #32
2025/08/04-13:39:34.327399 45 [db/db_impl/db_impl_open.cc:874] Skipping log #26 since it is older than min log to keep #32
2025/08/04-13:39:34.327399 45 [db/db_impl/db_impl_open.cc:874] Skipping log #31 since it is older than min log to keep #32
2025/08/04-13:39:34.327401 45 [db/db_impl/db_impl_open.cc:888] Recovering log #36 mode 2
2025/08/04-13:39:34.342921 45 EVENT_LOG_v1 {"time_micros": 1754314774342749, "cf_name": "default", "job": 1, "event": "table_file_creation", "file_number": 40, "file_size": 1057, "file_checksum": "", "file_checksum_func_name": "Unknown", "table_properties": {"data_size": 90, "index_size": 52, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 84, "raw_average_key_size": 42, "raw_value_size": 8, "raw_average_value_size": 4, "num_data_blocks": 1, "num_entries": 2, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; ", "creation_time": 1754314774, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "5d84d4a4-9f27-46dd-b1e4-572f86de492f", "db_session_id": "54R3F314YINJHB7F4TBP", "orig_file_number": 40}}
2025/08/04-13:39:34.343177 45 [db/version_set.cc:4409] Creating manifest 41
2025/08/04-13:39:34.365170 45 EVENT_LOG_v1 {"time_micros": 1754314774365159, "job": 1, "event": "recovery_finished"}
2025/08/04-13:39:34.390299 45 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data_meta_kv/000036.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/08/04-13:39:34.391268 45 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data_meta_kv/000031.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/08/04-13:39:34.392371 45 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data_meta_kv/000026.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/08/04-13:39:34.393478 45 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data_meta_kv/000021.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/08/04-13:39:34.394509 45 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data_meta_kv/000016.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/08/04-13:39:34.395559 45 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data_meta_kv/000011.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/08/04-13:39:34.395788 45 [db/db_impl/db_impl_open.cc:1820] SstFileManager instance 0x7fea62b00000
2025/08/04-13:39:34.396975 45 DB pointer 0x7fea63620000
2025/08/04-13:39:34.398530 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/04-13:39:34.398551 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 0.1 total, 0.1 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0    2.72 KB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.1      0.01              0.00         1    0.011       0      0       0.0       0.0
 Sum      2/0    2.72 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.1      0.01              0.00         1    0.011       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.1      0.01              0.00         1    0.011       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.1      0.01              0.00         1    0.011       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 0.1 total, 0.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.01 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.01 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fea65a40010#8 capacity: 955.46 MB collections: 1 last_copies: 0 last_secs: 0.0001 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/08/04-13:49:34.398813 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/04-13:49:34.399101 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 600.1 total, 600.0 interval
Cumulative writes: 2861 writes, 2861 keys, 2861 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 2861 writes, 0 syncs, 2861.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 2861 writes, 2861 keys, 2861 commit groups, 1.0 writes per commit group, ingest: 0.15 MB, 0.00 MB/s
Interval WAL: 2861 writes, 0 syncs, 2861.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0    2.72 KB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.1      0.01              0.00         1    0.011       0      0       0.0       0.0
 Sum      2/0    2.72 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.1      0.01              0.00         1    0.011       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.1      0.01              0.00         1    0.011       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 600.1 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fea65a40010#8 capacity: 955.46 MB collections: 2 last_copies: 0 last_secs: 9.1e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(3,65.08 KB,0.00665154%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/08/04-13:51:29.471376 35 [db/db_impl/db_impl.cc:481] Shutdown: canceling all background work
2025/08/04-13:51:29.477131 35 [db/db_impl/db_impl.cc:699] Shutdown complete
