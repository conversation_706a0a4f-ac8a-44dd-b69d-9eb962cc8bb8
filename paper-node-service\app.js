import express from 'express';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import routes from './src/routes/index.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const app = express();
const port = process.env.PORT || 9529; // 修改默认端口为9529

// 中间件
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 注册所有路由
app.use('/api', routes);

// 错误处理中间件
app.use((err, req, res, next) => {
  console.error('服务器错误:', err);
  res.status(500).json({ error: '服务器内部错误' });
});

// 优雅地处理服务器启动
const server = app.listen(port, () => {
  console.log(`express 服务已启动，端口：${port}`);
}).on('error', (err) => {
  if (err.code === 'EADDRINUSE') {
    console.error(`端口 ${port} 已被占用，请尝试其他端口`);
    process.exit(1);
  } else {
    console.error('服务器启动错误:', err);
    process.exit(1);
  }
});

// 优雅地处理进程终止
process.on('SIGTERM', () => {
  console.log('收到 SIGTERM 信号，正在关闭服务器...');
  server.close(() => {
    console.log('服务器已关闭');
    process.exit(0);
  });
});