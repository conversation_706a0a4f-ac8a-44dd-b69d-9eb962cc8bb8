id: 60030
name: Doubao-1.5-Pro-256k
icon_uri: default_icon/doubao_v2.png
icon_url: ""
description:
    zh: doubao-1.5-pro-256k，基于doubao-1.5-Pro全面升级版，整体效果大幅提升10%。更高性能、更大窗口、超高性价比，适用于更广泛的应用场景。
    en: doubao-1.5-pro-256k is a fully upgraded version based on doubao-1.5-Pro, with an overall performance improvement of 10%. It offers higher performance, a larger context window, and exceptional cost-effectiveness, making it suitable for a wider range of application scenarios.
default_parameters:
    - name: temperature
      label:
        zh: 生成随机性
        en: Temperature
      desc:
        zh: '- **temperature**: 调高温度会使得模型的输出更多样性和创新性，反之，降低温度会使输出内容更加遵循指令要求但减少多样性。建议不要与“Top p”同时调整。'
        en: '**Temperature**:\n\n- When you increase this value, the model outputs more diverse and innovative content; when you decrease it, the model outputs less diverse content that strictly follows the given instructions.\n- It is recommended not to adjust this value with \"Top p\" at the same time.'
      type: float
      min: "0"
      max: "1"
      default_val:
        balance: "0.8"
        creative: "1"
        default_val: "1.0"
        precise: "0.3"
      precision: 1
      options: []
      style:
        widget: slider
        label:
            zh: 生成多样性
            en: Generation diversity
    - name: max_tokens
      label:
        zh: 最大回复长度
        en: Response max length
      desc:
        zh: 控制模型输出的Tokens 长度上限。通常 100 Tokens 约等于 150 个中文汉字。
        en: You can specify the maximum length of the tokens output through this value. Typically, 100 tokens are approximately equal to 150 Chinese characters.
      type: int
      min: "1"
      max: "4096"
      default_val:
        default_val: "4096"
      options: []
      style:
        widget: slider
        label:
            zh: 输入及输出设置
            en: Input and output settings
    - name: top_p
      label:
        zh: Top P
        en: Top P
      desc:
        zh: '- **Top p 为累计概率**: 模型在生成输出时会从概率最高的词汇开始选择，直到这些词汇的总概率累积达到Top p 值。这样可以限制模型只选择这些高概率的词汇，从而控制输出内容的多样性。建议不要与“生成随机性”同时调整。'
        en: '**Top P**:\n\n- An alternative to sampling with temperature, where only tokens within the top p probability mass are considered. For example, 0.1 means only the top 10% probability mass tokens are considered.\n- We recommend altering this or temperature, but not both.'
      type: float
      min: "0"
      max: "1"
      default_val:
        default_val: "0.7"
      precision: 2
      options: []
      style:
        widget: slider
        label:
            zh: 生成多样性
            en: Generation diversity
meta:
    protocol: ark
    capability:
        function_call: true
        input_modal:
            - text
        input_tokens: 96000
        json_mode: false
        max_tokens: 256000
        output_modal:
            - text
        output_tokens: 12000
        prefix_caching: false
        reasoning: false
        prefill_response: false
    conn_config:
        base_url: "https://ark.cn-beijing.volces.com/api/v3/"
        api_key: ""
        timeout: 0s
        model: "" # model_id / endpoint_id
        temperature: 0.1
        frequency_penalty: 0
        presence_penalty: 0
        max_tokens: 4096
        top_p: 0.7
        top_k: 0
        stop: []
        ark:
            region: ""
            access_key: ""
            secret_key: ""
            retry_times: null
            custom_header: {}
        custom: {}
    status: 0
