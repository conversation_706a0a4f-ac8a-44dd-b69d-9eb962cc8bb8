// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameNodeExecution = "node_execution"

// NodeExecution Node run record, used to record the status information of each node during each workflow execution
type NodeExecution struct {
	ID                 int64  `gorm:"column:id;primaryKey;comment:node execution id" json:"id"`                                                                      // node execution id
	ExecuteID          int64  `gorm:"column:execute_id;not null;comment:the workflow execute id this node execution belongs to" json:"execute_id"`                   // the workflow execute id this node execution belongs to
	NodeID             string `gorm:"column:node_id;not null;comment:node key" json:"node_id"`                                                                       // node key
	NodeName           string `gorm:"column:node_name;not null;comment:name of the node" json:"node_name"`                                                           // name of the node
	NodeType           string `gorm:"column:node_type;not null;comment:the type of the node, in string" json:"node_type"`                                            // the type of the node, in string
	CreatedAt          int64  `gorm:"column:created_at;not null;autoCreateTime:milli;comment:create time in millisecond" json:"created_at"`                          // create time in millisecond
	Status             int32  `gorm:"column:status;not null;comment:1=waiting 2=running 3=success 4=fail" json:"status"`                                             // 1=waiting 2=running 3=success 4=fail
	Duration           int64  `gorm:"column:duration;comment:execution duration in millisecond" json:"duration"`                                                     // execution duration in millisecond
	Input              string `gorm:"column:input;comment:actual input of the node" json:"input"`                                                                    // actual input of the node
	Output             string `gorm:"column:output;comment:actual output of the node" json:"output"`                                                                 // actual output of the node
	RawOutput          string `gorm:"column:raw_output;comment:the original output of the node" json:"raw_output"`                                                   // the original output of the node
	ErrorInfo          string `gorm:"column:error_info;comment:error info" json:"error_info"`                                                                        // error info
	ErrorLevel         string `gorm:"column:error_level;comment:level of the error" json:"error_level"`                                                              // level of the error
	InputTokens        int64  `gorm:"column:input_tokens;comment:number of input tokens" json:"input_tokens"`                                                        // number of input tokens
	OutputTokens       int64  `gorm:"column:output_tokens;comment:number of output tokens" json:"output_tokens"`                                                     // number of output tokens
	UpdatedAt          int64  `gorm:"column:updated_at;autoUpdateTime:milli;comment:update time in millisecond" json:"updated_at"`                                   // update time in millisecond
	CompositeNodeIndex int64  `gorm:"column:composite_node_index;comment:loop or batch's execution index" json:"composite_node_index"`                               // loop or batch's execution index
	CompositeNodeItems string `gorm:"column:composite_node_items;comment:the items extracted from parent composite node for this index" json:"composite_node_items"` // the items extracted from parent composite node for this index
	ParentNodeID       string `gorm:"column:parent_node_id;comment:when as inner node for loop or batch, this is the parent node's key" json:"parent_node_id"`       // when as inner node for loop or batch, this is the parent node's key
	SubExecuteID       int64  `gorm:"column:sub_execute_id;comment:if this node is sub_workflow, the exe id of the sub workflow" json:"sub_execute_id"`              // if this node is sub_workflow, the exe id of the sub workflow
	Extra              string `gorm:"column:extra;comment:extra info" json:"extra"`                                                                                  // extra info
}

// TableName NodeExecution's table name
func (*NodeExecution) TableName() string {
	return TableNameNodeExecution
}
