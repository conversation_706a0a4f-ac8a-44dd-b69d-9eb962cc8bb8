2025/08/04-07:27:35.271929 38 RocksDB version: 6.29.5
2025/08/04-07:27:35.272893 38 Git sha 0
2025/08/04-07:27:35.272902 38 Compile date 2024-11-15 11:22:58
2025/08/04-07:27:35.272923 38 DB SUMMARY
2025/08/04-07:27:35.272926 38 DB Session ID:  EJSCCFHN5Q31SEW6D2KL
2025/08/04-07:27:35.274643 38 CURRENT file:  CURRENT
2025/08/04-07:27:35.274648 38 IDENTITY file:  IDENTITY
2025/08/04-07:27:35.275256 38 MANIFEST file:  MANIFEST-000010 size: 185 Bytes
2025/08/04-07:27:35.275262 38 SST files in /var/lib/milvus/rdb_data_meta_kv dir, Total Num: 1, files: 000009.sst 
2025/08/04-07:27:35.275266 38 Write Ahead Log file in /var/lib/milvus/rdb_data_meta_kv: 000011.log size: 0 ; 
2025/08/04-07:27:35.275268 38                         Options.error_if_exists: 0
2025/08/04-07:27:35.275269 38                       Options.create_if_missing: 1
2025/08/04-07:27:35.275270 38                         Options.paranoid_checks: 1
2025/08/04-07:27:35.275271 38             Options.flush_verify_memtable_count: 1
2025/08/04-07:27:35.275271 38                               Options.track_and_verify_wals_in_manifest: 0
2025/08/04-07:27:35.275272 38                                     Options.env: 0x7f9176a0dd00
2025/08/04-07:27:35.275273 38                                      Options.fs: PosixFileSystem
2025/08/04-07:27:35.275274 38                                Options.info_log: 0x7f9099890050
2025/08/04-07:27:35.275274 38                Options.max_file_opening_threads: 16
2025/08/04-07:27:35.275275 38                              Options.statistics: (nil)
2025/08/04-07:27:35.275276 38                               Options.use_fsync: 0
2025/08/04-07:27:35.275276 38                       Options.max_log_file_size: 0
2025/08/04-07:27:35.275277 38                  Options.max_manifest_file_size: 1073741824
2025/08/04-07:27:35.275278 38                   Options.log_file_time_to_roll: 0
2025/08/04-07:27:35.275278 38                       Options.keep_log_file_num: 1000
2025/08/04-07:27:35.275279 38                    Options.recycle_log_file_num: 0
2025/08/04-07:27:35.275279 38                         Options.allow_fallocate: 1
2025/08/04-07:27:35.275280 38                        Options.allow_mmap_reads: 0
2025/08/04-07:27:35.275281 38                       Options.allow_mmap_writes: 0
2025/08/04-07:27:35.275281 38                        Options.use_direct_reads: 0
2025/08/04-07:27:35.275282 38                        Options.use_direct_io_for_flush_and_compaction: 0
2025/08/04-07:27:35.275282 38          Options.create_missing_column_families: 0
2025/08/04-07:27:35.275283 38                              Options.db_log_dir: 
2025/08/04-07:27:35.275283 38                                 Options.wal_dir: 
2025/08/04-07:27:35.275284 38                Options.table_cache_numshardbits: 6
2025/08/04-07:27:35.275285 38                         Options.WAL_ttl_seconds: 0
2025/08/04-07:27:35.275285 38                       Options.WAL_size_limit_MB: 0
2025/08/04-07:27:35.275286 38                        Options.max_write_batch_group_size_bytes: 1048576
2025/08/04-07:27:35.275286 38             Options.manifest_preallocation_size: 4194304
2025/08/04-07:27:35.275287 38                     Options.is_fd_close_on_exec: 1
2025/08/04-07:27:35.275288 38                   Options.advise_random_on_open: 1
2025/08/04-07:27:35.275288 38                   Options.experimental_mempurge_threshold: 0.000000
2025/08/04-07:27:35.275724 38                    Options.db_write_buffer_size: 0
2025/08/04-07:27:35.275726 38                    Options.write_buffer_manager: 0x7f909c2400a0
2025/08/04-07:27:35.275727 38         Options.access_hint_on_compaction_start: 1
2025/08/04-07:27:35.275728 38  Options.new_table_reader_for_compaction_inputs: 0
2025/08/04-07:27:35.275728 38           Options.random_access_max_buffer_size: 1048576
2025/08/04-07:27:35.275729 38                      Options.use_adaptive_mutex: 0
2025/08/04-07:27:35.275730 38                            Options.rate_limiter: (nil)
2025/08/04-07:27:35.275744 38     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/08/04-07:27:35.275745 38                       Options.wal_recovery_mode: 2
2025/08/04-07:27:35.276105 38                  Options.enable_thread_tracking: 0
2025/08/04-07:27:35.276109 38                  Options.enable_pipelined_write: 0
2025/08/04-07:27:35.276110 38                  Options.unordered_write: 0
2025/08/04-07:27:35.276111 38         Options.allow_concurrent_memtable_write: 1
2025/08/04-07:27:35.276112 38      Options.enable_write_thread_adaptive_yield: 1
2025/08/04-07:27:35.276112 38             Options.write_thread_max_yield_usec: 100
2025/08/04-07:27:35.276113 38            Options.write_thread_slow_yield_usec: 3
2025/08/04-07:27:35.276114 38                               Options.row_cache: None
2025/08/04-07:27:35.276114 38                              Options.wal_filter: None
2025/08/04-07:27:35.276115 38             Options.avoid_flush_during_recovery: 0
2025/08/04-07:27:35.276116 38             Options.allow_ingest_behind: 0
2025/08/04-07:27:35.276116 38             Options.preserve_deletes: 0
2025/08/04-07:27:35.276117 38             Options.two_write_queues: 0
2025/08/04-07:27:35.276117 38             Options.manual_wal_flush: 0
2025/08/04-07:27:35.276118 38             Options.atomic_flush: 0
2025/08/04-07:27:35.276119 38             Options.avoid_unnecessary_blocking_io: 0
2025/08/04-07:27:35.276119 38                 Options.persist_stats_to_disk: 0
2025/08/04-07:27:35.276120 38                 Options.write_dbid_to_manifest: 0
2025/08/04-07:27:35.276120 38                 Options.log_readahead_size: 0
2025/08/04-07:27:35.276121 38                 Options.file_checksum_gen_factory: Unknown
2025/08/04-07:27:35.276122 38                 Options.best_efforts_recovery: 0
2025/08/04-07:27:35.276122 38                Options.max_bgerror_resume_count: 2147483647
2025/08/04-07:27:35.276123 38            Options.bgerror_resume_retry_interval: 1000000
2025/08/04-07:27:35.276124 38             Options.allow_data_in_errors: 0
2025/08/04-07:27:35.276124 38             Options.db_host_id: __hostname__
2025/08/04-07:27:35.276127 38             Options.max_background_jobs: 2
2025/08/04-07:27:35.276127 38             Options.max_background_compactions: -1
2025/08/04-07:27:35.276128 38             Options.max_subcompactions: 1
2025/08/04-07:27:35.276128 38             Options.avoid_flush_during_shutdown: 0
2025/08/04-07:27:35.276129 38           Options.writable_file_max_buffer_size: 1048576
2025/08/04-07:27:35.276130 38             Options.delayed_write_rate : 16777216
2025/08/04-07:27:35.276130 38             Options.max_total_wal_size: 0
2025/08/04-07:27:35.276131 38             Options.delete_obsolete_files_period_micros: 21600000000
2025/08/04-07:27:35.276132 38                   Options.stats_dump_period_sec: 600
2025/08/04-07:27:35.276132 38                 Options.stats_persist_period_sec: 600
2025/08/04-07:27:35.276133 38                 Options.stats_history_buffer_size: 1048576
2025/08/04-07:27:35.276133 38                          Options.max_open_files: -1
2025/08/04-07:27:35.276134 38                          Options.bytes_per_sync: 0
2025/08/04-07:27:35.276135 38                      Options.wal_bytes_per_sync: 0
2025/08/04-07:27:35.276135 38                   Options.strict_bytes_per_sync: 0
2025/08/04-07:27:35.276136 38       Options.compaction_readahead_size: 0
2025/08/04-07:27:35.276136 38                  Options.max_background_flushes: 1
2025/08/04-07:27:35.276137 38 Compression algorithms supported:
2025/08/04-07:27:35.276139 38 	kZSTD supported: 1
2025/08/04-07:27:35.276139 38 	kXpressCompression supported: 0
2025/08/04-07:27:35.276140 38 	kBZip2Compression supported: 0
2025/08/04-07:27:35.276141 38 	kZSTDNotFinalCompression supported: 1
2025/08/04-07:27:35.276142 38 	kLZ4Compression supported: 0
2025/08/04-07:27:35.276143 38 	kZlibCompression supported: 0
2025/08/04-07:27:35.276143 38 	kLZ4HCCompression supported: 0
2025/08/04-07:27:35.276144 38 	kSnappyCompression supported: 0
2025/08/04-07:27:35.276148 38 Fast CRC32 supported: Not supported on x86
2025/08/04-07:27:35.282332 38 [db/version_set.cc:4888] Recovering from manifest file: /var/lib/milvus/rdb_data_meta_kv/MANIFEST-000010
2025/08/04-07:27:35.285254 38 [db/column_family.cc:605] --------------- Options for column family [default]:
2025/08/04-07:27:35.285266 38               Options.comparator: leveldb.BytewiseComparator
2025/08/04-07:27:35.285268 38           Options.merge_operator: None
2025/08/04-07:27:35.285269 38        Options.compaction_filter: None
2025/08/04-07:27:35.285269 38        Options.compaction_filter_factory: None
2025/08/04-07:27:35.285270 38  Options.sst_partitioner_factory: None
2025/08/04-07:27:35.285271 38         Options.memtable_factory: SkipListFactory
2025/08/04-07:27:35.285272 38            Options.table_factory: BlockBasedTable
2025/08/04-07:27:35.285309 38            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f909c3000c0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0x7f909c240010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 1001872834
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: (nil)
  persistent_cache: (nil)
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2025/08/04-07:27:35.285312 38        Options.write_buffer_size: 67108864
2025/08/04-07:27:35.285312 38  Options.max_write_buffer_number: 2
2025/08/04-07:27:35.285314 38        Options.compression[0]: NoCompression
2025/08/04-07:27:35.285315 38        Options.compression[1]: NoCompression
2025/08/04-07:27:35.285316 38        Options.compression[2]: ZSTD
2025/08/04-07:27:35.285316 38        Options.compression[3]: ZSTD
2025/08/04-07:27:35.285317 38        Options.compression[4]: ZSTD
2025/08/04-07:27:35.285318 38                  Options.bottommost_compression: Disabled
2025/08/04-07:27:35.285318 38       Options.prefix_extractor: nullptr
2025/08/04-07:27:35.285319 38   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/04-07:27:35.285319 38             Options.num_levels: 5
2025/08/04-07:27:35.285320 38        Options.min_write_buffer_number_to_merge: 1
2025/08/04-07:27:35.285321 38     Options.max_write_buffer_number_to_maintain: 0
2025/08/04-07:27:35.285321 38     Options.max_write_buffer_size_to_maintain: 0
2025/08/04-07:27:35.285322 38            Options.bottommost_compression_opts.window_bits: -14
2025/08/04-07:27:35.285323 38                  Options.bottommost_compression_opts.level: 32767
2025/08/04-07:27:35.285323 38               Options.bottommost_compression_opts.strategy: 0
2025/08/04-07:27:35.285324 38         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/04-07:27:35.285324 38         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/04-07:27:35.285325 38         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/04-07:27:35.285326 38                  Options.bottommost_compression_opts.enabled: false
2025/08/04-07:27:35.285326 38         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/04-07:27:35.285327 38            Options.compression_opts.window_bits: -14
2025/08/04-07:27:35.285328 38                  Options.compression_opts.level: 32767
2025/08/04-07:27:35.285328 38               Options.compression_opts.strategy: 0
2025/08/04-07:27:35.285329 38         Options.compression_opts.max_dict_bytes: 0
2025/08/04-07:27:35.285329 38         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/04-07:27:35.285330 38         Options.compression_opts.parallel_threads: 1
2025/08/04-07:27:35.285527 38                  Options.compression_opts.enabled: false
2025/08/04-07:27:35.285533 38         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/04-07:27:35.285534 38      Options.level0_file_num_compaction_trigger: 4
2025/08/04-07:27:35.285536 38          Options.level0_slowdown_writes_trigger: 20
2025/08/04-07:27:35.285537 38              Options.level0_stop_writes_trigger: 36
2025/08/04-07:27:35.285538 38                   Options.target_file_size_base: 67108864
2025/08/04-07:27:35.285539 38             Options.target_file_size_multiplier: 2
2025/08/04-07:27:35.285540 38                Options.max_bytes_for_level_base: 268435456
2025/08/04-07:27:35.285541 38 Options.level_compaction_dynamic_level_bytes: 0
2025/08/04-07:27:35.285542 38          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/04-07:27:35.285545 38 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/04-07:27:35.285547 38 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/04-07:27:35.285548 38 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/04-07:27:35.285549 38 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/04-07:27:35.285550 38 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/04-07:27:35.285551 38 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/04-07:27:35.285551 38 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/04-07:27:35.285552 38       Options.max_sequential_skip_in_iterations: 8
2025/08/04-07:27:35.285553 38                    Options.max_compaction_bytes: 1677721600
2025/08/04-07:27:35.285554 38                        Options.arena_block_size: 1048576
2025/08/04-07:27:35.285555 38   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/04-07:27:35.285557 38   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/04-07:27:35.285558 38       Options.rate_limit_delay_max_milliseconds: 100
2025/08/04-07:27:35.285559 38                Options.disable_auto_compactions: 0
2025/08/04-07:27:35.285564 38                        Options.compaction_style: kCompactionStyleLevel
2025/08/04-07:27:35.285565 38                          Options.compaction_pri: kMinOverlappingRatio
2025/08/04-07:27:35.285566 38 Options.compaction_options_universal.size_ratio: 1
2025/08/04-07:27:35.285567 38 Options.compaction_options_universal.min_merge_width: 2
2025/08/04-07:27:35.285568 38 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/04-07:27:35.285569 38 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/04-07:27:35.285570 38 Options.compaction_options_universal.compression_size_percent: -1
2025/08/04-07:27:35.285572 38 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/04-07:27:35.285573 38 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/04-07:27:35.285574 38 Options.compaction_options_fifo.allow_compaction: 0
2025/08/04-07:27:35.285605 38                   Options.table_properties_collectors: 
2025/08/04-07:27:35.285608 38                   Options.inplace_update_support: 0
2025/08/04-07:27:35.285608 38                 Options.inplace_update_num_locks: 10000
2025/08/04-07:27:35.285609 38               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/04-07:27:35.285611 38               Options.memtable_whole_key_filtering: 0
2025/08/04-07:27:35.285611 38   Options.memtable_huge_page_size: 0
2025/08/04-07:27:35.285612 38                           Options.bloom_locality: 0
2025/08/04-07:27:35.285612 38                    Options.max_successive_merges: 0
2025/08/04-07:27:35.285613 38                Options.optimize_filters_for_hits: 0
2025/08/04-07:27:35.285614 38                Options.paranoid_file_checks: 0
2025/08/04-07:27:35.285614 38                Options.force_consistency_checks: 1
2025/08/04-07:27:35.285615 38                Options.report_bg_io_stats: 0
2025/08/04-07:27:35.285615 38                               Options.ttl: 2592000
2025/08/04-07:27:35.285616 38          Options.periodic_compaction_seconds: 0
2025/08/04-07:27:35.285847 38                       Options.enable_blob_files: false
2025/08/04-07:27:35.285849 38                           Options.min_blob_size: 0
2025/08/04-07:27:35.285850 38                          Options.blob_file_size: 268435456
2025/08/04-07:27:35.285852 38                   Options.blob_compression_type: NoCompression
2025/08/04-07:27:35.285853 38          Options.enable_blob_garbage_collection: false
2025/08/04-07:27:35.285853 38      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/04-07:27:35.285854 38 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/04-07:27:35.285855 38          Options.blob_compaction_readahead_size: 0
2025/08/04-07:27:35.300148 38 [db/version_set.cc:4928] Recovered from manifest file:/var/lib/milvus/rdb_data_meta_kv/MANIFEST-000010 succeeded,manifest_file_number is 10, next_file_number is 12, last_sequence is 34, log_number is 6,prev_log_number is 0,max_column_family is 0,min_log_number_to_keep is 0
2025/08/04-07:27:35.300160 38 [db/version_set.cc:4943] Column family [default] (ID 0), log number is 6
2025/08/04-07:27:35.305172 38 [db/version_set.cc:4409] Creating manifest 14
2025/08/04-07:27:35.324622 38 EVENT_LOG_v1 {"time_micros": 1754292455324598, "job": 1, "event": "recovery_started", "wal_files": [11]}
2025/08/04-07:27:35.324630 38 [db/db_impl/db_impl_open.cc:888] Recovering log #11 mode 2
2025/08/04-07:27:35.325612 38 [db/version_set.cc:4409] Creating manifest 15
2025/08/04-07:27:35.347784 38 EVENT_LOG_v1 {"time_micros": 1754292455347771, "job": 1, "event": "recovery_finished"}
2025/08/04-07:27:35.369673 38 [db/db_impl/db_impl_open.cc:1820] SstFileManager instance 0x7f9099990000
2025/08/04-07:27:35.370790 38 DB pointer 0x7f9099820000
2025/08/04-07:27:35.371357 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/04-07:27:35.371425 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 0.1 total, 0.1 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    1.69 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      1/0    1.69 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 0.1 total, 0.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f909c240010#8 capacity: 955.46 MB collections: 1 last_copies: 0 last_secs: 8.4e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/08/04-07:37:35.372089 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/04-07:37:35.372830 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 600.1 total, 600.0 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    1.69 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      1/0    1.69 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 600.1 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f909c240010#8 capacity: 955.46 MB collections: 2 last_copies: 0 last_secs: 0.000169 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(1,0.83 KB,8.46415e-05%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/08/04-07:47:35.373177 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/04-07:47:35.373467 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 1200.1 total, 600.0 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    1.69 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      1/0    1.69 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 1200.1 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f909c240010#8 capacity: 955.46 MB collections: 3 last_copies: 0 last_secs: 5.4e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(1,0.83 KB,8.46415e-05%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/08/04-07:57:35.373729 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/04-07:57:35.373930 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 1800.1 total, 600.0 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    1.69 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      1/0    1.69 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 1800.1 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f909c240010#8 capacity: 955.46 MB collections: 4 last_copies: 0 last_secs: 5.5e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(1,0.83 KB,8.46415e-05%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/08/04-08:07:35.374290 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/04-08:07:35.374573 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 2400.1 total, 600.0 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    1.69 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      1/0    1.69 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 2400.1 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f909c240010#8 capacity: 955.46 MB collections: 5 last_copies: 0 last_secs: 0.0001 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(1,0.83 KB,8.46415e-05%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/08/04-08:17:35.374830 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/04-08:17:35.375110 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 3000.1 total, 600.0 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    1.69 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      1/0    1.69 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3000.1 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f909c240010#8 capacity: 955.46 MB collections: 6 last_copies: 0 last_secs: 6.5e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(1,0.83 KB,8.46415e-05%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/08/04-08:27:35.375412 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/04-08:27:35.376787 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 3600.1 total, 600.0 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    1.69 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      1/0    1.69 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3600.1 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f909c240010#8 capacity: 955.46 MB collections: 7 last_copies: 0 last_secs: 5.7e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(1,0.83 KB,8.46415e-05%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/08/04-08:37:35.377233 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/04-08:37:35.377649 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 4200.1 total, 600.0 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    1.69 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      1/0    1.69 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 4200.1 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f909c240010#8 capacity: 955.46 MB collections: 8 last_copies: 0 last_secs: 9.8e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(1,0.83 KB,8.46415e-05%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/08/04-08:41:32.840838 40 [db/db_impl/db_impl.cc:481] Shutdown: canceling all background work
2025/08/04-08:41:32.859649 40 [db/db_impl/db_impl.cc:699] Shutdown complete
