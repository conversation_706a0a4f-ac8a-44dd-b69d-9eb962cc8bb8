# K8s 文件清单

## 📁 核心配置文件

### 🏗️ 基础设施
- `infrastructure.yaml` - 服务隔离的基础设施（PostgreSQL、Redis）
- `storage.yaml` - 每个服务独立的存储配置
- `namespace.yaml` - 命名空间定义

### 🚀 应用部署
- `paper-editor-api-deployment.yaml` - Go API 服务
- `lunwen-generate-ui-deployment.yaml` - 前端 UI 服务
- `dify-deployment.yaml` - Dify AI 平台
- `paper-node-service-deployment.yaml` - Node.js 工具服务
- `paper-py-service-deployment.yaml` - Python 分析服务
- `paper-api-service-deplyment.yml` - 额外的 API 服务（如果存在）

### 🌐 网络配置
- `ingress.yaml` - 统一入口配置
- `services-config.yaml` - 服务发现和环境变量

## 🛠️ 脚本文件

### 📦 构建和部署
- `build-images.ps1` - 构建所有 Docker 镜像
- `deploy.ps1` - 主部署脚本（服务隔离架构）
- `quick-start.ps1` - 一键启动脚本

### 🔧 管理工具
- `cleanup.ps1` - 清理资源脚本
- `health-check.ps1` - 健康检查脚本

## 📚 文档
- `README.md` - 完整的使用指南
- `FILES.md` - 本文件清单

## 🗑️ 已删除的冗余文件

### 共享架构文件（已被服务隔离架构替代）
- ~~`infrastructure-isolated.yaml`~~ → 重命名为 `infrastructure.yaml`
- ~~`storage-isolated.yaml`~~ → 重命名为 `storage.yaml`
- ~~`deploy-isolated.ps1`~~ → 重命名为 `deploy.ps1`

### 重复文档（已合并到 README.md）
- ~~`GETTING_STARTED.md`~~ → 合并到 `README.md`
- ~~`ARCHITECTURE_COMPARISON.md`~~ → 合并到 `README.md`

## 🎯 使用方式

### 快速启动
```powershell
.\quick-start.ps1
```

### 分步操作
```powershell
# 构建镜像
.\build-images.ps1

# 部署服务
.\deploy.ps1

# 检查健康状态
.\health-check.ps1

# 清理资源
.\cleanup.ps1 -All
```

## 📊 架构特点

- ✅ **服务隔离**: 每个服务有独立的数据库和缓存
- ✅ **微服务架构**: 符合微服务设计原则
- ✅ **本地友好**: 适合本地开发和测试
- ✅ **一键操作**: 简化的部署和管理流程
