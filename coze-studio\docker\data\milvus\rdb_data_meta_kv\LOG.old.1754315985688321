2025/08/04-13:51:45.236810 43 RocksDB version: 6.29.5
2025/08/04-13:51:45.237587 43 Git sha 0
2025/08/04-13:51:45.237597 43 Compile date 2024-11-15 11:22:58
2025/08/04-13:51:45.237605 43 DB SUMMARY
2025/08/04-13:51:45.237606 43 DB Session ID:  Q1UPF5RZP4IW6H3R6Y0V
2025/08/04-13:51:45.241129 43 CURRENT file:  CURRENT
2025/08/04-13:51:45.241143 43 IDENTITY file:  IDENTITY
2025/08/04-13:51:45.241900 43 MANIFEST file:  MANIFEST-000041 size: 303 Bytes
2025/08/04-13:51:45.241914 43 SST files in /var/lib/milvus/rdb_data_meta_kv dir, Total Num: 2, files: 000009.sst 000040.sst 
2025/08/04-13:51:45.241921 43 Write Ahead Log file in /var/lib/milvus/rdb_data_meta_kv: 000042.log size: 185720 ; 
2025/08/04-13:51:45.241925 43                         Options.error_if_exists: 0
2025/08/04-13:51:45.241927 43                       Options.create_if_missing: 1
2025/08/04-13:51:45.241928 43                         Options.paranoid_checks: 1
2025/08/04-13:51:45.241929 43             Options.flush_verify_memtable_count: 1
2025/08/04-13:51:45.241930 43                               Options.track_and_verify_wals_in_manifest: 0
2025/08/04-13:51:45.241931 43                                     Options.env: 0x7fcf72c97d00
2025/08/04-13:51:45.241933 43                                      Options.fs: PosixFileSystem
2025/08/04-13:51:45.241934 43                                Options.info_log: 0x7fce95a90050
2025/08/04-13:51:45.241935 43                Options.max_file_opening_threads: 16
2025/08/04-13:51:45.241936 43                              Options.statistics: (nil)
2025/08/04-13:51:45.241937 43                               Options.use_fsync: 0
2025/08/04-13:51:45.241938 43                       Options.max_log_file_size: 0
2025/08/04-13:51:45.241939 43                  Options.max_manifest_file_size: 1073741824
2025/08/04-13:51:45.241940 43                   Options.log_file_time_to_roll: 0
2025/08/04-13:51:45.241941 43                       Options.keep_log_file_num: 1000
2025/08/04-13:51:45.241942 43                    Options.recycle_log_file_num: 0
2025/08/04-13:51:45.241943 43                         Options.allow_fallocate: 1
2025/08/04-13:51:45.241944 43                        Options.allow_mmap_reads: 0
2025/08/04-13:51:45.241945 43                       Options.allow_mmap_writes: 0
2025/08/04-13:51:45.241946 43                        Options.use_direct_reads: 0
2025/08/04-13:51:45.241946 43                        Options.use_direct_io_for_flush_and_compaction: 0
2025/08/04-13:51:45.241947 43          Options.create_missing_column_families: 0
2025/08/04-13:51:45.241948 43                              Options.db_log_dir: 
2025/08/04-13:51:45.241949 43                                 Options.wal_dir: 
2025/08/04-13:51:45.241950 43                Options.table_cache_numshardbits: 6
2025/08/04-13:51:45.241950 43                         Options.WAL_ttl_seconds: 0
2025/08/04-13:51:45.241951 43                       Options.WAL_size_limit_MB: 0
2025/08/04-13:51:45.241952 43                        Options.max_write_batch_group_size_bytes: 1048576
2025/08/04-13:51:45.241953 43             Options.manifest_preallocation_size: 4194304
2025/08/04-13:51:45.241954 43                     Options.is_fd_close_on_exec: 1
2025/08/04-13:51:45.241955 43                   Options.advise_random_on_open: 1
2025/08/04-13:51:45.241956 43                   Options.experimental_mempurge_threshold: 0.000000
2025/08/04-13:51:45.241969 43                    Options.db_write_buffer_size: 0
2025/08/04-13:51:45.241969 43                    Options.write_buffer_manager: 0x7fce984400a0
2025/08/04-13:51:45.241970 43         Options.access_hint_on_compaction_start: 1
2025/08/04-13:51:45.241971 43  Options.new_table_reader_for_compaction_inputs: 0
2025/08/04-13:51:45.241971 43           Options.random_access_max_buffer_size: 1048576
2025/08/04-13:51:45.241972 43                      Options.use_adaptive_mutex: 0
2025/08/04-13:51:45.241972 43                            Options.rate_limiter: (nil)
2025/08/04-13:51:45.241976 43     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/08/04-13:51:45.241976 43                       Options.wal_recovery_mode: 2
2025/08/04-13:51:45.242601 43                  Options.enable_thread_tracking: 0
2025/08/04-13:51:45.242607 43                  Options.enable_pipelined_write: 0
2025/08/04-13:51:45.242607 43                  Options.unordered_write: 0
2025/08/04-13:51:45.242608 43         Options.allow_concurrent_memtable_write: 1
2025/08/04-13:51:45.242609 43      Options.enable_write_thread_adaptive_yield: 1
2025/08/04-13:51:45.242609 43             Options.write_thread_max_yield_usec: 100
2025/08/04-13:51:45.242610 43            Options.write_thread_slow_yield_usec: 3
2025/08/04-13:51:45.242610 43                               Options.row_cache: None
2025/08/04-13:51:45.242611 43                              Options.wal_filter: None
2025/08/04-13:51:45.242612 43             Options.avoid_flush_during_recovery: 0
2025/08/04-13:51:45.242613 43             Options.allow_ingest_behind: 0
2025/08/04-13:51:45.242613 43             Options.preserve_deletes: 0
2025/08/04-13:51:45.242614 43             Options.two_write_queues: 0
2025/08/04-13:51:45.242614 43             Options.manual_wal_flush: 0
2025/08/04-13:51:45.242615 43             Options.atomic_flush: 0
2025/08/04-13:51:45.242615 43             Options.avoid_unnecessary_blocking_io: 0
2025/08/04-13:51:45.242616 43                 Options.persist_stats_to_disk: 0
2025/08/04-13:51:45.242617 43                 Options.write_dbid_to_manifest: 0
2025/08/04-13:51:45.242617 43                 Options.log_readahead_size: 0
2025/08/04-13:51:45.242618 43                 Options.file_checksum_gen_factory: Unknown
2025/08/04-13:51:45.242618 43                 Options.best_efforts_recovery: 0
2025/08/04-13:51:45.242619 43                Options.max_bgerror_resume_count: 2147483647
2025/08/04-13:51:45.242620 43            Options.bgerror_resume_retry_interval: 1000000
2025/08/04-13:51:45.242620 43             Options.allow_data_in_errors: 0
2025/08/04-13:51:45.242621 43             Options.db_host_id: __hostname__
2025/08/04-13:51:45.242625 43             Options.max_background_jobs: 2
2025/08/04-13:51:45.242625 43             Options.max_background_compactions: -1
2025/08/04-13:51:45.242626 43             Options.max_subcompactions: 1
2025/08/04-13:51:45.242627 43             Options.avoid_flush_during_shutdown: 0
2025/08/04-13:51:45.242627 43           Options.writable_file_max_buffer_size: 1048576
2025/08/04-13:51:45.242628 43             Options.delayed_write_rate : 16777216
2025/08/04-13:51:45.242628 43             Options.max_total_wal_size: 0
2025/08/04-13:51:45.242629 43             Options.delete_obsolete_files_period_micros: 21600000000
2025/08/04-13:51:45.242630 43                   Options.stats_dump_period_sec: 600
2025/08/04-13:51:45.242630 43                 Options.stats_persist_period_sec: 600
2025/08/04-13:51:45.242631 43                 Options.stats_history_buffer_size: 1048576
2025/08/04-13:51:45.242631 43                          Options.max_open_files: -1
2025/08/04-13:51:45.242632 43                          Options.bytes_per_sync: 0
2025/08/04-13:51:45.242633 43                      Options.wal_bytes_per_sync: 0
2025/08/04-13:51:45.242633 43                   Options.strict_bytes_per_sync: 0
2025/08/04-13:51:45.242634 43       Options.compaction_readahead_size: 0
2025/08/04-13:51:45.242634 43                  Options.max_background_flushes: 1
2025/08/04-13:51:45.242635 43 Compression algorithms supported:
2025/08/04-13:51:45.242637 43 	kZSTD supported: 1
2025/08/04-13:51:45.242638 43 	kXpressCompression supported: 0
2025/08/04-13:51:45.242639 43 	kBZip2Compression supported: 0
2025/08/04-13:51:45.242639 43 	kZSTDNotFinalCompression supported: 1
2025/08/04-13:51:45.242640 43 	kLZ4Compression supported: 0
2025/08/04-13:51:45.242641 43 	kZlibCompression supported: 0
2025/08/04-13:51:45.242641 43 	kLZ4HCCompression supported: 0
2025/08/04-13:51:45.242642 43 	kSnappyCompression supported: 0
2025/08/04-13:51:45.242647 43 Fast CRC32 supported: Not supported on x86
2025/08/04-13:51:45.248572 43 [db/version_set.cc:4888] Recovering from manifest file: /var/lib/milvus/rdb_data_meta_kv/MANIFEST-000041
2025/08/04-13:51:45.251197 43 [db/column_family.cc:605] --------------- Options for column family [default]:
2025/08/04-13:51:45.251211 43               Options.comparator: leveldb.BytewiseComparator
2025/08/04-13:51:45.251213 43           Options.merge_operator: None
2025/08/04-13:51:45.251215 43        Options.compaction_filter: None
2025/08/04-13:51:45.251216 43        Options.compaction_filter_factory: None
2025/08/04-13:51:45.251216 43  Options.sst_partitioner_factory: None
2025/08/04-13:51:45.251218 43         Options.memtable_factory: SkipListFactory
2025/08/04-13:51:45.251219 43            Options.table_factory: BlockBasedTable
2025/08/04-13:51:45.251259 43            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7fce985000c0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0x7fce98440010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 1001872834
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: (nil)
  persistent_cache: (nil)
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2025/08/04-13:51:45.251265 43        Options.write_buffer_size: 67108864
2025/08/04-13:51:45.251266 43  Options.max_write_buffer_number: 2
2025/08/04-13:51:45.251269 43        Options.compression[0]: NoCompression
2025/08/04-13:51:45.251271 43        Options.compression[1]: NoCompression
2025/08/04-13:51:45.251272 43        Options.compression[2]: ZSTD
2025/08/04-13:51:45.251273 43        Options.compression[3]: ZSTD
2025/08/04-13:51:45.251274 43        Options.compression[4]: ZSTD
2025/08/04-13:51:45.251275 43                  Options.bottommost_compression: Disabled
2025/08/04-13:51:45.251276 43       Options.prefix_extractor: nullptr
2025/08/04-13:51:45.251277 43   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/04-13:51:45.251278 43             Options.num_levels: 5
2025/08/04-13:51:45.251279 43        Options.min_write_buffer_number_to_merge: 1
2025/08/04-13:51:45.251280 43     Options.max_write_buffer_number_to_maintain: 0
2025/08/04-13:51:45.251281 43     Options.max_write_buffer_size_to_maintain: 0
2025/08/04-13:51:45.251282 43            Options.bottommost_compression_opts.window_bits: -14
2025/08/04-13:51:45.251283 43                  Options.bottommost_compression_opts.level: 32767
2025/08/04-13:51:45.251283 43               Options.bottommost_compression_opts.strategy: 0
2025/08/04-13:51:45.251284 43         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/04-13:51:45.251285 43         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/04-13:51:45.251286 43         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/04-13:51:45.251287 43                  Options.bottommost_compression_opts.enabled: false
2025/08/04-13:51:45.251288 43         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/04-13:51:45.251289 43            Options.compression_opts.window_bits: -14
2025/08/04-13:51:45.251291 43                  Options.compression_opts.level: 32767
2025/08/04-13:51:45.251291 43               Options.compression_opts.strategy: 0
2025/08/04-13:51:45.251292 43         Options.compression_opts.max_dict_bytes: 0
2025/08/04-13:51:45.251293 43         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/04-13:51:45.251498 43         Options.compression_opts.parallel_threads: 1
2025/08/04-13:51:45.251505 43                  Options.compression_opts.enabled: false
2025/08/04-13:51:45.251507 43         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/04-13:51:45.251508 43      Options.level0_file_num_compaction_trigger: 4
2025/08/04-13:51:45.251509 43          Options.level0_slowdown_writes_trigger: 20
2025/08/04-13:51:45.251510 43              Options.level0_stop_writes_trigger: 36
2025/08/04-13:51:45.251511 43                   Options.target_file_size_base: 67108864
2025/08/04-13:51:45.251512 43             Options.target_file_size_multiplier: 2
2025/08/04-13:51:45.251513 43                Options.max_bytes_for_level_base: 268435456
2025/08/04-13:51:45.251514 43 Options.level_compaction_dynamic_level_bytes: 0
2025/08/04-13:51:45.251515 43          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/04-13:51:45.251519 43 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/04-13:51:45.251521 43 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/04-13:51:45.251522 43 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/04-13:51:45.251523 43 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/04-13:51:45.251524 43 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/04-13:51:45.251525 43 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/04-13:51:45.251526 43 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/04-13:51:45.251527 43       Options.max_sequential_skip_in_iterations: 8
2025/08/04-13:51:45.251528 43                    Options.max_compaction_bytes: 1677721600
2025/08/04-13:51:45.251529 43                        Options.arena_block_size: 1048576
2025/08/04-13:51:45.251531 43   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/04-13:51:45.251532 43   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/04-13:51:45.251533 43       Options.rate_limit_delay_max_milliseconds: 100
2025/08/04-13:51:45.251533 43                Options.disable_auto_compactions: 0
2025/08/04-13:51:45.251539 43                        Options.compaction_style: kCompactionStyleLevel
2025/08/04-13:51:45.251540 43                          Options.compaction_pri: kMinOverlappingRatio
2025/08/04-13:51:45.251541 43 Options.compaction_options_universal.size_ratio: 1
2025/08/04-13:51:45.251542 43 Options.compaction_options_universal.min_merge_width: 2
2025/08/04-13:51:45.251543 43 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/04-13:51:45.251544 43 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/04-13:51:45.251545 43 Options.compaction_options_universal.compression_size_percent: -1
2025/08/04-13:51:45.251546 43 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/04-13:51:45.251547 43 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/04-13:51:45.251548 43 Options.compaction_options_fifo.allow_compaction: 0
2025/08/04-13:51:45.251558 43                   Options.table_properties_collectors: 
2025/08/04-13:51:45.251559 43                   Options.inplace_update_support: 0
2025/08/04-13:51:45.251560 43                 Options.inplace_update_num_locks: 10000
2025/08/04-13:51:45.251561 43               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/04-13:51:45.251563 43               Options.memtable_whole_key_filtering: 0
2025/08/04-13:51:45.251564 43   Options.memtable_huge_page_size: 0
2025/08/04-13:51:45.251565 43                           Options.bloom_locality: 0
2025/08/04-13:51:45.251566 43                    Options.max_successive_merges: 0
2025/08/04-13:51:45.251567 43                Options.optimize_filters_for_hits: 0
2025/08/04-13:51:45.251568 43                Options.paranoid_file_checks: 0
2025/08/04-13:51:45.251569 43                Options.force_consistency_checks: 1
2025/08/04-13:51:45.251570 43                Options.report_bg_io_stats: 0
2025/08/04-13:51:45.251571 43                               Options.ttl: 2592000
2025/08/04-13:51:45.251845 43          Options.periodic_compaction_seconds: 0
2025/08/04-13:51:45.251851 43                       Options.enable_blob_files: false
2025/08/04-13:51:45.251852 43                           Options.min_blob_size: 0
2025/08/04-13:51:45.251854 43                          Options.blob_file_size: 268435456
2025/08/04-13:51:45.251855 43                   Options.blob_compression_type: NoCompression
2025/08/04-13:51:45.251856 43          Options.enable_blob_garbage_collection: false
2025/08/04-13:51:45.251857 43      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/04-13:51:45.251861 43 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/04-13:51:45.251862 43          Options.blob_compaction_readahead_size: 0
2025/08/04-13:51:45.264122 43 [db/version_set.cc:4928] Recovered from manifest file:/var/lib/milvus/rdb_data_meta_kv/MANIFEST-000041 succeeded,manifest_file_number is 41, next_file_number is 43, last_sequence is 3966, log_number is 37,prev_log_number is 0,max_column_family is 0,min_log_number_to_keep is 0
2025/08/04-13:51:45.264136 43 [db/version_set.cc:4943] Column family [default] (ID 0), log number is 37
2025/08/04-13:51:45.269204 43 [db/version_set.cc:4409] Creating manifest 45
2025/08/04-13:51:45.290722 43 EVENT_LOG_v1 {"time_micros": 1754315505290705, "job": 1, "event": "recovery_started", "wal_files": [42]}
2025/08/04-13:51:45.290731 43 [db/db_impl/db_impl_open.cc:888] Recovering log #42 mode 2
2025/08/04-13:51:45.302283 43 EVENT_LOG_v1 {"time_micros": 1754315505302217, "cf_name": "default", "job": 1, "event": "table_file_creation", "file_number": 46, "file_size": 1031, "file_checksum": "", "file_checksum_func_name": "Unknown", "table_properties": {"data_size": 65, "index_size": 52, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 43, "raw_average_key_size": 43, "raw_value_size": 6, "raw_average_value_size": 6, "num_data_blocks": 1, "num_entries": 1, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; ", "creation_time": 1754315505, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "5d84d4a4-9f27-46dd-b1e4-572f86de492f", "db_session_id": "Q1UPF5RZP4IW6H3R6Y0V", "orig_file_number": 46}}
2025/08/04-13:51:45.302636 43 [db/version_set.cc:4409] Creating manifest 47
2025/08/04-13:51:45.327049 43 EVENT_LOG_v1 {"time_micros": 1754315505327039, "job": 1, "event": "recovery_finished"}
2025/08/04-13:51:45.359367 43 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data_meta_kv/000042.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/08/04-13:51:45.359707 43 [db/db_impl/db_impl_open.cc:1820] SstFileManager instance 0x7fce95480000
2025/08/04-13:51:45.361656 43 DB pointer 0x7fce95a20000
2025/08/04-13:51:45.362243 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/04-13:51:45.362260 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 0.1 total, 0.1 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0    3.73 KB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.1      0.01              0.00         1    0.008       0      0       0.0       0.0
 Sum      3/0    3.73 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.1      0.01              0.00         1    0.008       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.1      0.01              0.00         1    0.008       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.1      0.01              0.00         1    0.008       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 0.1 total, 0.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.01 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.01 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fce98440010#8 capacity: 955.46 MB collections: 1 last_copies: 0 last_secs: 6.5e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/08/04-13:59:10.003349 39 [db/db_impl/db_impl.cc:481] Shutdown: canceling all background work
2025/08/04-13:59:10.010911 39 [db/db_impl/db_impl.cc:699] Shutdown complete
