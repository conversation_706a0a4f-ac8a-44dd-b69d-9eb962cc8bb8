from setuptools import setup, find_packages


with open('requirements.txt') as f:
    requirements = f.read().splitlines()

setup(
    name='py_handler',
    version='0.1',
    packages=find_packages(),
    install_requires=requirements,
    author='',
    author_email='',
    description='工具包',
    long_description='一个用于处理文档转换和学术搜索的工具包',
    long_description_content_type='text/plain',
    url='',
    classifiers=[
        'Programming Language :: Python :: 3',
        'License :: OSI Approved :: MIT License',
        'Operating System :: OS Independent',
    ],
    entry_points={},
)