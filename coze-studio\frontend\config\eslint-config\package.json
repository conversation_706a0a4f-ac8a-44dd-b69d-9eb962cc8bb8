{"name": "@coze-arch/eslint-config", "version": "0.0.1", "author": "<EMAIL>", "maintainers": [], "main": "src/index.js", "bin": {"eslint": "./scripts/reslint.sh", "prettier": "./scripts/rprettier.sh", "reslint": "./scripts/reslint.sh"}, "scripts": {"build": "exit 0", "dev": "npm run build -- -w", "lint": "eslint ./ --cache --quiet", "test": "exit", "test:cov": "exit"}, "dependencies": {"@babel/eslint-parser": "~7.25.8", "@babel/eslint-plugin": "~7.25.7", "@babel/plugin-proposal-async-generator-functions": "~7.20.7", "@babel/plugin-proposal-class-properties": "~7.18.6", "@babel/plugin-proposal-decorators": "~7.23.6", "@babel/plugin-proposal-do-expressions": "~7.23.3", "@babel/plugin-proposal-export-default-from": "~7.23.3", "@babel/plugin-proposal-export-namespace-from": "~7.18.9", "@babel/plugin-proposal-function-bind": "~7.23.3", "@babel/plugin-proposal-nullish-coalescing-operator": "~7.18.6", "@babel/plugin-proposal-object-rest-spread": "~7.20.7", "@babel/plugin-proposal-optional-catch-binding": "~7.18.6", "@babel/plugin-proposal-optional-chaining": "~7.21.0", "@babel/plugin-proposal-pipeline-operator": "~7.23.3", "@babel/plugin-syntax-dynamic-import": "~7.8.3", "@babel/plugin-syntax-jsx": "~7.23.3", "@babel/preset-env": "~7.20.2", "@babel/preset-react": "~7.13.13", "@coze-arch/eslint-plugin": "workspace:*", "@coze-arch/ts-config": "workspace:*", "@eslint/compat": "~1.2.0", "@eslint/eslintrc": "~3.1.0", "@rushstack/eslint-config": "~3.1.1", "@stylistic/eslint-plugin-ts": "^2.8.0", "@typescript-eslint/eslint-plugin": "^8.5.0", "@typescript-eslint/parser": "~8.17.0", "eslint": "~9.12.0", "eslint-config-airbnb": "^18.0.1", "eslint-config-prettier": "~9.1.0", "eslint-config-react-app": "^7", "eslint-define-config": "~1.12.0", "eslint-import-resolver-typescript": "^3", "eslint-plugin-babel": "^5.3.1", "eslint-plugin-cypress": "^2.6.1", "eslint-plugin-eslint-comments": "~3.2.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-jsx-a11y": "^6.2.3", "eslint-plugin-prettier": "~5.2.1", "eslint-plugin-promise": "^5.1.0", "eslint-plugin-react": "~7.37.1", "eslint-plugin-react-hooks": "5.1.0-beta-26f2496093-20240514", "eslint-plugin-redux-saga": "^1.1.0", "eslint-plugin-risxss": "~2.1.0", "eslint-plugin-security": "3.0.1", "eslint-plugin-unicorn": "48.0.1", "globals": "~15.11.0", "json5": "^2.2.1", "prettier": "~3.3.3", "prettier-plugin-packagejson": "^2.3.0", "sucrase": "^3.32.0"}, "devDependencies": {"@types/eslint": "~9.6.1", "@types/node": "^18", "typescript": "~5.8.2"}}