# coding: utf-8

"""
    privatelink

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreatePrivateLinkGatewayRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'client_token': 'str',
        'description': 'str',
        'private_link_gateway_name': 'str',
        'security_group_ids': 'list[str]',
        'subnet_id': 'str',
        'vpc_id': 'str',
        'zone_id': 'str'
    }

    attribute_map = {
        'client_token': 'ClientToken',
        'description': 'Description',
        'private_link_gateway_name': 'PrivateLinkGatewayName',
        'security_group_ids': 'SecurityGroupIds',
        'subnet_id': 'SubnetId',
        'vpc_id': 'VpcId',
        'zone_id': 'ZoneId'
    }

    def __init__(self, client_token=None, description=None, private_link_gateway_name=None, security_group_ids=None, subnet_id=None, vpc_id=None, zone_id=None, _configuration=None):  # noqa: E501
        """CreatePrivateLinkGatewayRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._client_token = None
        self._description = None
        self._private_link_gateway_name = None
        self._security_group_ids = None
        self._subnet_id = None
        self._vpc_id = None
        self._zone_id = None
        self.discriminator = None

        if client_token is not None:
            self.client_token = client_token
        if description is not None:
            self.description = description
        if private_link_gateway_name is not None:
            self.private_link_gateway_name = private_link_gateway_name
        if security_group_ids is not None:
            self.security_group_ids = security_group_ids
        self.subnet_id = subnet_id
        self.vpc_id = vpc_id
        self.zone_id = zone_id

    @property
    def client_token(self):
        """Gets the client_token of this CreatePrivateLinkGatewayRequest.  # noqa: E501


        :return: The client_token of this CreatePrivateLinkGatewayRequest.  # noqa: E501
        :rtype: str
        """
        return self._client_token

    @client_token.setter
    def client_token(self, client_token):
        """Sets the client_token of this CreatePrivateLinkGatewayRequest.


        :param client_token: The client_token of this CreatePrivateLinkGatewayRequest.  # noqa: E501
        :type: str
        """

        self._client_token = client_token

    @property
    def description(self):
        """Gets the description of this CreatePrivateLinkGatewayRequest.  # noqa: E501


        :return: The description of this CreatePrivateLinkGatewayRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CreatePrivateLinkGatewayRequest.


        :param description: The description of this CreatePrivateLinkGatewayRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def private_link_gateway_name(self):
        """Gets the private_link_gateway_name of this CreatePrivateLinkGatewayRequest.  # noqa: E501


        :return: The private_link_gateway_name of this CreatePrivateLinkGatewayRequest.  # noqa: E501
        :rtype: str
        """
        return self._private_link_gateway_name

    @private_link_gateway_name.setter
    def private_link_gateway_name(self, private_link_gateway_name):
        """Sets the private_link_gateway_name of this CreatePrivateLinkGatewayRequest.


        :param private_link_gateway_name: The private_link_gateway_name of this CreatePrivateLinkGatewayRequest.  # noqa: E501
        :type: str
        """

        self._private_link_gateway_name = private_link_gateway_name

    @property
    def security_group_ids(self):
        """Gets the security_group_ids of this CreatePrivateLinkGatewayRequest.  # noqa: E501


        :return: The security_group_ids of this CreatePrivateLinkGatewayRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._security_group_ids

    @security_group_ids.setter
    def security_group_ids(self, security_group_ids):
        """Sets the security_group_ids of this CreatePrivateLinkGatewayRequest.


        :param security_group_ids: The security_group_ids of this CreatePrivateLinkGatewayRequest.  # noqa: E501
        :type: list[str]
        """

        self._security_group_ids = security_group_ids

    @property
    def subnet_id(self):
        """Gets the subnet_id of this CreatePrivateLinkGatewayRequest.  # noqa: E501


        :return: The subnet_id of this CreatePrivateLinkGatewayRequest.  # noqa: E501
        :rtype: str
        """
        return self._subnet_id

    @subnet_id.setter
    def subnet_id(self, subnet_id):
        """Sets the subnet_id of this CreatePrivateLinkGatewayRequest.


        :param subnet_id: The subnet_id of this CreatePrivateLinkGatewayRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and subnet_id is None:
            raise ValueError("Invalid value for `subnet_id`, must not be `None`")  # noqa: E501

        self._subnet_id = subnet_id

    @property
    def vpc_id(self):
        """Gets the vpc_id of this CreatePrivateLinkGatewayRequest.  # noqa: E501


        :return: The vpc_id of this CreatePrivateLinkGatewayRequest.  # noqa: E501
        :rtype: str
        """
        return self._vpc_id

    @vpc_id.setter
    def vpc_id(self, vpc_id):
        """Sets the vpc_id of this CreatePrivateLinkGatewayRequest.


        :param vpc_id: The vpc_id of this CreatePrivateLinkGatewayRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and vpc_id is None:
            raise ValueError("Invalid value for `vpc_id`, must not be `None`")  # noqa: E501

        self._vpc_id = vpc_id

    @property
    def zone_id(self):
        """Gets the zone_id of this CreatePrivateLinkGatewayRequest.  # noqa: E501


        :return: The zone_id of this CreatePrivateLinkGatewayRequest.  # noqa: E501
        :rtype: str
        """
        return self._zone_id

    @zone_id.setter
    def zone_id(self, zone_id):
        """Sets the zone_id of this CreatePrivateLinkGatewayRequest.


        :param zone_id: The zone_id of this CreatePrivateLinkGatewayRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and zone_id is None:
            raise ValueError("Invalid value for `zone_id`, must not be `None`")  # noqa: E501

        self._zone_id = zone_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreatePrivateLinkGatewayRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreatePrivateLinkGatewayRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreatePrivateLinkGatewayRequest):
            return True

        return self.to_dict() != other.to_dict()
