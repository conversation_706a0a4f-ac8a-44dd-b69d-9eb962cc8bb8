// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameConversation = "conversation"

// Conversation conversation info record
type Conversation struct {
	ID          int64  `gorm:"column:id;primaryKey;autoIncrement:true;comment:id" json:"id"`                                          // id
	ConnectorID int64  `gorm:"column:connector_id;not null;comment:Publish Connector ID" json:"connector_id"`                         // Publish Connector ID
	AgentID     int64  `gorm:"column:agent_id;not null;comment:agent_id" json:"agent_id"`                                             // agent_id
	Scene       int32  `gorm:"column:scene;not null;comment:conversation scene" json:"scene"`                                         // conversation scene
	SectionID   int64  `gorm:"column:section_id;not null;comment:section_id" json:"section_id"`                                       // section_id
	CreatorID   int64  `gorm:"column:creator_id;comment:creator_id" json:"creator_id"`                                                // creator_id
	Ext         string `gorm:"column:ext;comment:ext" json:"ext"`                                                                     // ext
	Status      int32  `gorm:"column:status;not null;default:1;comment:status: 1-normal 2-deleted" json:"status"`                     // status: 1-normal 2-deleted
	CreatedAt   int64  `gorm:"column:created_at;not null;autoCreateTime:milli;comment:Create Time in Milliseconds" json:"created_at"` // Create Time in Milliseconds
	UpdatedAt   int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli;comment:Update Time in Milliseconds" json:"updated_at"` // Update Time in Milliseconds
}

// TableName Conversation's table name
func (*Conversation) TableName() string {
	return TableNameConversation
}
