/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// Code generated by hertz generator.

package coze

import (
	"context"
	"io"
	"net/http"
	"strings"

	"github.com/cloudwego/hertz/pkg/app"
	"github.com/cloudwego/hertz/pkg/protocol"

	"github.com/coze-dev/coze-studio/backend/api/model/passport"
	"github.com/coze-dev/coze-studio/backend/application/user"
	"github.com/coze-dev/coze-studio/backend/domain/user/entity"
	"github.com/coze-dev/coze-studio/backend/pkg/hertzutil/domain"
	"github.com/coze-dev/coze-studio/backend/pkg/i18n"
	"github.com/coze-dev/coze-studio/backend/pkg/logs"
	"github.com/coze-dev/coze-studio/backend/types/consts"
)

// PassportWebEmailRegisterV2Post .
// @router /passport/web/email/register/v2/ [POST]
func PassportWebEmailRegisterV2Post(ctx context.Context, c *app.RequestContext) {
	var err error
	var req passport.PassportWebEmailRegisterV2PostRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	locale := string(i18n.GetLocale(ctx))

	resp, sessionKey, err := user.UserApplicationSVC.PassportWebEmailRegisterV2(ctx, locale, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.SetCookie(entity.SessionKey,
		sessionKey,
		consts.SessionMaxAgeSecond,
		"/", domain.GetOriginHost(c),
		protocol.CookieSameSiteDefaultMode,
		false, true)

	c.JSON(http.StatusOK, resp)
}

// PassportWebLogoutGet .
// @router /passport/web/logout/ [GET]
func PassportWebLogoutGet(ctx context.Context, c *app.RequestContext) {
	var err error
	var req passport.PassportWebLogoutGetRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(http.StatusBadRequest, err.Error())
		return
	}

	resp, err := user.UserApplicationSVC.PassportWebLogoutGet(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(http.StatusOK, resp)
}

// PassportWebEmailLoginPost .
// @router /passport/web/email/login/ [POST]
func PassportWebEmailLoginPost(ctx context.Context, c *app.RequestContext) {
	var err error
	var req passport.PassportWebEmailLoginPostRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(http.StatusBadRequest, err.Error())
		return
	}

	resp, sessionKey, err := user.UserApplicationSVC.PassportWebEmailLoginPost(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	logs.Infof("[PassportWebEmailLoginPost] sessionKey: %s", sessionKey)

	c.SetCookie(entity.SessionKey,
		sessionKey,
		consts.SessionMaxAgeSecond,
		"/", domain.GetOriginHost(c),
		protocol.CookieSameSiteDefaultMode,
		false, true)
	c.JSON(http.StatusOK, resp)
}

// PassportWebEmailPasswordResetGet .
// @router /passport/web/email/password/reset/ [GET]
func PassportWebEmailPasswordResetGet(ctx context.Context, c *app.RequestContext) {
	var err error
	var req passport.PassportWebEmailPasswordResetGetRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(http.StatusBadRequest, err.Error())
		return
	}

	resp, err := user.UserApplicationSVC.PassportWebEmailPasswordResetGet(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(http.StatusOK, resp)
}

// PassportAccountInfoV2 .
// @router /passport/account/info/v2/ [POST]
func PassportAccountInfoV2(ctx context.Context, c *app.RequestContext) {
	var err error
	var req passport.PassportAccountInfoV2Request
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(http.StatusBadRequest, err.Error())
		return
	}

	resp, err := user.UserApplicationSVC.PassportAccountInfoV2(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(http.StatusOK, resp)
}

// UserUpdateAvatar .
// @router web/user/update/upload_avatar/ [POST]
func UserUpdateAvatar(ctx context.Context, c *app.RequestContext) {
	var err error
	var req passport.UserUpdateAvatarRequest

	// Get the uploaded file
	file, err := c.FormFile("avatar")
	if err != nil {
		logs.CtxErrorf(ctx, "Get Avatar Fail failed, err=%v", err)
		invalidParamRequestResponse(c, "missing avatar file")
		return
	}

	// Check file type
	if !strings.HasPrefix(file.Header.Get("Content-Type"), "image/") {
		invalidParamRequestResponse(c, "invalid file type, only image allowed")
		return
	}

	// Read file content
	src, err := file.Open()
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}
	defer src.Close()

	fileContent, err := io.ReadAll(src)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	req.Avatar = fileContent
	mimeType := file.Header.Get("Content-Type")

	resp, err := user.UserApplicationSVC.UserUpdateAvatar(ctx, mimeType, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(http.StatusOK, resp)
}

// UserUpdateProfile .
// @router api/user/update_profile [POST]
func UserUpdateProfile(ctx context.Context, c *app.RequestContext) {
	var err error
	var req passport.UserUpdateProfileRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(http.StatusBadRequest, err.Error())
		return
	}

	resp, err := user.UserApplicationSVC.UserUpdateProfile(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(http.StatusOK, resp)
}
