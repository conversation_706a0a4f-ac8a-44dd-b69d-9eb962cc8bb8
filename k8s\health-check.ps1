# 健康检查脚本
param(
    [string]$Namespace = "paper-services"
)

Write-Host "=== Paper Services 健康检查 ===" -ForegroundColor Green

# 检查命名空间是否存在
$nsExists = kubectl get namespace $Namespace 2>$null
if (!$nsExists) {
    Write-Host "❌ 命名空间 $Namespace 不存在" -ForegroundColor Red
    exit 1
}

Write-Host "✓ 命名空间 $Namespace 存在" -ForegroundColor Green

# 检查 Pod 状态
Write-Host "`n📊 Pod 状态检查:" -ForegroundColor Cyan
$pods = kubectl get pods -n $Namespace -o json | ConvertFrom-Json

foreach ($pod in $pods.items) {
    $name = $pod.metadata.name
    $status = $pod.status.phase
    $ready = $pod.status.containerStatuses[0].ready
    
    if ($status -eq "Running" -and $ready) {
        Write-Host "✓ $name - 运行正常" -ForegroundColor Green
    } elseif ($status -eq "Pending") {
        Write-Host "⏳ $name - 启动中..." -ForegroundColor Yellow
    } else {
        Write-Host "❌ $name - 状态异常: $status" -ForegroundColor Red
    }
}

# 检查服务状态
Write-Host "`n🌐 服务状态检查:" -ForegroundColor Cyan
$services = @(
    @{Name="lunwen-generate-ui"; Port=30000; Type="NodePort"},
    @{Name="dify-web"; Port=30001; Type="NodePort"},
    @{Name="paper-editor-api"; Port=8890; Type="ClusterIP"},
    @{Name="dify-api"; Port=5001; Type="ClusterIP"},
    @{Name="paper-node-service"; Port=9529; Type="ClusterIP"},
    @{Name="paper-py-service"; Port=9528; Type="ClusterIP"}
)

foreach ($svc in $services) {
    $svcInfo = kubectl get svc $svc.Name -n $Namespace -o json 2>$null | ConvertFrom-Json
    if ($svcInfo) {
        Write-Host "✓ $($svc.Name) - 服务可用 ($($svc.Type):$($svc.Port))" -ForegroundColor Green
    } else {
        Write-Host "❌ $($svc.Name) - 服务不可用" -ForegroundColor Red
    }
}

# 检查外部访问
Write-Host "`n🔗 外部访问检查:" -ForegroundColor Cyan
try {
    $response = Invoke-WebRequest -Uri "http://localhost:30000" -TimeoutSec 5 -UseBasicParsing
    Write-Host "✓ 论文生成系统 (http://localhost:30000) - 可访问" -ForegroundColor Green
} catch {
    Write-Host "❌ 论文生成系统 (http://localhost:30000) - 无法访问" -ForegroundColor Red
}

try {
    $response = Invoke-WebRequest -Uri "http://localhost:30001" -TimeoutSec 5 -UseBasicParsing
    Write-Host "✓ Dify AI 平台 (http://localhost:30001) - 可访问" -ForegroundColor Green
} catch {
    Write-Host "❌ Dify AI 平台 (http://localhost:30001) - 无法访问" -ForegroundColor Red
}

Write-Host "`n📈 资源使用情况:" -ForegroundColor Cyan
kubectl top pods -n $Namespace 2>$null

Write-Host "`n💡 如果发现问题，请使用以下命令进行详细诊断:" -ForegroundColor Yellow
Write-Host "kubectl describe pods -n $Namespace" -ForegroundColor White
Write-Host "kubectl logs -f <pod-name> -n $Namespace" -ForegroundColor White
