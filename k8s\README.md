# Paper Services Kubernetes 本地部署指南

## 🏗️ 架构概述

这是一个采用**服务隔离架构**的完整 Kubernetes 微服务部署方案，每个服务都有独立的基础设施：

### 📊 服务列表

| 服务名称 | 技术栈 | 端口 | 功能描述 | 外部访问 |
|---------|--------|------|----------|----------|
| lunwen-generate-ui | Next.js | 3000 | 论文生成前端界面 | :30000 |
| dify-web | Next.js | 3000 | Dify AI 平台前端 | :30001 |
| paper-editor-api | Go | 8890 | 主 API 服务 | 内部 |
| dify-api | Python | 5001 | Dify AI API 服务 | 内部 |
| paper-node-service | Node.js | 9529 | Node.js 工具服务 | 内部 |
| paper-py-service | Python | 9528 | Python 分析服务 | 内部 |

### 🔒 服务隔离基础设施

每个服务都有独立的数据库和缓存：

```
┌─────────────────────────────────────────────────────────────┐
│                    应用服务层                                │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │paper-editor │ │lunwen-ui    │ │dify-api     │ │dify-web │ │
│  │    :8890    │ │   :3000     │ │   :5001     │ │  :3000  │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
│           │              │              │              │     │
├─────────────────────────────────────────────────────────────┤
│                   独立基础设施层                             │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │paper-editor │ │paper-editor │ │dify-postgres│ │dify-redis│ │
│  │ postgres    │ │   redis     │ │   :5432     │ │  :6379  │ │
│  │   :5432     │ │   :6379     │ │             │ │         │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
│  ┌─────────────┐ ┌─────────────┐                             │
│  │paper-node   │ │paper-py     │                             │
│  │temp-storage │ │data-storage │                             │
│  └─────────────┘ └─────────────┘                             │
└─────────────────────────────────────────────────────────────┘
```

## 前置要求

### 1. 安装 Docker Desktop
- 下载并安装 [Docker Desktop](https://www.docker.com/products/docker-desktop/)
- 启用 Kubernetes 功能：Settings → Kubernetes → Enable Kubernetes

### 2. 验证环境
```powershell
# 检查 Docker
docker --version

# 检查 Kubernetes
kubectl version --client
kubectl cluster-info
```

## 🚀 快速开始

### 一键启动（推荐）
```powershell
# 进入 k8s 目录
cd k8s

# 构建镜像并部署服务隔离架构
.\deploy.ps1 -Build
```

### 分步操作
```powershell
# 1. 仅构建镜像
.\build-images.ps1

# 2. 部署服务（镜像已存在）
.\deploy.ps1

# 3. 检查服务健康状态
.\health-check.ps1
```

### 清理资源
```powershell
# 清理部署（保留存储）
.\cleanup.ps1

# 完全清理（包括存储）
.\cleanup.ps1 -All
```

## 服务架构

### 服务间通信
```
lunwen-generate-ui (论文前端) ←→ paper-editor-api (主API)
dify-web (AI平台前端) ←→ dify-api (AI API)
                              ↓
paper-editor-api ←→ paper-node-service (Node.js工具)
                ←→ paper-py-service (Python分析)
                ←→ dify-api (AI能力)
                              ↓
                    postgres + redis (基础设施)
```

### 🌐 访问地址
- **外部访问**:
  - 📝 论文生成系统: http://localhost:30000
  - 🤖 Dify AI 平台: http://localhost:30001

### 🔗 服务隔离通信
每个服务使用独立的基础设施：

| 服务 | 数据库 | 缓存 | 存储 |
|------|--------|------|------|
| paper-editor-api | paper-editor-postgres:5432 | paper-editor-redis:6379 | paper-editor-uploads-pvc |
| dify-api | dify-postgres:5432 | dify-redis:6379 | dify-storage-pvc |
| paper-node-service | 无 | 无 | paper-node-temp-pvc |
| paper-py-service | 无 | 无 | paper-py-data-pvc |

### ✅ 服务隔离优势
- 🔒 **数据完全隔离**: 每个服务有独立的数据库实例
- 📈 **独立扩展**: 可以根据服务需求独立扩展基础设施
- 🛡️ **故障隔离**: 一个服务的数据库问题不影响其他服务
- 🔧 **技术灵活性**: 不同服务可以选择最适合的数据库类型

## 常用命令

### 查看状态
```powershell
# 查看所有 Pod
kubectl get pods -n paper-services

# 查看服务
kubectl get svc -n paper-services

# 查看详细信息
kubectl describe pod <pod-name> -n paper-services
```

### 查看日志
```powershell
# 查看实时日志
kubectl logs -f <pod-name> -n paper-services

# 查看所有容器日志
kubectl logs -f deployment/paper-editor-api-deployment -n paper-services
```

### 调试
```powershell
# 进入容器
kubectl exec -it <pod-name> -n paper-services -- /bin/sh

# 端口转发（用于调试）
kubectl port-forward svc/paper-editor-api 8890:8890 -n paper-services
```

## 故障排除

### 1. Pod 启动失败
```powershell
# 查看 Pod 事件
kubectl describe pod <pod-name> -n paper-services

# 查看日志
kubectl logs <pod-name> -n paper-services
```

### 2. 镜像拉取失败
确保镜像已正确构建：
```powershell
docker images | findstr "paper-\|lunwen-"
```

### 3. 服务无法访问
检查服务和端点：
```powershell
kubectl get endpoints -n paper-services
```

## 开发模式

如果需要在开发过程中快速重新部署某个服务：

```powershell
# 重新构建并部署单个服务
docker build -t paper-editor-api:latest ../paper-editor-api
kubectl rollout restart deployment/paper-editor-api-deployment -n paper-services
```

## 配置说明

- **存储**: 使用 PersistentVolumeClaim 进行数据持久化
- **网络**: 服务间通过 ClusterIP 进行内部通信
- **配置**: 通过 ConfigMap 管理配置文件
- **外部访问**: 通过 NodePort 暴露前端服务
