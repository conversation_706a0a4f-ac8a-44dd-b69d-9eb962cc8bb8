# Paper Services Kubernetes 本地部署指南

## 概述

这是一个完整的本地 Kubernetes 微服务部署方案，包含以下服务：

- **paper-editor-api** (Go) - 主 API 服务，端口 8890
- **lunwen-generate-ui** (Next.js) - 前端界面，端口 3000
- **paper-node-service** (Node.js) - Node.js 工具服务，端口 9529
- **paper-py-service** (Python Flask) - Python 分析服务，端口 9528

## 前置要求

### 1. 安装 Docker Desktop
- 下载并安装 [Docker Desktop](https://www.docker.com/products/docker-desktop/)
- 启用 Kubernetes 功能：Settings → Kubernetes → Enable Kubernetes

### 2. 验证环境
```powershell
# 检查 Docker
docker --version

# 检查 Kubernetes
kubectl version --client
kubectl cluster-info
```

## 快速开始

### 1. 构建镜像并部署
```powershell
# 进入 k8s 目录
cd k8s

# 构建所有镜像并部署
.\deploy.ps1 -Build
```

### 2. 仅部署（镜像已存在）
```powershell
.\deploy.ps1
```

### 3. 清理部署
```powershell
# 清理部署但保留命名空间和存储
.\cleanup.ps1

# 完全清理（包括命名空间和存储）
.\cleanup.ps1 -All
```

## 服务架构

### 服务间通信
```
lunwen-generate-ui (前端)
    ↓ HTTP API 调用
paper-editor-api (主 API 网关)
    ↓ 内部服务调用
paper-node-service (Node.js 工具)
paper-py-service (Python 分析)
```

### 端口映射
- **外部访问**: http://localhost:30000 (前端 UI)
- **内部服务通信**: 通过 Kubernetes Service 名称
  - `paper-editor-api:8890`
  - `paper-node-service:9529`
  - `paper-py-service:9528`

## 常用命令

### 查看状态
```powershell
# 查看所有 Pod
kubectl get pods -n paper-services

# 查看服务
kubectl get svc -n paper-services

# 查看详细信息
kubectl describe pod <pod-name> -n paper-services
```

### 查看日志
```powershell
# 查看实时日志
kubectl logs -f <pod-name> -n paper-services

# 查看所有容器日志
kubectl logs -f deployment/paper-editor-api-deployment -n paper-services
```

### 调试
```powershell
# 进入容器
kubectl exec -it <pod-name> -n paper-services -- /bin/sh

# 端口转发（用于调试）
kubectl port-forward svc/paper-editor-api 8890:8890 -n paper-services
```

## 故障排除

### 1. Pod 启动失败
```powershell
# 查看 Pod 事件
kubectl describe pod <pod-name> -n paper-services

# 查看日志
kubectl logs <pod-name> -n paper-services
```

### 2. 镜像拉取失败
确保镜像已正确构建：
```powershell
docker images | findstr "paper-\|lunwen-"
```

### 3. 服务无法访问
检查服务和端点：
```powershell
kubectl get endpoints -n paper-services
```

## 开发模式

如果需要在开发过程中快速重新部署某个服务：

```powershell
# 重新构建并部署单个服务
docker build -t paper-editor-api:latest ../paper-editor-api
kubectl rollout restart deployment/paper-editor-api-deployment -n paper-services
```

## 配置说明

- **存储**: 使用 PersistentVolumeClaim 进行数据持久化
- **网络**: 服务间通过 ClusterIP 进行内部通信
- **配置**: 通过 ConfigMap 管理配置文件
- **外部访问**: 通过 NodePort 暴露前端服务
