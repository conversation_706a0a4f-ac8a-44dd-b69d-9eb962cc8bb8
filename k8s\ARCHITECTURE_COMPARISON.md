# 🏗️ K8s 架构设计对比：共享 vs 隔离

## 📊 两种架构对比

### 🔄 共享基础设施架构（原设计）

```
┌─────────────────────────────────────────────────────────────┐
│                    应用服务层                                │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │paper-editor │ │lunwen-ui    │ │dify-api     │ │dify-web │ │
│  │    :8890    │ │   :3000     │ │   :5001     │ │  :3000  │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
│           │              │              │              │     │
│           └──────────────┼──────────────┼──────────────┘     │
├─────────────────────────────────────────────────────────────┤
│                   共享基础设施层                             │
│  ┌─────────────────────────┐ ┌─────────────────────────┐     │
│  │    PostgreSQL :5432     │ │      Redis :6379        │     │
│  │   (所有服务共享)         │ │    (所有服务共享)        │     │
│  └─────────────────────────┘ └─────────────────────────┘     │
└─────────────────────────────────────────────────────────────┘
```

**优点：**
- 资源使用效率高
- 部署简单，管理方便
- 适合小型项目或开发环境

**缺点：**
- 数据耦合，不符合微服务原则
- 单点故障风险
- 扩展性差，无法独立扩展
- 数据安全隔离不足

### 🔒 服务隔离架构（推荐设计）

```
┌─────────────────────────────────────────────────────────────┐
│                    应用服务层                                │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │paper-editor │ │lunwen-ui    │ │dify-api     │ │dify-web │ │
│  │    :8890    │ │   :3000     │ │   :5001     │ │  :3000  │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
│           │              │              │              │     │
├─────────────────────────────────────────────────────────────┤
│                   独立基础设施层                             │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │paper-editor │ │paper-editor │ │dify-postgres│ │dify-redis│ │
│  │ postgres    │ │   redis     │ │   :5432     │ │  :6379  │ │
│  │   :5432     │ │   :6379     │ │             │ │         │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
│  ┌─────────────┐ ┌─────────────┐                             │
│  │paper-node   │ │paper-py     │                             │
│  │temp-storage │ │data-storage │                             │
│  └─────────────┘ └─────────────┘                             │
└─────────────────────────────────────────────────────────────┘
```

**优点：**
- ✅ 真正的微服务架构
- ✅ 数据完全隔离
- ✅ 独立扩展能力
- ✅ 故障隔离
- ✅ 技术栈灵活性

**缺点：**
- 资源消耗更多
- 管理复杂度增加
- 需要更多存储空间

## 🚀 使用方式

### 共享架构部署
```powershell
# 使用共享基础设施
.\deploy.ps1
```

### 隔离架构部署（推荐）
```powershell
# 使用服务隔离架构
.\deploy-isolated.ps1
```

## 📋 服务对应关系

| 服务 | 共享架构数据库 | 隔离架构数据库 | 隔离架构缓存 |
|------|---------------|---------------|-------------|
| paper-editor-api | postgres:5432 | paper-editor-postgres:5432 | paper-editor-redis:6379 |
| dify-api | postgres:5432 | dify-postgres:5432 | dify-redis:6379 |
| paper-node-service | 无数据库 | 无数据库 | paper-node-temp-pvc |
| paper-py-service | 无数据库 | 无数据库 | paper-py-data-pvc |

## 🎯 选择建议

### 选择共享架构的场景：
- 🧪 **开发和测试环境**
- 💻 **本地开发体验**
- 📦 **资源受限环境**
- 🚀 **快速原型验证**

### 选择隔离架构的场景：
- 🏭 **生产环境**
- 📈 **需要独立扩展的服务**
- 🔒 **数据安全要求高**
- 🎯 **真正的微服务架构**

## 🔧 配置文件对比

### 共享架构文件：
- `infrastructure.yaml` - 共享的 PostgreSQL 和 Redis
- `storage-persistentvolumeclaim.yaml` - 共享存储
- `deploy.ps1` - 共享架构部署脚本

### 隔离架构文件：
- `infrastructure-isolated.yaml` - 每个服务独立的基础设施
- `storage-isolated.yaml` - 服务隔离的存储
- `deploy-isolated.ps1` - 隔离架构部署脚本

## 💡 最佳实践建议

1. **开发阶段**: 使用共享架构快速迭代
2. **测试阶段**: 使用隔离架构验证服务独立性
3. **生产环境**: 必须使用隔离架构
4. **监控**: 为每个服务配置独立的监控和日志
5. **备份**: 为每个数据库实例配置独立的备份策略
