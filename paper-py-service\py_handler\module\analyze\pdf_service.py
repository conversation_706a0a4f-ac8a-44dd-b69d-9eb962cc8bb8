import tempfile
import os
from typing import List, Dict, Any, <PERSON>ple
import logging
import fitz  # PyMuPDF
import numpy as np
from PIL import Image
import io
import pytesseract

logger = logging.getLogger(__name__)


def is_pdf_editable(pdf_data: bytes) -> Tuple[bool, int]:
    """检查PDF是否可编辑

    Returns:
        Tuple[bool, int]: (是否可编辑, 文本块总数)
    """
    try:
        with fitz.open(stream=pdf_data, filetype="pdf") as doc:
            total_blocks = 0
            for page in doc:
                # 获取页面上的文本块
                blocks = page.get_text("dict")["blocks"]
                total_blocks += len(blocks)

                # 如果找到文本块，说明是可编辑的
                if total_blocks > 0:
                    logger.info(f"PDF可编辑，包含 {total_blocks} 个文本块")
                    return True, total_blocks

            logger.info("PDF不可编辑，没有找到文本块")
            return False, 0

    except Exception as e:
        logger.error(f"检查PDF可编辑性时出错: {e}")
        return False, 0


def is_red_pixel(r: int, g: int, b: int) -> bool:
    """判断像素是否为红色"""
    return r > 150 and r > (g + b) * 1.5


def get_text_mask(img: Image.Image) -> np.ndarray:
    """获取文本区域的掩码"""
    gray = img.convert("L")
    return np.array(gray) < 200


def get_red_regions(img: Image.Image) -> np.ndarray:
    """获取红色区域的掩码"""
    rgb = np.array(img)
    r, g, b = rgb[:, :, 0], rgb[:, :, 1], rgb[:, :, 2]
    return (r > 150) & (r > (g + b) * 1.5)


def extract_text_with_ocr(pdf_data: bytes) -> List[str]:
    """使用OCR提取红色文本"""
    logger.info("使用OCR提取文本")
    red_texts = []

    try:
        # 使用PyMuPDF打开PDF
        with fitz.open(stream=pdf_data, filetype="pdf") as doc:
            logger.info(f"PDF页数: {len(doc)}")

            for i, page in enumerate(doc):
                logger.info(f"OCR处理第 {i+1} 页")

                # 将页面渲染为图片
                pix = page.get_pixmap(matrix=fitz.Matrix(2, 2))  # 2x缩放以提高清晰度
                img = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)

                # 获取文本区域和红色区域的掩码
                text_mask = get_text_mask(img)
                red_mask = get_red_regions(img)

                # 结合两个掩码得到红色文本区域
                red_text_mask = text_mask & red_mask

                if not np.any(red_text_mask):
                    logger.info(f"第 {i+1} 页没有找到红色文本区域")
                    continue

                # 创建一个新图片，只保留红色文本区域
                masked_img = Image.fromarray(np.uint8(red_text_mask) * 255)

                try:
                    # 使用tesseract进行OCR
                    text = pytesseract.image_to_string(masked_img, lang="chi_sim+eng")

                    if text.strip():
                        # 分行处理提取的文本
                        for line in text.split("\n"):
                            if line.strip():
                                logger.info(f"OCR找到红色文本: {line.strip()}")
                                red_texts.append(line.strip())
                except Exception as e:
                    logger.error(f"OCR处理失败: {e}")
                    if "tesseract is not installed" in str(e):
                        logger.error("请安装Tesseract OCR并确保其在系统PATH中")
                        break

        return red_texts
    except Exception as e:
        logger.error(f"OCR处理出错: {e}")
        import traceback

        logger.error(traceback.format_exc())
        return []


def extract_text_directly(pdf_data: bytes) -> List[str]:
    """直接从PDF提取文本"""
    logger.info("直接从PDF提取文本")
    red_texts = []

    try:
        # 从内存中加载PDF
        with fitz.open(stream=pdf_data, filetype="pdf") as doc:
            logger.info(f"PDF页数: {len(doc)}")

            for page_num in range(len(doc)):
                page = doc[page_num]
                logger.info(f"处理第 {page_num + 1} 页")

                # 获取页面上的文本块
                blocks = page.get_text("dict")["blocks"]

                for block in blocks:
                    if "lines" in block:
                        for line in block["lines"]:
                            for span in line["spans"]:
                                # 获取文本颜色
                                color = span.get("color")
                                if color:
                                    logger.info(
                                        f"找到带颜色的文本: {span['text']}, 颜色: {color}"
                                    )

                                    # PyMuPDF中颜色是RGB格式
                                    if isinstance(color, str):
                                        try:
                                            color = tuple(
                                                int(color[i : i + 2], 16) / 255
                                                for i in (0, 2, 4)
                                            )
                                        except:
                                            continue

                                    # 检查是否为红色
                                    if len(color) >= 3:
                                        r, g, b = color[:3]
                                        if r > 0.8 and g < 0.3 and b < 0.3:
                                            text = span["text"].strip()
                                            if text:
                                                logger.info(f"找到红色文本: {text}")
                                                red_texts.append(text)

            return red_texts

    except Exception as e:
        logger.error(f"直接提取文本出错: {e}")
        import traceback

        logger.error(traceback.format_exc())
        return []


def extract_red_text(pdf_data: bytes) -> Dict[str, Any]:
    """从PDF中提取红色文本

    先检查PDF是否可编辑，如果可编辑则直接提取文本，否则使用OCR
    """
    try:
        logger.info(f"收到PDF数据大小: {len(pdf_data)} bytes")

        # 检查PDF是否可编辑
        is_editable, total_blocks = is_pdf_editable(pdf_data)

        if is_editable:
            logger.info("PDF可编辑，直接提取文本")
            red_texts = extract_text_directly(pdf_data)
        else:
            logger.info("PDF不可编辑，使用OCR提取文本")
            red_texts = extract_text_with_ocr(pdf_data)

        logger.info(f"共找到 {len(red_texts)} 条红色文本")
        return {
            "success": True,
            "red_texts": red_texts,
            "count": len(red_texts),
            "is_editable": is_editable,
        }

    except Exception as e:
        logger.error(f"处理PDF时出错: {e}")
        import traceback

        logger.error(traceback.format_exc())
        return {"success": False, "error": str(e)}
