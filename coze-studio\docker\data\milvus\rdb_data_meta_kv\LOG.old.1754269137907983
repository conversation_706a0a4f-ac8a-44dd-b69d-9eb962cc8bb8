2025/08/03-06:24:20.543901 33 RocksDB version: 6.29.5
2025/08/03-06:24:20.545135 33 Git sha 0
2025/08/03-06:24:20.545141 33 Compile date 2024-11-15 11:22:58
2025/08/03-06:24:20.545148 33 DB SUMMARY
2025/08/03-06:24:20.545148 33 DB Session ID:  AHLH90FMLKD8P6H5NIXP
2025/08/03-06:24:20.546321 33 SST files in /var/lib/milvus/rdb_data_meta_kv dir, Total Num: 0, files: 
2025/08/03-06:24:20.546329 33 Write Ahead Log file in /var/lib/milvus/rdb_data_meta_kv: 
2025/08/03-06:24:20.546332 33                         Options.error_if_exists: 0
2025/08/03-06:24:20.546333 33                       Options.create_if_missing: 1
2025/08/03-06:24:20.546333 33                         Options.paranoid_checks: 1
2025/08/03-06:24:20.546334 33             Options.flush_verify_memtable_count: 1
2025/08/03-06:24:20.546335 33                               Options.track_and_verify_wals_in_manifest: 0
2025/08/03-06:24:20.546335 33                                     Options.env: 0x7fd0c878bd00
2025/08/03-06:24:20.546336 33                                      Options.fs: PosixFileSystem
2025/08/03-06:24:20.546337 33                                Options.info_log: 0x7fcfed090050
2025/08/03-06:24:20.546338 33                Options.max_file_opening_threads: 16
2025/08/03-06:24:20.546338 33                              Options.statistics: (nil)
2025/08/03-06:24:20.546339 33                               Options.use_fsync: 0
2025/08/03-06:24:20.546340 33                       Options.max_log_file_size: 0
2025/08/03-06:24:20.546340 33                  Options.max_manifest_file_size: 1073741824
2025/08/03-06:24:20.546341 33                   Options.log_file_time_to_roll: 0
2025/08/03-06:24:20.546342 33                       Options.keep_log_file_num: 1000
2025/08/03-06:24:20.546342 33                    Options.recycle_log_file_num: 0
2025/08/03-06:24:20.546343 33                         Options.allow_fallocate: 1
2025/08/03-06:24:20.546343 33                        Options.allow_mmap_reads: 0
2025/08/03-06:24:20.546344 33                       Options.allow_mmap_writes: 0
2025/08/03-06:24:20.546344 33                        Options.use_direct_reads: 0
2025/08/03-06:24:20.546345 33                        Options.use_direct_io_for_flush_and_compaction: 0
2025/08/03-06:24:20.546346 33          Options.create_missing_column_families: 0
2025/08/03-06:24:20.546346 33                              Options.db_log_dir: 
2025/08/03-06:24:20.546347 33                                 Options.wal_dir: 
2025/08/03-06:24:20.546347 33                Options.table_cache_numshardbits: 6
2025/08/03-06:24:20.546348 33                         Options.WAL_ttl_seconds: 0
2025/08/03-06:24:20.546348 33                       Options.WAL_size_limit_MB: 0
2025/08/03-06:24:20.546349 33                        Options.max_write_batch_group_size_bytes: 1048576
2025/08/03-06:24:20.546350 33             Options.manifest_preallocation_size: 4194304
2025/08/03-06:24:20.546350 33                     Options.is_fd_close_on_exec: 1
2025/08/03-06:24:20.546351 33                   Options.advise_random_on_open: 1
2025/08/03-06:24:20.546351 33                   Options.experimental_mempurge_threshold: 0.000000
2025/08/03-06:24:20.546655 33                    Options.db_write_buffer_size: 0
2025/08/03-06:24:20.546657 33                    Options.write_buffer_manager: 0x7fcff4e600a0
2025/08/03-06:24:20.546658 33         Options.access_hint_on_compaction_start: 1
2025/08/03-06:24:20.546659 33  Options.new_table_reader_for_compaction_inputs: 0
2025/08/03-06:24:20.546660 33           Options.random_access_max_buffer_size: 1048576
2025/08/03-06:24:20.546660 33                      Options.use_adaptive_mutex: 0
2025/08/03-06:24:20.546661 33                            Options.rate_limiter: (nil)
2025/08/03-06:24:20.546675 33     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/08/03-06:24:20.546676 33                       Options.wal_recovery_mode: 2
2025/08/03-06:24:20.546677 33                  Options.enable_thread_tracking: 0
2025/08/03-06:24:20.546677 33                  Options.enable_pipelined_write: 0
2025/08/03-06:24:20.546678 33                  Options.unordered_write: 0
2025/08/03-06:24:20.546919 33         Options.allow_concurrent_memtable_write: 1
2025/08/03-06:24:20.546921 33      Options.enable_write_thread_adaptive_yield: 1
2025/08/03-06:24:20.546922 33             Options.write_thread_max_yield_usec: 100
2025/08/03-06:24:20.546923 33            Options.write_thread_slow_yield_usec: 3
2025/08/03-06:24:20.546924 33                               Options.row_cache: None
2025/08/03-06:24:20.546924 33                              Options.wal_filter: None
2025/08/03-06:24:20.546925 33             Options.avoid_flush_during_recovery: 0
2025/08/03-06:24:20.546926 33             Options.allow_ingest_behind: 0
2025/08/03-06:24:20.546926 33             Options.preserve_deletes: 0
2025/08/03-06:24:20.546927 33             Options.two_write_queues: 0
2025/08/03-06:24:20.546927 33             Options.manual_wal_flush: 0
2025/08/03-06:24:20.546928 33             Options.atomic_flush: 0
2025/08/03-06:24:20.546928 33             Options.avoid_unnecessary_blocking_io: 0
2025/08/03-06:24:20.546929 33                 Options.persist_stats_to_disk: 0
2025/08/03-06:24:20.546930 33                 Options.write_dbid_to_manifest: 0
2025/08/03-06:24:20.546930 33                 Options.log_readahead_size: 0
2025/08/03-06:24:20.546931 33                 Options.file_checksum_gen_factory: Unknown
2025/08/03-06:24:20.546931 33                 Options.best_efforts_recovery: 0
2025/08/03-06:24:20.546932 33                Options.max_bgerror_resume_count: 2147483647
2025/08/03-06:24:20.546933 33            Options.bgerror_resume_retry_interval: 1000000
2025/08/03-06:24:20.546933 33             Options.allow_data_in_errors: 0
2025/08/03-06:24:20.546934 33             Options.db_host_id: __hostname__
2025/08/03-06:24:20.546936 33             Options.max_background_jobs: 2
2025/08/03-06:24:20.546936 33             Options.max_background_compactions: -1
2025/08/03-06:24:20.546937 33             Options.max_subcompactions: 1
2025/08/03-06:24:20.546937 33             Options.avoid_flush_during_shutdown: 0
2025/08/03-06:24:20.546938 33           Options.writable_file_max_buffer_size: 1048576
2025/08/03-06:24:20.546939 33             Options.delayed_write_rate : 16777216
2025/08/03-06:24:20.546939 33             Options.max_total_wal_size: 0
2025/08/03-06:24:20.546940 33             Options.delete_obsolete_files_period_micros: 21600000000
2025/08/03-06:24:20.546940 33                   Options.stats_dump_period_sec: 600
2025/08/03-06:24:20.546941 33                 Options.stats_persist_period_sec: 600
2025/08/03-06:24:20.546942 33                 Options.stats_history_buffer_size: 1048576
2025/08/03-06:24:20.546942 33                          Options.max_open_files: -1
2025/08/03-06:24:20.546943 33                          Options.bytes_per_sync: 0
2025/08/03-06:24:20.546943 33                      Options.wal_bytes_per_sync: 0
2025/08/03-06:24:20.546944 33                   Options.strict_bytes_per_sync: 0
2025/08/03-06:24:20.546945 33       Options.compaction_readahead_size: 0
2025/08/03-06:24:20.546945 33                  Options.max_background_flushes: 1
2025/08/03-06:24:20.546946 33 Compression algorithms supported:
2025/08/03-06:24:20.546947 33 	kZSTD supported: 1
2025/08/03-06:24:20.546948 33 	kXpressCompression supported: 0
2025/08/03-06:24:20.546949 33 	kBZip2Compression supported: 0
2025/08/03-06:24:20.546950 33 	kZSTDNotFinalCompression supported: 1
2025/08/03-06:24:20.546951 33 	kLZ4Compression supported: 0
2025/08/03-06:24:20.546951 33 	kZlibCompression supported: 0
2025/08/03-06:24:20.546952 33 	kLZ4HCCompression supported: 0
2025/08/03-06:24:20.546952 33 	kSnappyCompression supported: 0
2025/08/03-06:24:20.546957 33 Fast CRC32 supported: Not supported on x86
2025/08/03-06:24:20.563725 33 [db/db_impl/db_impl_open.cc:307] Creating manifest 1 
2025/08/03-06:24:20.587514 33 [db/version_set.cc:4888] Recovering from manifest file: /var/lib/milvus/rdb_data_meta_kv/MANIFEST-000001
2025/08/03-06:24:20.589380 33 [db/column_family.cc:605] --------------- Options for column family [default]:
2025/08/03-06:24:20.589560 33               Options.comparator: leveldb.BytewiseComparator
2025/08/03-06:24:20.589561 33           Options.merge_operator: None
2025/08/03-06:24:20.589562 33        Options.compaction_filter: None
2025/08/03-06:24:20.589563 33        Options.compaction_filter_factory: None
2025/08/03-06:24:20.589563 33  Options.sst_partitioner_factory: None
2025/08/03-06:24:20.589564 33         Options.memtable_factory: SkipListFactory
2025/08/03-06:24:20.589565 33            Options.table_factory: BlockBasedTable
2025/08/03-06:24:20.589590 33            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7fcff4e00100)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0x7fcff4e60010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 1001872834
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: (nil)
  persistent_cache: (nil)
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2025/08/03-06:24:20.589592 33        Options.write_buffer_size: 67108864
2025/08/03-06:24:20.589593 33  Options.max_write_buffer_number: 2
2025/08/03-06:24:20.589594 33        Options.compression[0]: NoCompression
2025/08/03-06:24:20.589595 33        Options.compression[1]: NoCompression
2025/08/03-06:24:20.589596 33        Options.compression[2]: ZSTD
2025/08/03-06:24:20.589597 33        Options.compression[3]: ZSTD
2025/08/03-06:24:20.589597 33        Options.compression[4]: ZSTD
2025/08/03-06:24:20.589598 33                  Options.bottommost_compression: Disabled
2025/08/03-06:24:20.589598 33       Options.prefix_extractor: nullptr
2025/08/03-06:24:20.589599 33   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/03-06:24:20.589600 33             Options.num_levels: 5
2025/08/03-06:24:20.589600 33        Options.min_write_buffer_number_to_merge: 1
2025/08/03-06:24:20.589601 33     Options.max_write_buffer_number_to_maintain: 0
2025/08/03-06:24:20.589601 33     Options.max_write_buffer_size_to_maintain: 0
2025/08/03-06:24:20.589602 33            Options.bottommost_compression_opts.window_bits: -14
2025/08/03-06:24:20.589603 33                  Options.bottommost_compression_opts.level: 32767
2025/08/03-06:24:20.589603 33               Options.bottommost_compression_opts.strategy: 0
2025/08/03-06:24:20.589604 33         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/03-06:24:20.589604 33         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/03-06:24:20.589605 33         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/03-06:24:20.589606 33                  Options.bottommost_compression_opts.enabled: false
2025/08/03-06:24:20.589606 33         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/03-06:24:20.589607 33            Options.compression_opts.window_bits: -14
2025/08/03-06:24:20.589608 33                  Options.compression_opts.level: 32767
2025/08/03-06:24:20.589608 33               Options.compression_opts.strategy: 0
2025/08/03-06:24:20.589609 33         Options.compression_opts.max_dict_bytes: 0
2025/08/03-06:24:20.589609 33         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/03-06:24:20.589610 33         Options.compression_opts.parallel_threads: 1
2025/08/03-06:24:20.589610 33                  Options.compression_opts.enabled: false
2025/08/03-06:24:20.589750 33         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/03-06:24:20.589752 33      Options.level0_file_num_compaction_trigger: 4
2025/08/03-06:24:20.589752 33          Options.level0_slowdown_writes_trigger: 20
2025/08/03-06:24:20.589753 33              Options.level0_stop_writes_trigger: 36
2025/08/03-06:24:20.589754 33                   Options.target_file_size_base: 67108864
2025/08/03-06:24:20.589755 33             Options.target_file_size_multiplier: 2
2025/08/03-06:24:20.589755 33                Options.max_bytes_for_level_base: 268435456
2025/08/03-06:24:20.589756 33 Options.level_compaction_dynamic_level_bytes: 0
2025/08/03-06:24:20.589756 33          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/03-06:24:20.589758 33 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/03-06:24:20.589759 33 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/03-06:24:20.589759 33 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/03-06:24:20.589760 33 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/03-06:24:20.589761 33 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/03-06:24:20.589761 33 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/03-06:24:20.589762 33 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/03-06:24:20.589762 33       Options.max_sequential_skip_in_iterations: 8
2025/08/03-06:24:20.589763 33                    Options.max_compaction_bytes: 1677721600
2025/08/03-06:24:20.589764 33                        Options.arena_block_size: 1048576
2025/08/03-06:24:20.589764 33   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/03-06:24:20.589765 33   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/03-06:24:20.589766 33       Options.rate_limit_delay_max_milliseconds: 100
2025/08/03-06:24:20.589766 33                Options.disable_auto_compactions: 0
2025/08/03-06:24:20.589768 33                        Options.compaction_style: kCompactionStyleLevel
2025/08/03-06:24:20.589769 33                          Options.compaction_pri: kMinOverlappingRatio
2025/08/03-06:24:20.589770 33 Options.compaction_options_universal.size_ratio: 1
2025/08/03-06:24:20.589771 33 Options.compaction_options_universal.min_merge_width: 2
2025/08/03-06:24:20.589771 33 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/03-06:24:20.589772 33 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/03-06:24:20.589772 33 Options.compaction_options_universal.compression_size_percent: -1
2025/08/03-06:24:20.589773 33 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/03-06:24:20.589774 33 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/03-06:24:20.589774 33 Options.compaction_options_fifo.allow_compaction: 0
2025/08/03-06:24:20.589778 33                   Options.table_properties_collectors: 
2025/08/03-06:24:20.589779 33                   Options.inplace_update_support: 0
2025/08/03-06:24:20.589780 33                 Options.inplace_update_num_locks: 10000
2025/08/03-06:24:20.589781 33               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/03-06:24:20.589781 33               Options.memtable_whole_key_filtering: 0
2025/08/03-06:24:20.589782 33   Options.memtable_huge_page_size: 0
2025/08/03-06:24:20.589783 33                           Options.bloom_locality: 0
2025/08/03-06:24:20.589783 33                    Options.max_successive_merges: 0
2025/08/03-06:24:20.589784 33                Options.optimize_filters_for_hits: 0
2025/08/03-06:24:20.589784 33                Options.paranoid_file_checks: 0
2025/08/03-06:24:20.589785 33                Options.force_consistency_checks: 1
2025/08/03-06:24:20.589786 33                Options.report_bg_io_stats: 0
2025/08/03-06:24:20.589786 33                               Options.ttl: 2592000
2025/08/03-06:24:20.589787 33          Options.periodic_compaction_seconds: 0
2025/08/03-06:24:20.589787 33                       Options.enable_blob_files: false
2025/08/03-06:24:20.589981 33                           Options.min_blob_size: 0
2025/08/03-06:24:20.589984 33                          Options.blob_file_size: 268435456
2025/08/03-06:24:20.589985 33                   Options.blob_compression_type: NoCompression
2025/08/03-06:24:20.589986 33          Options.enable_blob_garbage_collection: false
2025/08/03-06:24:20.589987 33      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/03-06:24:20.589989 33 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/03-06:24:20.589990 33          Options.blob_compaction_readahead_size: 0
2025/08/03-06:24:20.593746 33 [db/version_set.cc:4928] Recovered from manifest file:/var/lib/milvus/rdb_data_meta_kv/MANIFEST-000001 succeeded,manifest_file_number is 1, next_file_number is 3, last_sequence is 0, log_number is 0,prev_log_number is 0,max_column_family is 0,min_log_number_to_keep is 0
2025/08/03-06:24:20.593755 33 [db/version_set.cc:4943] Column family [default] (ID 0), log number is 0
2025/08/03-06:24:20.598393 33 [db/version_set.cc:4409] Creating manifest 4
2025/08/03-06:24:20.640237 33 [db/db_impl/db_impl_open.cc:1820] SstFileManager instance 0x7fcfed180000
2025/08/03-06:24:20.641999 33 DB pointer 0x7fcfed020000
2025/08/03-06:24:20.643096 63 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/03-06:24:20.643110 63 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 0.1 total, 0.1 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 0.1 total, 0.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fcff4e60010#8 capacity: 955.46 MB collections: 1 last_copies: 0 last_secs: 4.7e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/08/03-06:29:56.682308 91 [db/db_impl/db_impl.cc:481] Shutdown: canceling all background work
2025/08/03-06:29:56.687381 91 [db/db_impl/db_impl.cc:699] Shutdown complete
