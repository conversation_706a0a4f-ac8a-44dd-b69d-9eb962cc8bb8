2025/08/04-13:18:44.939595 40 RocksDB version: 6.29.5
2025/08/04-13:18:44.940262 40 Git sha 0
2025/08/04-13:18:44.940269 40 Compile date 2024-11-15 11:22:58
2025/08/04-13:18:44.940276 40 DB SUMMARY
2025/08/04-13:18:44.940278 40 DB Session ID:  OEWBHM8QHEF6GL3LWZGR
2025/08/04-13:18:44.944287 40 CURRENT file:  CURRENT
2025/08/04-13:18:44.944297 40 IDENTITY file:  IDENTITY
2025/08/04-13:18:44.945185 40 MANIFEST file:  MANIFEST-000025 size: 172 Bytes
2025/08/04-13:18:44.945197 40 SST files in /var/lib/milvus/rdb_data_meta_kv dir, Total Num: 1, files: 000009.sst 
2025/08/04-13:18:44.945203 40 Write Ahead Log file in /var/lib/milvus/rdb_data_meta_kv: 000011.log size: 0 ; 000016.log size: 0 ; 000021.log size: 0 ; 000026.log size: 0 ; 
2025/08/04-13:18:44.945208 40                         Options.error_if_exists: 0
2025/08/04-13:18:44.945210 40                       Options.create_if_missing: 1
2025/08/04-13:18:44.945210 40                         Options.paranoid_checks: 1
2025/08/04-13:18:44.945211 40             Options.flush_verify_memtable_count: 1
2025/08/04-13:18:44.945212 40                               Options.track_and_verify_wals_in_manifest: 0
2025/08/04-13:18:44.945212 40                                     Options.env: 0x7f7729752d00
2025/08/04-13:18:44.945213 40                                      Options.fs: PosixFileSystem
2025/08/04-13:18:44.945214 40                                Options.info_log: 0x7f764de90050
2025/08/04-13:18:44.945215 40                Options.max_file_opening_threads: 16
2025/08/04-13:18:44.945215 40                              Options.statistics: (nil)
2025/08/04-13:18:44.945216 40                               Options.use_fsync: 0
2025/08/04-13:18:44.945217 40                       Options.max_log_file_size: 0
2025/08/04-13:18:44.945217 40                  Options.max_manifest_file_size: 1073741824
2025/08/04-13:18:44.945218 40                   Options.log_file_time_to_roll: 0
2025/08/04-13:18:44.945219 40                       Options.keep_log_file_num: 1000
2025/08/04-13:18:44.945219 40                    Options.recycle_log_file_num: 0
2025/08/04-13:18:44.945220 40                         Options.allow_fallocate: 1
2025/08/04-13:18:44.945220 40                        Options.allow_mmap_reads: 0
2025/08/04-13:18:44.945221 40                       Options.allow_mmap_writes: 0
2025/08/04-13:18:44.945221 40                        Options.use_direct_reads: 0
2025/08/04-13:18:44.945222 40                        Options.use_direct_io_for_flush_and_compaction: 0
2025/08/04-13:18:44.945222 40          Options.create_missing_column_families: 0
2025/08/04-13:18:44.945224 40                              Options.db_log_dir: 
2025/08/04-13:18:44.945225 40                                 Options.wal_dir: 
2025/08/04-13:18:44.945226 40                Options.table_cache_numshardbits: 6
2025/08/04-13:18:44.945227 40                         Options.WAL_ttl_seconds: 0
2025/08/04-13:18:44.945227 40                       Options.WAL_size_limit_MB: 0
2025/08/04-13:18:44.945228 40                        Options.max_write_batch_group_size_bytes: 1048576
2025/08/04-13:18:44.945228 40             Options.manifest_preallocation_size: 4194304
2025/08/04-13:18:44.945229 40                     Options.is_fd_close_on_exec: 1
2025/08/04-13:18:44.945229 40                   Options.advise_random_on_open: 1
2025/08/04-13:18:44.945230 40                   Options.experimental_mempurge_threshold: 0.000000
2025/08/04-13:18:44.945239 40                    Options.db_write_buffer_size: 0
2025/08/04-13:18:44.945240 40                    Options.write_buffer_manager: 0x7f7650a500a0
2025/08/04-13:18:44.945240 40         Options.access_hint_on_compaction_start: 1
2025/08/04-13:18:44.945241 40  Options.new_table_reader_for_compaction_inputs: 0
2025/08/04-13:18:44.945242 40           Options.random_access_max_buffer_size: 1048576
2025/08/04-13:18:44.945242 40                      Options.use_adaptive_mutex: 0
2025/08/04-13:18:44.945243 40                            Options.rate_limiter: (nil)
2025/08/04-13:18:44.945246 40     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/08/04-13:18:44.945247 40                       Options.wal_recovery_mode: 2
2025/08/04-13:18:44.945820 40                  Options.enable_thread_tracking: 0
2025/08/04-13:18:44.945825 40                  Options.enable_pipelined_write: 0
2025/08/04-13:18:44.945826 40                  Options.unordered_write: 0
2025/08/04-13:18:44.945827 40         Options.allow_concurrent_memtable_write: 1
2025/08/04-13:18:44.945828 40      Options.enable_write_thread_adaptive_yield: 1
2025/08/04-13:18:44.945828 40             Options.write_thread_max_yield_usec: 100
2025/08/04-13:18:44.945829 40            Options.write_thread_slow_yield_usec: 3
2025/08/04-13:18:44.945829 40                               Options.row_cache: None
2025/08/04-13:18:44.945830 40                              Options.wal_filter: None
2025/08/04-13:18:44.945831 40             Options.avoid_flush_during_recovery: 0
2025/08/04-13:18:44.945831 40             Options.allow_ingest_behind: 0
2025/08/04-13:18:44.945832 40             Options.preserve_deletes: 0
2025/08/04-13:18:44.945833 40             Options.two_write_queues: 0
2025/08/04-13:18:44.945833 40             Options.manual_wal_flush: 0
2025/08/04-13:18:44.945834 40             Options.atomic_flush: 0
2025/08/04-13:18:44.945834 40             Options.avoid_unnecessary_blocking_io: 0
2025/08/04-13:18:44.945835 40                 Options.persist_stats_to_disk: 0
2025/08/04-13:18:44.945835 40                 Options.write_dbid_to_manifest: 0
2025/08/04-13:18:44.945836 40                 Options.log_readahead_size: 0
2025/08/04-13:18:44.945837 40                 Options.file_checksum_gen_factory: Unknown
2025/08/04-13:18:44.945837 40                 Options.best_efforts_recovery: 0
2025/08/04-13:18:44.945838 40                Options.max_bgerror_resume_count: 2147483647
2025/08/04-13:18:44.945839 40            Options.bgerror_resume_retry_interval: 1000000
2025/08/04-13:18:44.945839 40             Options.allow_data_in_errors: 0
2025/08/04-13:18:44.945840 40             Options.db_host_id: __hostname__
2025/08/04-13:18:44.945843 40             Options.max_background_jobs: 2
2025/08/04-13:18:44.945844 40             Options.max_background_compactions: -1
2025/08/04-13:18:44.945845 40             Options.max_subcompactions: 1
2025/08/04-13:18:44.945845 40             Options.avoid_flush_during_shutdown: 0
2025/08/04-13:18:44.945846 40           Options.writable_file_max_buffer_size: 1048576
2025/08/04-13:18:44.945846 40             Options.delayed_write_rate : 16777216
2025/08/04-13:18:44.945847 40             Options.max_total_wal_size: 0
2025/08/04-13:18:44.945848 40             Options.delete_obsolete_files_period_micros: 21600000000
2025/08/04-13:18:44.945848 40                   Options.stats_dump_period_sec: 600
2025/08/04-13:18:44.945849 40                 Options.stats_persist_period_sec: 600
2025/08/04-13:18:44.945849 40                 Options.stats_history_buffer_size: 1048576
2025/08/04-13:18:44.945850 40                          Options.max_open_files: -1
2025/08/04-13:18:44.945851 40                          Options.bytes_per_sync: 0
2025/08/04-13:18:44.945852 40                      Options.wal_bytes_per_sync: 0
2025/08/04-13:18:44.945853 40                   Options.strict_bytes_per_sync: 0
2025/08/04-13:18:44.945854 40       Options.compaction_readahead_size: 0
2025/08/04-13:18:44.945855 40                  Options.max_background_flushes: 1
2025/08/04-13:18:44.945856 40 Compression algorithms supported:
2025/08/04-13:18:44.945858 40 	kZSTD supported: 1
2025/08/04-13:18:44.945859 40 	kXpressCompression supported: 0
2025/08/04-13:18:44.945861 40 	kBZip2Compression supported: 0
2025/08/04-13:18:44.945874 40 	kZSTDNotFinalCompression supported: 1
2025/08/04-13:18:44.945876 40 	kLZ4Compression supported: 0
2025/08/04-13:18:44.945877 40 	kZlibCompression supported: 0
2025/08/04-13:18:44.945878 40 	kLZ4HCCompression supported: 0
2025/08/04-13:18:44.945880 40 	kSnappyCompression supported: 0
2025/08/04-13:18:44.945886 40 Fast CRC32 supported: Not supported on x86
2025/08/04-13:18:44.952337 40 [db/version_set.cc:4888] Recovering from manifest file: /var/lib/milvus/rdb_data_meta_kv/MANIFEST-000025
2025/08/04-13:18:44.955190 40 [db/column_family.cc:605] --------------- Options for column family [default]:
2025/08/04-13:18:44.955200 40               Options.comparator: leveldb.BytewiseComparator
2025/08/04-13:18:44.955202 40           Options.merge_operator: None
2025/08/04-13:18:44.955203 40        Options.compaction_filter: None
2025/08/04-13:18:44.955203 40        Options.compaction_filter_factory: None
2025/08/04-13:18:44.955204 40  Options.sst_partitioner_factory: None
2025/08/04-13:18:44.955204 40         Options.memtable_factory: SkipListFactory
2025/08/04-13:18:44.955205 40            Options.table_factory: BlockBasedTable
2025/08/04-13:18:44.955260 40            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f7650a00100)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0x7f7650a50010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 1001872834
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: (nil)
  persistent_cache: (nil)
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2025/08/04-13:18:44.955262 40        Options.write_buffer_size: 67108864
2025/08/04-13:18:44.955262 40  Options.max_write_buffer_number: 2
2025/08/04-13:18:44.955297 40        Options.compression[0]: NoCompression
2025/08/04-13:18:44.955299 40        Options.compression[1]: NoCompression
2025/08/04-13:18:44.955301 40        Options.compression[2]: ZSTD
2025/08/04-13:18:44.955303 40        Options.compression[3]: ZSTD
2025/08/04-13:18:44.955304 40        Options.compression[4]: ZSTD
2025/08/04-13:18:44.955305 40                  Options.bottommost_compression: Disabled
2025/08/04-13:18:44.955305 40       Options.prefix_extractor: nullptr
2025/08/04-13:18:44.955306 40   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/04-13:18:44.955307 40             Options.num_levels: 5
2025/08/04-13:18:44.955307 40        Options.min_write_buffer_number_to_merge: 1
2025/08/04-13:18:44.955308 40     Options.max_write_buffer_number_to_maintain: 0
2025/08/04-13:18:44.955308 40     Options.max_write_buffer_size_to_maintain: 0
2025/08/04-13:18:44.955309 40            Options.bottommost_compression_opts.window_bits: -14
2025/08/04-13:18:44.955310 40                  Options.bottommost_compression_opts.level: 32767
2025/08/04-13:18:44.955310 40               Options.bottommost_compression_opts.strategy: 0
2025/08/04-13:18:44.955311 40         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/04-13:18:44.955311 40         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/04-13:18:44.955312 40         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/04-13:18:44.955313 40                  Options.bottommost_compression_opts.enabled: false
2025/08/04-13:18:44.955313 40         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/04-13:18:44.955323 40            Options.compression_opts.window_bits: -14
2025/08/04-13:18:44.955325 40                  Options.compression_opts.level: 32767
2025/08/04-13:18:44.955326 40               Options.compression_opts.strategy: 0
2025/08/04-13:18:44.955327 40         Options.compression_opts.max_dict_bytes: 0
2025/08/04-13:18:44.955328 40         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/04-13:18:44.955531 40         Options.compression_opts.parallel_threads: 1
2025/08/04-13:18:44.955534 40                  Options.compression_opts.enabled: false
2025/08/04-13:18:44.955536 40         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/04-13:18:44.955536 40      Options.level0_file_num_compaction_trigger: 4
2025/08/04-13:18:44.955537 40          Options.level0_slowdown_writes_trigger: 20
2025/08/04-13:18:44.955538 40              Options.level0_stop_writes_trigger: 36
2025/08/04-13:18:44.955538 40                   Options.target_file_size_base: 67108864
2025/08/04-13:18:44.955539 40             Options.target_file_size_multiplier: 2
2025/08/04-13:18:44.955540 40                Options.max_bytes_for_level_base: 268435456
2025/08/04-13:18:44.955540 40 Options.level_compaction_dynamic_level_bytes: 0
2025/08/04-13:18:44.955541 40          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/04-13:18:44.955544 40 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/04-13:18:44.955545 40 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/04-13:18:44.955545 40 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/04-13:18:44.955546 40 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/04-13:18:44.955546 40 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/04-13:18:44.955547 40 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/04-13:18:44.955548 40 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/04-13:18:44.955548 40       Options.max_sequential_skip_in_iterations: 8
2025/08/04-13:18:44.955549 40                    Options.max_compaction_bytes: 1677721600
2025/08/04-13:18:44.955549 40                        Options.arena_block_size: 1048576
2025/08/04-13:18:44.955550 40   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/04-13:18:44.955551 40   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/04-13:18:44.955551 40       Options.rate_limit_delay_max_milliseconds: 100
2025/08/04-13:18:44.955552 40                Options.disable_auto_compactions: 0
2025/08/04-13:18:44.955555 40                        Options.compaction_style: kCompactionStyleLevel
2025/08/04-13:18:44.955556 40                          Options.compaction_pri: kMinOverlappingRatio
2025/08/04-13:18:44.955557 40 Options.compaction_options_universal.size_ratio: 1
2025/08/04-13:18:44.955557 40 Options.compaction_options_universal.min_merge_width: 2
2025/08/04-13:18:44.955558 40 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/04-13:18:44.955559 40 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/04-13:18:44.955559 40 Options.compaction_options_universal.compression_size_percent: -1
2025/08/04-13:18:44.955561 40 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/04-13:18:44.955561 40 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/04-13:18:44.955562 40 Options.compaction_options_fifo.allow_compaction: 0
2025/08/04-13:18:44.955584 40                   Options.table_properties_collectors: 
2025/08/04-13:18:44.955586 40                   Options.inplace_update_support: 0
2025/08/04-13:18:44.955588 40                 Options.inplace_update_num_locks: 10000
2025/08/04-13:18:44.955589 40               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/04-13:18:44.955590 40               Options.memtable_whole_key_filtering: 0
2025/08/04-13:18:44.955591 40   Options.memtable_huge_page_size: 0
2025/08/04-13:18:44.955592 40                           Options.bloom_locality: 0
2025/08/04-13:18:44.955592 40                    Options.max_successive_merges: 0
2025/08/04-13:18:44.955593 40                Options.optimize_filters_for_hits: 0
2025/08/04-13:18:44.955594 40                Options.paranoid_file_checks: 0
2025/08/04-13:18:44.955594 40                Options.force_consistency_checks: 1
2025/08/04-13:18:44.955595 40                Options.report_bg_io_stats: 0
2025/08/04-13:18:44.955595 40                               Options.ttl: 2592000
2025/08/04-13:18:44.955975 40          Options.periodic_compaction_seconds: 0
2025/08/04-13:18:44.955977 40                       Options.enable_blob_files: false
2025/08/04-13:18:44.955978 40                           Options.min_blob_size: 0
2025/08/04-13:18:44.955979 40                          Options.blob_file_size: 268435456
2025/08/04-13:18:44.955981 40                   Options.blob_compression_type: NoCompression
2025/08/04-13:18:44.955982 40          Options.enable_blob_garbage_collection: false
2025/08/04-13:18:44.955982 40      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/04-13:18:44.955983 40 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/04-13:18:44.955984 40          Options.blob_compaction_readahead_size: 0
2025/08/04-13:18:44.962136 40 [db/version_set.cc:4928] Recovered from manifest file:/var/lib/milvus/rdb_data_meta_kv/MANIFEST-000025 succeeded,manifest_file_number is 25, next_file_number is 27, last_sequence is 34, log_number is 22,prev_log_number is 0,max_column_family is 0,min_log_number_to_keep is 0
2025/08/04-13:18:44.962146 40 [db/version_set.cc:4943] Column family [default] (ID 0), log number is 22
2025/08/04-13:18:44.965684 40 [db/version_set.cc:4409] Creating manifest 29
2025/08/04-13:18:44.986736 40 EVENT_LOG_v1 {"time_micros": 1754313524986698, "job": 1, "event": "recovery_started", "wal_files": [11, 16, 21, 26]}
2025/08/04-13:18:44.986744 40 [db/db_impl/db_impl_open.cc:874] Skipping log #11 since it is older than min log to keep #22
2025/08/04-13:18:44.986746 40 [db/db_impl/db_impl_open.cc:874] Skipping log #16 since it is older than min log to keep #22
2025/08/04-13:18:44.986747 40 [db/db_impl/db_impl_open.cc:874] Skipping log #21 since it is older than min log to keep #22
2025/08/04-13:18:44.986749 40 [db/db_impl/db_impl_open.cc:888] Recovering log #26 mode 2
2025/08/04-13:18:44.988252 40 [db/version_set.cc:4409] Creating manifest 30
2025/08/04-13:18:45.009293 40 EVENT_LOG_v1 {"time_micros": 1754313525009284, "job": 1, "event": "recovery_finished"}
2025/08/04-13:18:45.033036 40 [db/db_impl/db_impl_open.cc:1820] SstFileManager instance 0x7f764d300000
2025/08/04-13:18:45.034591 40 DB pointer 0x7f764de20000
2025/08/04-13:18:45.035043 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/04-13:18:45.035056 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 0.1 total, 0.1 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    1.69 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      1/0    1.69 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 0.1 total, 0.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f7650a50010#8 capacity: 955.46 MB collections: 1 last_copies: 0 last_secs: 5e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/08/04-13:20:43.209203 144 [db/db_impl/db_impl.cc:481] Shutdown: canceling all background work
2025/08/04-13:20:43.212876 144 [db/db_impl/db_impl.cc:699] Shutdown complete
