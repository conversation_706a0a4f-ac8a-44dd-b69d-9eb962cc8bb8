-- Modify "conversation" table
ALTER TABLE `opencoze`.`conversation` COMMENT "conversation info record", MODIFY COLUMN `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT "id", <PERSON><PERSON><PERSON><PERSON> COLUMN `connector_id` bigint unsigned NOT NULL DEFAULT 0 COMMENT "Publish Connector ID", M<PERSON><PERSON>Y COLUMN `scene` tinyint NOT NULL DEFAULT 0 COMMENT "conversation scene", MODIFY COLUMN `section_id` bigint unsigned NOT NULL DEFAULT 0 COMMENT "section_id", MODIFY COLUMN `creator_id` bigint unsigned NULL DEFAULT 0 COMMENT "creator_id", M<PERSON><PERSON><PERSON> COLUMN `ext` text NULL COMMENT "ext", MODIFY COLUMN `created_at` bigint unsigned NOT NULL DEFAULT 0 COMMENT "Create Time in Milliseconds", MODIFY COLUMN `updated_at` bigint unsigned NOT NULL DEFAULT 0 COMMENT "Update Time in Milliseconds";
-- Modify "data_copy_task" table
ALTER TABLE `opencoze`.`data_copy_task` COMMENT "data copy task record", <PERSON><PERSON><PERSON><PERSON> COLUMN `master_task_id` varchar(128) NULL DEFAULT "" COMMENT "task id", MODIFY COLUMN `origin_data_id` bigint unsigned NOT NULL DEFAULT 0 COMMENT "origin data id", MODIFY COLUMN `target_data_id` bigint unsigned NOT NULL DEFAULT 0 COMMENT "target data id", MODIFY COLUMN `origin_space_id` bigint unsigned NOT NULL DEFAULT 0 COMMENT "origin space id", MODIFY COLUMN `target_space_id` bigint unsigned NOT NULL DEFAULT 0 COMMENT "target space id", MODIFY COLUMN `origin_user_id` bigint unsigned NOT NULL DEFAULT 0 COMMENT "origin user id", MODIFY COLUMN `target_user_id` bigint unsigned NULL DEFAULT 0 COMMENT "target user id", MODIFY COLUMN `origin_app_id` bigint unsigned NOT NULL DEFAULT 0 COMMENT "origin app id", MODIFY COLUMN `target_app_id` bigint unsigned NOT NULL DEFAULT 0 COMMENT "target app id", MODIFY COLUMN `data_type` tinyint unsigned NOT NULL DEFAULT 0 COMMENT "data type 1:knowledge, 2:database", MODIFY COLUMN `ext_info` varchar(255) NOT NULL DEFAULT "" COMMENT "ext", MODIFY COLUMN `start_time` bigint NULL DEFAULT 0 COMMENT "task start time", MODIFY COLUMN `finish_time` bigint NULL COMMENT "task finish time", MODIFY COLUMN `status` tinyint NOT NULL DEFAULT 1 COMMENT "1: Create 2: Running 3: Success 4: Failure", MODIFY COLUMN `error_msg` varchar(128) NULL COMMENT "error msg";
-- Modify "knowledge" table
ALTER TABLE `opencoze`.`knowledge` COMMENT "knowledge tabke", MODIFY COLUMN `id` bigint unsigned NOT NULL COMMENT "id", MODIFY COLUMN `name` varchar(150) NOT NULL DEFAULT "" COMMENT "knowledge's name", MODIFY COLUMN `app_id` bigint NOT NULL DEFAULT 0 COMMENT "app id", MODIFY COLUMN `creator_id` bigint unsigned NOT NULL DEFAULT 0 COMMENT "creator id", MODIFY COLUMN `space_id` bigint unsigned NOT NULL DEFAULT 0 COMMENT "space id", MODIFY COLUMN `deleted_at` datetime(3) NULL COMMENT "Delete Time", MODIFY COLUMN `status` tinyint NOT NULL DEFAULT 1 COMMENT "0 initialization, 1 effective, 2 invalid", MODIFY COLUMN `description` text NULL COMMENT "description", MODIFY COLUMN `icon_uri` varchar(150) NULL COMMENT "icon uri", MODIFY COLUMN `format_type` tinyint NOT NULL DEFAULT 0 COMMENT "0: Text 1: Table 2: Images";
-- Modify "knowledge_document" table
ALTER TABLE `opencoze`.`knowledge_document` COMMENT "knowledge document info", MODIFY COLUMN `id` bigint unsigned NOT NULL COMMENT "id", MODIFY COLUMN `knowledge_id` bigint unsigned NOT NULL DEFAULT 0 COMMENT "knowledge id", MODIFY COLUMN `name` varchar(150) NOT NULL DEFAULT "" COMMENT "document name", MODIFY COLUMN `file_extension` varchar(20) NOT NULL DEFAULT "0" COMMENT "Document type, txt/pdf/csv etc..", MODIFY COLUMN `document_type` int NOT NULL DEFAULT 0 COMMENT "Document type: 0: Text 1: Table 2: Image", MODIFY COLUMN `uri` text NULL COMMENT "uri", MODIFY COLUMN `size` bigint unsigned NOT NULL DEFAULT 0 COMMENT "document size", MODIFY COLUMN `slice_count` bigint unsigned NOT NULL DEFAULT 0 COMMENT "slice count", MODIFY COLUMN `char_count` bigint unsigned NOT NULL DEFAULT 0 COMMENT "number of characters", MODIFY COLUMN `creator_id` bigint unsigned NOT NULL DEFAULT 0 COMMENT "creator id", MODIFY COLUMN `space_id` bigint NOT NULL DEFAULT 0 COMMENT "space id", MODIFY COLUMN `deleted_at` datetime(3) NULL COMMENT "Delete Time", MODIFY COLUMN `source_type` int NULL DEFAULT 0 COMMENT "0: Local file upload, 2: Custom text, 103: Feishu 104: Lark", MODIFY COLUMN `status` int NOT NULL DEFAULT 0 COMMENT "status", MODIFY COLUMN `fail_reason` text NULL COMMENT "fail reason", MODIFY COLUMN `parse_rule` json NULL COMMENT "parse rule", MODIFY COLUMN `table_info` json NULL COMMENT "table info";
-- Modify "knowledge_document_review" table
ALTER TABLE `opencoze`.`knowledge_document_review` COMMENT "Document slice preview info", MODIFY COLUMN `id` bigint unsigned NOT NULL DEFAULT 0 COMMENT "id", MODIFY COLUMN `space_id` bigint NOT NULL DEFAULT 0 COMMENT "space id", MODIFY COLUMN `name` varchar(150) NOT NULL DEFAULT "" COMMENT "name", MODIFY COLUMN `type` varchar(10) NOT NULL DEFAULT "0" COMMENT "document type", MODIFY COLUMN `uri` text NULL COMMENT "uri", MODIFY COLUMN `format_type` tinyint unsigned NOT NULL DEFAULT 0 COMMENT "0 text, 1 table, 2 images", MODIFY COLUMN `status` tinyint unsigned NOT NULL DEFAULT 0 COMMENT "0 Processing 1 Completed 2 Failed 3 Expired", MODIFY COLUMN `chunk_resp_uri` text NULL COMMENT "pre-sliced uri", MODIFY COLUMN `deleted_at` datetime(3) NULL COMMENT "Delete Time", MODIFY COLUMN `creator_id` bigint NOT NULL DEFAULT 0 COMMENT "creator id";
-- Modify "knowledge_document_slice" table
ALTER TABLE `opencoze`.`knowledge_document_slice` COMMENT "knowledge document slice", MODIFY COLUMN `id` bigint unsigned NOT NULL DEFAULT 0 COMMENT "id", MODIFY COLUMN `document_id` bigint unsigned NOT NULL DEFAULT 0 COMMENT "document_id", MODIFY COLUMN `content` text NULL COMMENT "content", MODIFY COLUMN `sequence` decimal(20,5) NOT NULL COMMENT "slice sequence number, starting from 1", MODIFY COLUMN `deleted_at` datetime(3) NULL COMMENT "Delete Time", MODIFY COLUMN `creator_id` bigint NOT NULL DEFAULT 0 COMMENT "creator id", MODIFY COLUMN `space_id` bigint NOT NULL DEFAULT 0 COMMENT "space id", MODIFY COLUMN `status` int NOT NULL DEFAULT 0 COMMENT "status", MODIFY COLUMN `fail_reason` text NULL COMMENT "fail reason", MODIFY COLUMN `hit` bigint unsigned NOT NULL DEFAULT 0 COMMENT "hit counts ";
-- Modify "message" table
ALTER TABLE `opencoze`.`message` COMMENT "message record", MODIFY COLUMN `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT "id", MODIFY COLUMN `run_id` bigint unsigned NOT NULL DEFAULT 0 COMMENT "run_id", MODIFY COLUMN `role` varchar(100) NOT NULL DEFAULT "" COMMENT "role: user、assistant、system", MODIFY COLUMN `content_type` varchar(100) NOT NULL DEFAULT "" COMMENT "content type 1 text", MODIFY COLUMN `content` mediumtext NULL COMMENT "content", MODIFY COLUMN `message_type` varchar(100) NOT NULL DEFAULT "" COMMENT "message_type", MODIFY COLUMN `display_content` text NULL COMMENT "display content", MODIFY COLUMN `ext` text NULL COMMENT "message ext" COLLATE utf8mb4_general_ci, MODIFY COLUMN `section_id` bigint unsigned NULL COMMENT "section_id", MODIFY COLUMN `broken_position` int NULL DEFAULT -1 COMMENT "broken position", MODIFY COLUMN `status` tinyint unsigned NOT NULL DEFAULT 0 COMMENT "message status: 1 Available 2 Deleted 3 Replaced 4 Broken 5 Failed 6 Streaming 7 Pending", MODIFY COLUMN `model_content` mediumtext NULL COMMENT "model content", MODIFY COLUMN `meta_info` text NULL COMMENT "text tagging information such as citation and highlighting", MODIFY COLUMN `reasoning_content` text NULL COMMENT "reasoning content" COLLATE utf8mb4_general_ci, MODIFY COLUMN `created_at` bigint unsigned NOT NULL DEFAULT 0 COMMENT "Create Time in Milliseconds", MODIFY COLUMN `updated_at` bigint unsigned NOT NULL DEFAULT 0 COMMENT "Update Time in Milliseconds";
-- Modify "model_entity" table
ALTER TABLE `opencoze`.`model_entity` COMMENT "Model information", MODIFY COLUMN `id` bigint unsigned NOT NULL COMMENT "id", MODIFY COLUMN `meta_id` bigint unsigned NOT NULL COMMENT "model metadata id", MODIFY COLUMN `name` varchar(128) NOT NULL COMMENT "name", MODIFY COLUMN `description` text NULL COMMENT "description", MODIFY COLUMN `default_params` json NULL COMMENT "default params", MODIFY COLUMN `scenario` bigint unsigned NOT NULL COMMENT "scenario", MODIFY COLUMN `status` int NOT NULL DEFAULT 1 COMMENT "model status", MODIFY COLUMN `deleted_at` bigint unsigned NULL COMMENT "Delete Time";
-- Modify "model_meta" table
ALTER TABLE `opencoze`.`model_meta` COMMENT "Model metadata", MODIFY COLUMN `id` bigint unsigned NOT NULL COMMENT "id", MODIFY COLUMN `model_name` varchar(128) NOT NULL COMMENT "model name", MODIFY COLUMN `protocol` varchar(128) NOT NULL COMMENT "model protocol", MODIFY COLUMN `capability` json NULL COMMENT "capability", MODIFY COLUMN `conn_config` json NULL COMMENT "model conn config", MODIFY COLUMN `status` int NOT NULL DEFAULT 1 COMMENT "model status", MODIFY COLUMN `description` varchar(2048) NOT NULL DEFAULT "" COMMENT "description", MODIFY COLUMN `deleted_at` bigint unsigned NULL COMMENT "Delete Time";
-- Modify "node_execution" table
ALTER TABLE `opencoze`.`node_execution` COMMENT "Node run record, used to record the status information of each node during each workflow execution";
-- Modify "prompt_resource" table
ALTER TABLE `opencoze`.`prompt_resource` MODIFY COLUMN `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT "id", MODIFY COLUMN `space_id` bigint NOT NULL COMMENT "space id", MODIFY COLUMN `name` varchar(255) NOT NULL COMMENT "name", MODIFY COLUMN `description` varchar(255) NOT NULL COMMENT "description", MODIFY COLUMN `prompt_text` mediumtext NULL COMMENT "prompt text", MODIFY COLUMN `status` int NOT NULL COMMENT "status, 0 is invalid, 1 is valid", MODIFY COLUMN `creator_id` bigint NOT NULL COMMENT "creator id", MODIFY COLUMN `created_at` bigint unsigned NOT NULL DEFAULT 0 COMMENT "Create Time in Milliseconds", MODIFY COLUMN `updated_at` bigint unsigned NOT NULL DEFAULT 0 COMMENT "Update Time in Milliseconds";
-- Modify "run_record" table
ALTER TABLE `opencoze`.`run_record` COMMENT "run record", MODIFY COLUMN `id` bigint unsigned NOT NULL COMMENT "id", MODIFY COLUMN `conversation_id` bigint unsigned NOT NULL DEFAULT 0 COMMENT "conversation id", MODIFY COLUMN `source` tinyint unsigned NOT NULL DEFAULT 0 COMMENT "Execute source 0 API", MODIFY COLUMN `status` varchar(255) NOT NULL DEFAULT "" COMMENT "status,0 Unknown, 1-Created,2-InProgress,3-Completed,4-Failed,5-Expired,6-Cancelled,7-RequiresAction", MODIFY COLUMN `creator_id` bigint NOT NULL DEFAULT 0 COMMENT "creator id", MODIFY COLUMN `created_at` bigint unsigned NOT NULL DEFAULT 0 COMMENT "Create Time in Milliseconds", MODIFY COLUMN `updated_at` bigint unsigned NOT NULL DEFAULT 0 COMMENT "Update Time in Milliseconds", MODIFY COLUMN `failed_at` bigint unsigned NOT NULL DEFAULT 0 COMMENT "Fail Time in Milliseconds", MODIFY COLUMN `completed_at` bigint unsigned NOT NULL DEFAULT 0 COMMENT "Finish Time in Milliseconds", MODIFY COLUMN `chat_request` text NULL COMMENT "Original request field" COLLATE utf8mb4_general_ci, MODIFY COLUMN `ext` text NULL COMMENT "ext" COLLATE utf8mb4_general_ci;
-- Modify "shortcut_command" table
ALTER TABLE `opencoze`.`shortcut_command` COMMENT "bot shortcut command table", MODIFY COLUMN `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT "id", MODIFY COLUMN `object_id` bigint unsigned NOT NULL DEFAULT 0 COMMENT "Entity ID, this command can be used for this entity", MODIFY COLUMN `command_id` bigint unsigned NOT NULL DEFAULT 0 COMMENT "command id", MODIFY COLUMN `command_name` varchar(255) NOT NULL DEFAULT "" COMMENT "command name", MODIFY COLUMN `shortcut_command` varchar(255) NOT NULL DEFAULT "" COMMENT "shortcut command", MODIFY COLUMN `description` varchar(2000) NOT NULL DEFAULT "" COMMENT "description", MODIFY COLUMN `send_type` tinyint unsigned NOT NULL DEFAULT 0 COMMENT "send type 0:query 1:panel", MODIFY COLUMN `tool_type` tinyint unsigned NOT NULL DEFAULT 0 COMMENT "Type 1 of tool used: WorkFlow 2: Plugin", MODIFY COLUMN `work_flow_id` bigint unsigned NOT NULL DEFAULT 0 COMMENT "workflow id", MODIFY COLUMN `plugin_id` bigint unsigned NOT NULL DEFAULT 0 COMMENT "plugin id", MODIFY COLUMN `plugin_tool_name` varchar(255) NOT NULL DEFAULT "" COMMENT "plugin tool name", MODIFY COLUMN `template_query` text NULL COMMENT "template query", MODIFY COLUMN `components` json NULL COMMENT "Panel parameters", MODIFY COLUMN `card_schema` text NULL COMMENT "card schema", MODIFY COLUMN `tool_info` json NULL COMMENT "Tool information includes name+variable list", MODIFY COLUMN `status` tinyint unsigned NOT NULL DEFAULT 0 COMMENT "Status, 0 is invalid, 1 is valid", MODIFY COLUMN `creator_id` bigint unsigned NULL DEFAULT 0 COMMENT "creator id", MODIFY COLUMN `is_online` tinyint unsigned NOT NULL DEFAULT 0 COMMENT "Is online information: 0 draft 1 online", MODIFY COLUMN `created_at` bigint NOT NULL DEFAULT 0 COMMENT "Create Time in Milliseconds", MODIFY COLUMN `updated_at` bigint NOT NULL DEFAULT 0 COMMENT "Update Time in Milliseconds", MODIFY COLUMN `agent_id` bigint unsigned NOT NULL DEFAULT 0 COMMENT "When executing a multi instruction, which node executes the instruction", MODIFY COLUMN `shortcut_icon` json NULL COMMENT "shortcut icon";
-- Modify "single_agent_draft" table
ALTER TABLE `opencoze`.`single_agent_draft` MODIFY COLUMN `variables_meta_id` bigint NULL COMMENT "variables meta table ID";
-- Modify "single_agent_publish" table
ALTER TABLE `opencoze`.`single_agent_publish` COMMENT "Bot connector and release version info", MODIFY COLUMN `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT "id", MODIFY COLUMN `publish_id` varchar(50) NOT NULL DEFAULT "" COMMENT "publish id" COLLATE utf8mb4_general_ci, MODIFY COLUMN `connector_ids` json NULL COMMENT "connector_ids", MODIFY COLUMN `publish_info` text NULL COMMENT "publish info" COLLATE utf8mb4_general_ci, MODIFY COLUMN `publish_time` bigint unsigned NOT NULL DEFAULT 0 COMMENT "publish time", MODIFY COLUMN `creator_id` bigint unsigned NOT NULL DEFAULT 0 COMMENT "creator id", MODIFY COLUMN `status` tinyint NOT NULL DEFAULT 0 COMMENT "Status 0: In use 1: Delete 3: Disabled", MODIFY COLUMN `extra` json NULL COMMENT "extra";
-- Modify "single_agent_version" table
ALTER TABLE `opencoze`.`single_agent_version` MODIFY COLUMN `variables_meta_id` bigint NULL COMMENT "variables meta table ID";
-- Modify "variable_instance" table
ALTER TABLE `opencoze`.`variable_instance` MODIFY COLUMN `id` bigint unsigned NOT NULL DEFAULT 0 COMMENT "id", MODIFY COLUMN `version` varchar(255) NOT NULL COMMENT "agent or project version empty represents draft status", MODIFY COLUMN `keyword` varchar(255) NOT NULL COMMENT "Keyword to Memory", MODIFY COLUMN `type` tinyint NOT NULL COMMENT "Memory type 1 KV 2 list", MODIFY COLUMN `content` text NULL COMMENT "content", MODIFY COLUMN `connector_uid` varchar(255) NOT NULL COMMENT "connector_uid", MODIFY COLUMN `connector_id` bigint NOT NULL COMMENT "connector_id, e.g. coze = 10000010", MODIFY COLUMN `created_at` bigint unsigned NOT NULL DEFAULT 0 COMMENT "Create Time in Milliseconds", MODIFY COLUMN `updated_at` bigint unsigned NOT NULL DEFAULT 0 COMMENT "Update Time in Milliseconds";
-- Modify "variables_meta" table
ALTER TABLE `opencoze`.`variables_meta` MODIFY COLUMN `id` bigint unsigned NOT NULL DEFAULT 0 COMMENT "id", MODIFY COLUMN `creator_id` bigint unsigned NOT NULL COMMENT "creator id", MODIFY COLUMN `variable_list` json NULL COMMENT "JSON data for variable configuration", MODIFY COLUMN `created_at` bigint unsigned NOT NULL DEFAULT 0 COMMENT "Create Time in Milliseconds", MODIFY COLUMN `updated_at` bigint unsigned NOT NULL DEFAULT 0 COMMENT "Update Time in Milliseconds", MODIFY COLUMN `version` varchar(255) NOT NULL COMMENT "Project version, empty represents draft status";
-- Modify "workflow_draft" table
ALTER TABLE `opencoze`.`workflow_draft` COMMENT "Workflow canvas draft table, used to record the latest draft canvas information of workflow", MODIFY COLUMN `canvas` mediumtext NOT NULL COMMENT "Front end schema", MODIFY COLUMN `input_params` mediumtext NULL COMMENT "Input schema", MODIFY COLUMN `output_params` mediumtext NULL COMMENT "Output parameter schema", MODIFY COLUMN `test_run_success` bool NOT NULL DEFAULT 0 COMMENT "0 not running, 1 running successfully", MODIFY COLUMN `modified` bool NOT NULL DEFAULT 0 COMMENT "0 has not been modified, 1 has been modified", MODIFY COLUMN `updated_at` bigint unsigned NULL COMMENT "Update Time in Milliseconds", MODIFY COLUMN `deleted_at` datetime(3) NULL COMMENT "Delete Time";
-- Modify "workflow_execution" table
ALTER TABLE `opencoze`.`workflow_execution` COMMENT "Workflow Execution Record Table, used to record the status of each workflow execution";
-- Modify "workflow_meta" table
ALTER TABLE `opencoze`.`workflow_meta` COMMENT "The workflow metadata table,used to record the basic metadata of workflow", MODIFY COLUMN `status` tinyint unsigned NOT NULL COMMENT "0: Not published, 1: Published", MODIFY COLUMN `content_type` tinyint unsigned NOT NULL COMMENT "0 Users 1 Official", MODIFY COLUMN `author_id` bigint unsigned NOT NULL COMMENT "Original author user ID", MODIFY COLUMN `space_id` bigint unsigned NOT NULL COMMENT "space id", MODIFY COLUMN `updater_id` bigint unsigned NULL COMMENT "User ID for updating metadata", MODIFY COLUMN `source_id` bigint unsigned NULL COMMENT "Workflow ID of source", MODIFY COLUMN `app_id` bigint unsigned NULL COMMENT "app id";
-- Modify "workflow_reference" table
ALTER TABLE `opencoze`.`workflow_reference` COMMENT "The workflow association table,used to record the direct mutual reference relationship between workflows", MODIFY COLUMN `deleted_at` datetime(3) NULL COMMENT "Delete Time";
-- Modify "workflow_snapshot" table
ALTER TABLE `opencoze`.`workflow_snapshot` MODIFY COLUMN `created_at` bigint unsigned NOT NULL COMMENT "Create Time in Milliseconds";
-- Modify "workflow_version" table
ALTER TABLE `opencoze`.`workflow_version` COMMENT "Workflow Canvas Version Information Table, used to record canvas information for different versions", MODIFY COLUMN `version` varchar(50) NOT NULL COMMENT "Published version", MODIFY COLUMN `version_description` varchar(2000) NOT NULL COMMENT "Version Description", MODIFY COLUMN `canvas` mediumtext NOT NULL COMMENT "Front end schema", MODIFY COLUMN `input_params` mediumtext NULL COMMENT "input params", MODIFY COLUMN `output_params` mediumtext NULL COMMENT "output params", MODIFY COLUMN `creator_id` bigint unsigned NOT NULL COMMENT "creator id", MODIFY COLUMN `created_at` bigint unsigned NOT NULL COMMENT "Create Time in Milliseconds", MODIFY COLUMN `deleted_at` datetime(3) NULL COMMENT "Delete Time";
