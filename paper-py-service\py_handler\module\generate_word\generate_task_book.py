import os
from pydoc import doc
from docx import Document
from docx.shared import Inches,Pt
from flask import Flask, json, request, send_file,Blueprint
from docx.enum.text import WD_BREAK 
import io
from docx.oxml.shared import OxmlElement
from docx.oxml.shared import qn
from .routes import generate_app
from .generate_word import insert_content_paragraphs


def insert_content(doc, paragraph, content):
   # 一对花括号{{}}在f-string中会被解释为一个字面意义上的单个花括号
    parsed_value = json.loads(content)
     
    # 识别是对象|数组|字符串
    if isinstance(parsed_value, dict):
        # "对象"
        insert_content_paragraphs(doc, paragraph, parsed_value.get('content', ''))
    elif isinstance(parsed_value, list):
        # "数组"
        for index,item in enumerate(parsed_value):
            parseItem = json.loads(item)
            level = parseItem.get('level', 1)
            target = parseItem.get("target", '')
            content = parseItem.get('content', '')

            # 创建新段落并设置文本
            new_paragraph = paragraph.insert_paragraph_before(target)
            
            # 根据level设置字体大小
            if level == 1:
                new_paragraph.style = doc.styles['Normal']
                new_paragraph.runs[0].font.size = Pt(13)  # 设置level 1的字体大小为16磅
            else:
                new_paragraph.style = doc.styles['Normal']
                new_paragraph.runs[0].font.size = Pt(10)  # 设置其他level的字体大小为14磅

            new_paragraph.paragraph_format.left_indent = Inches(0.75)
            new_paragraph.paragraph_format.right_indent = Inches(0.75)
            
            if level == 1:
                # 每个章节下，再插入一个空行
                paragraph.insert_paragraph_before('')

            # 正文
            insert_content_paragraphs(doc, paragraph, content)

            # 每个小章节尾部插入一个空行,最后一个不插入
            if level != 1 and index != len(parsed_value) - 1:
                paragraph.insert_paragraph_before('')       
                
                
def get_paragraph_text(paragraph):
    """获取段落文本内容"""
    return ''.join([run.text for run in paragraph.runs]) if hasattr(paragraph, 'runs') else paragraph.text


def process_paragraph(doc, paragraph, key_words):
    """处理单个段落"""
    text = get_paragraph_text(paragraph)
    if not text or text.strip() == '':
        return
    
    if text in key_words:
        # 剔除占位符ext
        paragraph.text = paragraph.text.replace(text, '')
        # 正文
        insert_content_paragraphs(doc, paragraph, key_words[text])
           
        

@generate_app.route('/task_book', methods=['POST'])
def generate_task_book():
    # 获取表单数据
    title = request.form.get('title','')
    key_arr = {
        "{{title}}": request.form.get('title',''),
        "{{topic_source}}": request.form.get("topic_source",''),
        "{{main_content}}": request.form.get('main_content',''),
        "{{completion_result}}": request.form.get('completion_result',''),
        "{{reference}}": request.form.get('reference','')
    }
    
    
    print("Content-Type:", request.headers.get('Content-Type'))
    # 打开模板文档
    current_dir = os.path.dirname(os.path.abspath(__file__))
    template_path = os.path.join(current_dir, "..", "..", "static", "任务书.docx")
    doc = Document(template_path)
    
    paragraphs_to_remove = []
    
    # 遍历文档中的所有段落和表格
    all_tables = doc.tables
    
    for table in all_tables:
        for row in table.rows:
            for cell in row.cells:
                for paragraph in cell.paragraphs:
                    process_paragraph(doc, paragraph, key_arr)
        
    # 标题            
    for paragraph in doc.paragraphs:
        if "{{title}}" in paragraph.text.strip():
            # 标题
            # 遍历所有的 runs 来保持格式
            for run in paragraph.runs:
                run.text = run.text.replace("{{title}}", title)
            break


    # 循环结束后，删除标记的空段落
    for para in paragraphs_to_remove:
        p = para._element
        p.getparent().remove(p)
        
    
    # 将修改后的文档保存到内存中
    output = io.BytesIO()
    doc.save(output)
    output.seek(0)

    # 保存到本地
    # output_dir = os.path.join(current_dir, "..", "..", "output")
    # os.makedirs(output_dir, exist_ok=True)
    # output_path = os.path.join(output_dir, "report.docx")
    # doc.save(output_path)
    
    # 返回生成的文档
    return send_file(output, mimetype='application/vnd.openxmlformats-officedocument.wordprocessingml.document', as_attachment=True, download_name='开题报告.docx')
