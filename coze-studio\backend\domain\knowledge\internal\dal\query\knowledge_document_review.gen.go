// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/coze-dev/coze-studio/backend/domain/knowledge/internal/dal/model"
)

func newKnowledgeDocumentReview(db *gorm.DB, opts ...gen.DOOption) knowledgeDocumentReview {
	_knowledgeDocumentReview := knowledgeDocumentReview{}

	_knowledgeDocumentReview.knowledgeDocumentReviewDo.UseDB(db, opts...)
	_knowledgeDocumentReview.knowledgeDocumentReviewDo.UseModel(&model.KnowledgeDocumentReview{})

	tableName := _knowledgeDocumentReview.knowledgeDocumentReviewDo.TableName()
	_knowledgeDocumentReview.ALL = field.NewAsterisk(tableName)
	_knowledgeDocumentReview.ID = field.NewInt64(tableName, "id")
	_knowledgeDocumentReview.KnowledgeID = field.NewInt64(tableName, "knowledge_id")
	_knowledgeDocumentReview.SpaceID = field.NewInt64(tableName, "space_id")
	_knowledgeDocumentReview.Name = field.NewString(tableName, "name")
	_knowledgeDocumentReview.Type = field.NewString(tableName, "type")
	_knowledgeDocumentReview.URI = field.NewString(tableName, "uri")
	_knowledgeDocumentReview.FormatType = field.NewInt32(tableName, "format_type")
	_knowledgeDocumentReview.Status = field.NewInt32(tableName, "status")
	_knowledgeDocumentReview.ChunkRespURI = field.NewString(tableName, "chunk_resp_uri")
	_knowledgeDocumentReview.DeletedAt = field.NewField(tableName, "deleted_at")
	_knowledgeDocumentReview.CreatedAt = field.NewInt64(tableName, "created_at")
	_knowledgeDocumentReview.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_knowledgeDocumentReview.CreatorID = field.NewInt64(tableName, "creator_id")

	_knowledgeDocumentReview.fillFieldMap()

	return _knowledgeDocumentReview
}

// knowledgeDocumentReview Document slice preview info
type knowledgeDocumentReview struct {
	knowledgeDocumentReviewDo

	ALL          field.Asterisk
	ID           field.Int64  // id
	KnowledgeID  field.Int64  // knowledge id
	SpaceID      field.Int64  // space id
	Name         field.String // name
	Type         field.String // document type
	URI          field.String // uri
	FormatType   field.Int32  // 0 text, 1 table, 2 images
	Status       field.Int32  // 0 Processing 1 Completed 2 Failed 3 Expired
	ChunkRespURI field.String // pre-sliced uri
	DeletedAt    field.Field  // Delete Time
	CreatedAt    field.Int64  // Create Time in Milliseconds
	UpdatedAt    field.Int64  // Update Time in Milliseconds
	CreatorID    field.Int64  // creator id

	fieldMap map[string]field.Expr
}

func (k knowledgeDocumentReview) Table(newTableName string) *knowledgeDocumentReview {
	k.knowledgeDocumentReviewDo.UseTable(newTableName)
	return k.updateTableName(newTableName)
}

func (k knowledgeDocumentReview) As(alias string) *knowledgeDocumentReview {
	k.knowledgeDocumentReviewDo.DO = *(k.knowledgeDocumentReviewDo.As(alias).(*gen.DO))
	return k.updateTableName(alias)
}

func (k *knowledgeDocumentReview) updateTableName(table string) *knowledgeDocumentReview {
	k.ALL = field.NewAsterisk(table)
	k.ID = field.NewInt64(table, "id")
	k.KnowledgeID = field.NewInt64(table, "knowledge_id")
	k.SpaceID = field.NewInt64(table, "space_id")
	k.Name = field.NewString(table, "name")
	k.Type = field.NewString(table, "type")
	k.URI = field.NewString(table, "uri")
	k.FormatType = field.NewInt32(table, "format_type")
	k.Status = field.NewInt32(table, "status")
	k.ChunkRespURI = field.NewString(table, "chunk_resp_uri")
	k.DeletedAt = field.NewField(table, "deleted_at")
	k.CreatedAt = field.NewInt64(table, "created_at")
	k.UpdatedAt = field.NewInt64(table, "updated_at")
	k.CreatorID = field.NewInt64(table, "creator_id")

	k.fillFieldMap()

	return k
}

func (k *knowledgeDocumentReview) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := k.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (k *knowledgeDocumentReview) fillFieldMap() {
	k.fieldMap = make(map[string]field.Expr, 13)
	k.fieldMap["id"] = k.ID
	k.fieldMap["knowledge_id"] = k.KnowledgeID
	k.fieldMap["space_id"] = k.SpaceID
	k.fieldMap["name"] = k.Name
	k.fieldMap["type"] = k.Type
	k.fieldMap["uri"] = k.URI
	k.fieldMap["format_type"] = k.FormatType
	k.fieldMap["status"] = k.Status
	k.fieldMap["chunk_resp_uri"] = k.ChunkRespURI
	k.fieldMap["deleted_at"] = k.DeletedAt
	k.fieldMap["created_at"] = k.CreatedAt
	k.fieldMap["updated_at"] = k.UpdatedAt
	k.fieldMap["creator_id"] = k.CreatorID
}

func (k knowledgeDocumentReview) clone(db *gorm.DB) knowledgeDocumentReview {
	k.knowledgeDocumentReviewDo.ReplaceConnPool(db.Statement.ConnPool)
	return k
}

func (k knowledgeDocumentReview) replaceDB(db *gorm.DB) knowledgeDocumentReview {
	k.knowledgeDocumentReviewDo.ReplaceDB(db)
	return k
}

type knowledgeDocumentReviewDo struct{ gen.DO }

type IKnowledgeDocumentReviewDo interface {
	gen.SubQuery
	Debug() IKnowledgeDocumentReviewDo
	WithContext(ctx context.Context) IKnowledgeDocumentReviewDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IKnowledgeDocumentReviewDo
	WriteDB() IKnowledgeDocumentReviewDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IKnowledgeDocumentReviewDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IKnowledgeDocumentReviewDo
	Not(conds ...gen.Condition) IKnowledgeDocumentReviewDo
	Or(conds ...gen.Condition) IKnowledgeDocumentReviewDo
	Select(conds ...field.Expr) IKnowledgeDocumentReviewDo
	Where(conds ...gen.Condition) IKnowledgeDocumentReviewDo
	Order(conds ...field.Expr) IKnowledgeDocumentReviewDo
	Distinct(cols ...field.Expr) IKnowledgeDocumentReviewDo
	Omit(cols ...field.Expr) IKnowledgeDocumentReviewDo
	Join(table schema.Tabler, on ...field.Expr) IKnowledgeDocumentReviewDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IKnowledgeDocumentReviewDo
	RightJoin(table schema.Tabler, on ...field.Expr) IKnowledgeDocumentReviewDo
	Group(cols ...field.Expr) IKnowledgeDocumentReviewDo
	Having(conds ...gen.Condition) IKnowledgeDocumentReviewDo
	Limit(limit int) IKnowledgeDocumentReviewDo
	Offset(offset int) IKnowledgeDocumentReviewDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IKnowledgeDocumentReviewDo
	Unscoped() IKnowledgeDocumentReviewDo
	Create(values ...*model.KnowledgeDocumentReview) error
	CreateInBatches(values []*model.KnowledgeDocumentReview, batchSize int) error
	Save(values ...*model.KnowledgeDocumentReview) error
	First() (*model.KnowledgeDocumentReview, error)
	Take() (*model.KnowledgeDocumentReview, error)
	Last() (*model.KnowledgeDocumentReview, error)
	Find() ([]*model.KnowledgeDocumentReview, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.KnowledgeDocumentReview, err error)
	FindInBatches(result *[]*model.KnowledgeDocumentReview, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.KnowledgeDocumentReview) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IKnowledgeDocumentReviewDo
	Assign(attrs ...field.AssignExpr) IKnowledgeDocumentReviewDo
	Joins(fields ...field.RelationField) IKnowledgeDocumentReviewDo
	Preload(fields ...field.RelationField) IKnowledgeDocumentReviewDo
	FirstOrInit() (*model.KnowledgeDocumentReview, error)
	FirstOrCreate() (*model.KnowledgeDocumentReview, error)
	FindByPage(offset int, limit int) (result []*model.KnowledgeDocumentReview, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IKnowledgeDocumentReviewDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (k knowledgeDocumentReviewDo) Debug() IKnowledgeDocumentReviewDo {
	return k.withDO(k.DO.Debug())
}

func (k knowledgeDocumentReviewDo) WithContext(ctx context.Context) IKnowledgeDocumentReviewDo {
	return k.withDO(k.DO.WithContext(ctx))
}

func (k knowledgeDocumentReviewDo) ReadDB() IKnowledgeDocumentReviewDo {
	return k.Clauses(dbresolver.Read)
}

func (k knowledgeDocumentReviewDo) WriteDB() IKnowledgeDocumentReviewDo {
	return k.Clauses(dbresolver.Write)
}

func (k knowledgeDocumentReviewDo) Session(config *gorm.Session) IKnowledgeDocumentReviewDo {
	return k.withDO(k.DO.Session(config))
}

func (k knowledgeDocumentReviewDo) Clauses(conds ...clause.Expression) IKnowledgeDocumentReviewDo {
	return k.withDO(k.DO.Clauses(conds...))
}

func (k knowledgeDocumentReviewDo) Returning(value interface{}, columns ...string) IKnowledgeDocumentReviewDo {
	return k.withDO(k.DO.Returning(value, columns...))
}

func (k knowledgeDocumentReviewDo) Not(conds ...gen.Condition) IKnowledgeDocumentReviewDo {
	return k.withDO(k.DO.Not(conds...))
}

func (k knowledgeDocumentReviewDo) Or(conds ...gen.Condition) IKnowledgeDocumentReviewDo {
	return k.withDO(k.DO.Or(conds...))
}

func (k knowledgeDocumentReviewDo) Select(conds ...field.Expr) IKnowledgeDocumentReviewDo {
	return k.withDO(k.DO.Select(conds...))
}

func (k knowledgeDocumentReviewDo) Where(conds ...gen.Condition) IKnowledgeDocumentReviewDo {
	return k.withDO(k.DO.Where(conds...))
}

func (k knowledgeDocumentReviewDo) Order(conds ...field.Expr) IKnowledgeDocumentReviewDo {
	return k.withDO(k.DO.Order(conds...))
}

func (k knowledgeDocumentReviewDo) Distinct(cols ...field.Expr) IKnowledgeDocumentReviewDo {
	return k.withDO(k.DO.Distinct(cols...))
}

func (k knowledgeDocumentReviewDo) Omit(cols ...field.Expr) IKnowledgeDocumentReviewDo {
	return k.withDO(k.DO.Omit(cols...))
}

func (k knowledgeDocumentReviewDo) Join(table schema.Tabler, on ...field.Expr) IKnowledgeDocumentReviewDo {
	return k.withDO(k.DO.Join(table, on...))
}

func (k knowledgeDocumentReviewDo) LeftJoin(table schema.Tabler, on ...field.Expr) IKnowledgeDocumentReviewDo {
	return k.withDO(k.DO.LeftJoin(table, on...))
}

func (k knowledgeDocumentReviewDo) RightJoin(table schema.Tabler, on ...field.Expr) IKnowledgeDocumentReviewDo {
	return k.withDO(k.DO.RightJoin(table, on...))
}

func (k knowledgeDocumentReviewDo) Group(cols ...field.Expr) IKnowledgeDocumentReviewDo {
	return k.withDO(k.DO.Group(cols...))
}

func (k knowledgeDocumentReviewDo) Having(conds ...gen.Condition) IKnowledgeDocumentReviewDo {
	return k.withDO(k.DO.Having(conds...))
}

func (k knowledgeDocumentReviewDo) Limit(limit int) IKnowledgeDocumentReviewDo {
	return k.withDO(k.DO.Limit(limit))
}

func (k knowledgeDocumentReviewDo) Offset(offset int) IKnowledgeDocumentReviewDo {
	return k.withDO(k.DO.Offset(offset))
}

func (k knowledgeDocumentReviewDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IKnowledgeDocumentReviewDo {
	return k.withDO(k.DO.Scopes(funcs...))
}

func (k knowledgeDocumentReviewDo) Unscoped() IKnowledgeDocumentReviewDo {
	return k.withDO(k.DO.Unscoped())
}

func (k knowledgeDocumentReviewDo) Create(values ...*model.KnowledgeDocumentReview) error {
	if len(values) == 0 {
		return nil
	}
	return k.DO.Create(values)
}

func (k knowledgeDocumentReviewDo) CreateInBatches(values []*model.KnowledgeDocumentReview, batchSize int) error {
	return k.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (k knowledgeDocumentReviewDo) Save(values ...*model.KnowledgeDocumentReview) error {
	if len(values) == 0 {
		return nil
	}
	return k.DO.Save(values)
}

func (k knowledgeDocumentReviewDo) First() (*model.KnowledgeDocumentReview, error) {
	if result, err := k.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.KnowledgeDocumentReview), nil
	}
}

func (k knowledgeDocumentReviewDo) Take() (*model.KnowledgeDocumentReview, error) {
	if result, err := k.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.KnowledgeDocumentReview), nil
	}
}

func (k knowledgeDocumentReviewDo) Last() (*model.KnowledgeDocumentReview, error) {
	if result, err := k.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.KnowledgeDocumentReview), nil
	}
}

func (k knowledgeDocumentReviewDo) Find() ([]*model.KnowledgeDocumentReview, error) {
	result, err := k.DO.Find()
	return result.([]*model.KnowledgeDocumentReview), err
}

func (k knowledgeDocumentReviewDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.KnowledgeDocumentReview, err error) {
	buf := make([]*model.KnowledgeDocumentReview, 0, batchSize)
	err = k.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (k knowledgeDocumentReviewDo) FindInBatches(result *[]*model.KnowledgeDocumentReview, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return k.DO.FindInBatches(result, batchSize, fc)
}

func (k knowledgeDocumentReviewDo) Attrs(attrs ...field.AssignExpr) IKnowledgeDocumentReviewDo {
	return k.withDO(k.DO.Attrs(attrs...))
}

func (k knowledgeDocumentReviewDo) Assign(attrs ...field.AssignExpr) IKnowledgeDocumentReviewDo {
	return k.withDO(k.DO.Assign(attrs...))
}

func (k knowledgeDocumentReviewDo) Joins(fields ...field.RelationField) IKnowledgeDocumentReviewDo {
	for _, _f := range fields {
		k = *k.withDO(k.DO.Joins(_f))
	}
	return &k
}

func (k knowledgeDocumentReviewDo) Preload(fields ...field.RelationField) IKnowledgeDocumentReviewDo {
	for _, _f := range fields {
		k = *k.withDO(k.DO.Preload(_f))
	}
	return &k
}

func (k knowledgeDocumentReviewDo) FirstOrInit() (*model.KnowledgeDocumentReview, error) {
	if result, err := k.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.KnowledgeDocumentReview), nil
	}
}

func (k knowledgeDocumentReviewDo) FirstOrCreate() (*model.KnowledgeDocumentReview, error) {
	if result, err := k.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.KnowledgeDocumentReview), nil
	}
}

func (k knowledgeDocumentReviewDo) FindByPage(offset int, limit int) (result []*model.KnowledgeDocumentReview, count int64, err error) {
	result, err = k.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = k.Offset(-1).Limit(-1).Count()
	return
}

func (k knowledgeDocumentReviewDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = k.Count()
	if err != nil {
		return
	}

	err = k.Offset(offset).Limit(limit).Scan(result)
	return
}

func (k knowledgeDocumentReviewDo) Scan(result interface{}) (err error) {
	return k.DO.Scan(result)
}

func (k knowledgeDocumentReviewDo) Delete(models ...*model.KnowledgeDocumentReview) (result gen.ResultInfo, err error) {
	return k.DO.Delete(models)
}

func (k *knowledgeDocumentReviewDo) withDO(do gen.Dao) *knowledgeDocumentReviewDo {
	k.DO = *do.(*gen.DO)
	return k
}
