{"name": "@coze-arch/import-watch-loader", "version": "1.0.0", "description": "", "license": "Apache-2.0", "author": "<EMAIL>", "main": "index.js", "scripts": {"build": "exit 0", "lint": "eslint ./", "test": "vitest --run --passWithNoTests", "test:cov": "npm run test -- --coverage"}, "devDependencies": {"@coze-arch/eslint-config": "workspace:*", "@coze-arch/ts-config": "workspace:*", "@coze-arch/vitest-config": "workspace:*", "@vitest/coverage-v8": "~3.0.5", "vitest": "~3.0.5"}}