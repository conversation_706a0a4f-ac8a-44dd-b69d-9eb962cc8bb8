# ===========================================
# 服务隔离的存储配置 - 每个服务独立的存储
# ===========================================

# Paper Editor API 存储
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: paper-editor-postgres-pvc
  namespace: paper-services
  labels:
    app: paper-editor-api
    component: database
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: paper-editor-redis-pvc
  namespace: paper-services
  labels:
    app: paper-editor-api
    component: cache
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 1Gi
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: paper-editor-uploads-pvc
  namespace: paper-services
  labels:
    app: paper-editor-api
    component: storage
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
---
# Dify 存储
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: dify-postgres-pvc
  namespace: paper-services
  labels:
    app: dify
    component: database
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: dify-redis-pvc
  namespace: paper-services
  labels:
    app: dify
    component: cache
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 2Gi
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: dify-storage-pvc
  namespace: paper-services
  labels:
    app: dify
    component: storage
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 20Gi
---
# Paper Node Service 存储（临时文件）
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: paper-node-temp-pvc
  namespace: paper-services
  labels:
    app: paper-node-service
    component: temp-storage
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 2Gi
---
# Paper Python Service 存储（数据处理）
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: paper-py-data-pvc
  namespace: paper-services
  labels:
    app: paper-py-service
    component: data-storage
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi
