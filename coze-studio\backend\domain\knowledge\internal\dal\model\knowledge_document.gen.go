// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"github.com/coze-dev/coze-studio/backend/domain/knowledge/entity"
	"gorm.io/gorm"
)

const TableNameKnowledgeDocument = "knowledge_document"

// KnowledgeDocument knowledge document info
type KnowledgeDocument struct {
	ID            int64              `gorm:"column:id;primaryKey;comment:id" json:"id"`                                                                 // id
	KnowledgeID   int64              `gorm:"column:knowledge_id;not null;comment:knowledge id" json:"knowledge_id"`                                     // knowledge id
	Name          string             `gorm:"column:name;not null;comment:document name" json:"name"`                                                    // document name
	FileExtension string             `gorm:"column:file_extension;not null;default:0;comment:Document type, txt/pdf/csv etc.." json:"file_extension"`   // Document type, txt/pdf/csv etc..
	DocumentType  int32              `gorm:"column:document_type;not null;comment:Document type: 0: Text 1: Table 2: Image" json:"document_type"`       // Document type: 0: Text 1: Table 2: Image
	URI           string             `gorm:"column:uri;comment:uri" json:"uri"`                                                                         // uri
	Size          int64              `gorm:"column:size;not null;comment:document size" json:"size"`                                                    // document size
	SliceCount    int64              `gorm:"column:slice_count;not null;comment:slice count" json:"slice_count"`                                        // slice count
	CharCount     int64              `gorm:"column:char_count;not null;comment:number of characters" json:"char_count"`                                 // number of characters
	CreatorID     int64              `gorm:"column:creator_id;not null;comment:creator id" json:"creator_id"`                                           // creator id
	SpaceID       int64              `gorm:"column:space_id;not null;comment:space id" json:"space_id"`                                                 // space id
	CreatedAt     int64              `gorm:"column:created_at;not null;comment:Create Time in Milliseconds" json:"created_at"`                          // Create Time in Milliseconds
	UpdatedAt     int64              `gorm:"column:updated_at;not null;comment:Update Time in Milliseconds" json:"updated_at"`                          // Update Time in Milliseconds
	DeletedAt     gorm.DeletedAt     `gorm:"column:deleted_at;comment:Delete Time" json:"deleted_at"`                                                   // Delete Time
	SourceType    int32              `gorm:"column:source_type;comment:0: Local file upload, 2: Custom text, 103: Feishu 104: Lark" json:"source_type"` // 0: Local file upload, 2: Custom text, 103: Feishu 104: Lark
	Status        int32              `gorm:"column:status;not null;comment:status" json:"status"`                                                       // status
	FailReason    string             `gorm:"column:fail_reason;comment:fail reason" json:"fail_reason"`                                                 // fail reason
	ParseRule     *DocumentParseRule `gorm:"column:parse_rule;comment:parse rule;serializer:json" json:"parse_rule"`                                    // parse rule
	TableInfo     *entity.TableInfo  `gorm:"column:table_info;comment:table info;serializer:json" json:"table_info"`                                    // table info
}

// TableName KnowledgeDocument's table name
func (*KnowledgeDocument) TableName() string {
	return TableNameKnowledgeDocument
}
