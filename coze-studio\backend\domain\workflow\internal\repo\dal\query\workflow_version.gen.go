// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/coze-dev/coze-studio/backend/domain/workflow/internal/repo/dal/model"
)

func newWorkflowVersion(db *gorm.DB, opts ...gen.DOOption) workflowVersion {
	_workflowVersion := workflowVersion{}

	_workflowVersion.workflowVersionDo.UseDB(db, opts...)
	_workflowVersion.workflowVersionDo.UseModel(&model.WorkflowVersion{})

	tableName := _workflowVersion.workflowVersionDo.TableName()
	_workflowVersion.ALL = field.NewAsterisk(tableName)
	_workflowVersion.ID = field.NewInt64(tableName, "id")
	_workflowVersion.WorkflowID = field.NewInt64(tableName, "workflow_id")
	_workflowVersion.Version = field.NewString(tableName, "version")
	_workflowVersion.VersionDescription = field.NewString(tableName, "version_description")
	_workflowVersion.Canvas = field.NewString(tableName, "canvas")
	_workflowVersion.InputParams = field.NewString(tableName, "input_params")
	_workflowVersion.OutputParams = field.NewString(tableName, "output_params")
	_workflowVersion.CreatorID = field.NewInt64(tableName, "creator_id")
	_workflowVersion.CreatedAt = field.NewInt64(tableName, "created_at")
	_workflowVersion.DeletedAt = field.NewField(tableName, "deleted_at")
	_workflowVersion.CommitID = field.NewString(tableName, "commit_id")

	_workflowVersion.fillFieldMap()

	return _workflowVersion
}

// workflowVersion Workflow Canvas Version Information Table, used to record canvas information for different versions
type workflowVersion struct {
	workflowVersionDo

	ALL                field.Asterisk
	ID                 field.Int64  // ID
	WorkflowID         field.Int64  // workflow id
	Version            field.String // Published version
	VersionDescription field.String // Version Description
	Canvas             field.String // Front end schema
	InputParams        field.String // input params
	OutputParams       field.String // output params
	CreatorID          field.Int64  // creator id
	CreatedAt          field.Int64  // Create Time in Milliseconds
	DeletedAt          field.Field  // Delete Time
	CommitID           field.String // the commit id corresponding to this version

	fieldMap map[string]field.Expr
}

func (w workflowVersion) Table(newTableName string) *workflowVersion {
	w.workflowVersionDo.UseTable(newTableName)
	return w.updateTableName(newTableName)
}

func (w workflowVersion) As(alias string) *workflowVersion {
	w.workflowVersionDo.DO = *(w.workflowVersionDo.As(alias).(*gen.DO))
	return w.updateTableName(alias)
}

func (w *workflowVersion) updateTableName(table string) *workflowVersion {
	w.ALL = field.NewAsterisk(table)
	w.ID = field.NewInt64(table, "id")
	w.WorkflowID = field.NewInt64(table, "workflow_id")
	w.Version = field.NewString(table, "version")
	w.VersionDescription = field.NewString(table, "version_description")
	w.Canvas = field.NewString(table, "canvas")
	w.InputParams = field.NewString(table, "input_params")
	w.OutputParams = field.NewString(table, "output_params")
	w.CreatorID = field.NewInt64(table, "creator_id")
	w.CreatedAt = field.NewInt64(table, "created_at")
	w.DeletedAt = field.NewField(table, "deleted_at")
	w.CommitID = field.NewString(table, "commit_id")

	w.fillFieldMap()

	return w
}

func (w *workflowVersion) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := w.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (w *workflowVersion) fillFieldMap() {
	w.fieldMap = make(map[string]field.Expr, 11)
	w.fieldMap["id"] = w.ID
	w.fieldMap["workflow_id"] = w.WorkflowID
	w.fieldMap["version"] = w.Version
	w.fieldMap["version_description"] = w.VersionDescription
	w.fieldMap["canvas"] = w.Canvas
	w.fieldMap["input_params"] = w.InputParams
	w.fieldMap["output_params"] = w.OutputParams
	w.fieldMap["creator_id"] = w.CreatorID
	w.fieldMap["created_at"] = w.CreatedAt
	w.fieldMap["deleted_at"] = w.DeletedAt
	w.fieldMap["commit_id"] = w.CommitID
}

func (w workflowVersion) clone(db *gorm.DB) workflowVersion {
	w.workflowVersionDo.ReplaceConnPool(db.Statement.ConnPool)
	return w
}

func (w workflowVersion) replaceDB(db *gorm.DB) workflowVersion {
	w.workflowVersionDo.ReplaceDB(db)
	return w
}

type workflowVersionDo struct{ gen.DO }

type IWorkflowVersionDo interface {
	gen.SubQuery
	Debug() IWorkflowVersionDo
	WithContext(ctx context.Context) IWorkflowVersionDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IWorkflowVersionDo
	WriteDB() IWorkflowVersionDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IWorkflowVersionDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IWorkflowVersionDo
	Not(conds ...gen.Condition) IWorkflowVersionDo
	Or(conds ...gen.Condition) IWorkflowVersionDo
	Select(conds ...field.Expr) IWorkflowVersionDo
	Where(conds ...gen.Condition) IWorkflowVersionDo
	Order(conds ...field.Expr) IWorkflowVersionDo
	Distinct(cols ...field.Expr) IWorkflowVersionDo
	Omit(cols ...field.Expr) IWorkflowVersionDo
	Join(table schema.Tabler, on ...field.Expr) IWorkflowVersionDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IWorkflowVersionDo
	RightJoin(table schema.Tabler, on ...field.Expr) IWorkflowVersionDo
	Group(cols ...field.Expr) IWorkflowVersionDo
	Having(conds ...gen.Condition) IWorkflowVersionDo
	Limit(limit int) IWorkflowVersionDo
	Offset(offset int) IWorkflowVersionDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IWorkflowVersionDo
	Unscoped() IWorkflowVersionDo
	Create(values ...*model.WorkflowVersion) error
	CreateInBatches(values []*model.WorkflowVersion, batchSize int) error
	Save(values ...*model.WorkflowVersion) error
	First() (*model.WorkflowVersion, error)
	Take() (*model.WorkflowVersion, error)
	Last() (*model.WorkflowVersion, error)
	Find() ([]*model.WorkflowVersion, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.WorkflowVersion, err error)
	FindInBatches(result *[]*model.WorkflowVersion, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.WorkflowVersion) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IWorkflowVersionDo
	Assign(attrs ...field.AssignExpr) IWorkflowVersionDo
	Joins(fields ...field.RelationField) IWorkflowVersionDo
	Preload(fields ...field.RelationField) IWorkflowVersionDo
	FirstOrInit() (*model.WorkflowVersion, error)
	FirstOrCreate() (*model.WorkflowVersion, error)
	FindByPage(offset int, limit int) (result []*model.WorkflowVersion, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IWorkflowVersionDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (w workflowVersionDo) Debug() IWorkflowVersionDo {
	return w.withDO(w.DO.Debug())
}

func (w workflowVersionDo) WithContext(ctx context.Context) IWorkflowVersionDo {
	return w.withDO(w.DO.WithContext(ctx))
}

func (w workflowVersionDo) ReadDB() IWorkflowVersionDo {
	return w.Clauses(dbresolver.Read)
}

func (w workflowVersionDo) WriteDB() IWorkflowVersionDo {
	return w.Clauses(dbresolver.Write)
}

func (w workflowVersionDo) Session(config *gorm.Session) IWorkflowVersionDo {
	return w.withDO(w.DO.Session(config))
}

func (w workflowVersionDo) Clauses(conds ...clause.Expression) IWorkflowVersionDo {
	return w.withDO(w.DO.Clauses(conds...))
}

func (w workflowVersionDo) Returning(value interface{}, columns ...string) IWorkflowVersionDo {
	return w.withDO(w.DO.Returning(value, columns...))
}

func (w workflowVersionDo) Not(conds ...gen.Condition) IWorkflowVersionDo {
	return w.withDO(w.DO.Not(conds...))
}

func (w workflowVersionDo) Or(conds ...gen.Condition) IWorkflowVersionDo {
	return w.withDO(w.DO.Or(conds...))
}

func (w workflowVersionDo) Select(conds ...field.Expr) IWorkflowVersionDo {
	return w.withDO(w.DO.Select(conds...))
}

func (w workflowVersionDo) Where(conds ...gen.Condition) IWorkflowVersionDo {
	return w.withDO(w.DO.Where(conds...))
}

func (w workflowVersionDo) Order(conds ...field.Expr) IWorkflowVersionDo {
	return w.withDO(w.DO.Order(conds...))
}

func (w workflowVersionDo) Distinct(cols ...field.Expr) IWorkflowVersionDo {
	return w.withDO(w.DO.Distinct(cols...))
}

func (w workflowVersionDo) Omit(cols ...field.Expr) IWorkflowVersionDo {
	return w.withDO(w.DO.Omit(cols...))
}

func (w workflowVersionDo) Join(table schema.Tabler, on ...field.Expr) IWorkflowVersionDo {
	return w.withDO(w.DO.Join(table, on...))
}

func (w workflowVersionDo) LeftJoin(table schema.Tabler, on ...field.Expr) IWorkflowVersionDo {
	return w.withDO(w.DO.LeftJoin(table, on...))
}

func (w workflowVersionDo) RightJoin(table schema.Tabler, on ...field.Expr) IWorkflowVersionDo {
	return w.withDO(w.DO.RightJoin(table, on...))
}

func (w workflowVersionDo) Group(cols ...field.Expr) IWorkflowVersionDo {
	return w.withDO(w.DO.Group(cols...))
}

func (w workflowVersionDo) Having(conds ...gen.Condition) IWorkflowVersionDo {
	return w.withDO(w.DO.Having(conds...))
}

func (w workflowVersionDo) Limit(limit int) IWorkflowVersionDo {
	return w.withDO(w.DO.Limit(limit))
}

func (w workflowVersionDo) Offset(offset int) IWorkflowVersionDo {
	return w.withDO(w.DO.Offset(offset))
}

func (w workflowVersionDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IWorkflowVersionDo {
	return w.withDO(w.DO.Scopes(funcs...))
}

func (w workflowVersionDo) Unscoped() IWorkflowVersionDo {
	return w.withDO(w.DO.Unscoped())
}

func (w workflowVersionDo) Create(values ...*model.WorkflowVersion) error {
	if len(values) == 0 {
		return nil
	}
	return w.DO.Create(values)
}

func (w workflowVersionDo) CreateInBatches(values []*model.WorkflowVersion, batchSize int) error {
	return w.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (w workflowVersionDo) Save(values ...*model.WorkflowVersion) error {
	if len(values) == 0 {
		return nil
	}
	return w.DO.Save(values)
}

func (w workflowVersionDo) First() (*model.WorkflowVersion, error) {
	if result, err := w.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.WorkflowVersion), nil
	}
}

func (w workflowVersionDo) Take() (*model.WorkflowVersion, error) {
	if result, err := w.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.WorkflowVersion), nil
	}
}

func (w workflowVersionDo) Last() (*model.WorkflowVersion, error) {
	if result, err := w.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.WorkflowVersion), nil
	}
}

func (w workflowVersionDo) Find() ([]*model.WorkflowVersion, error) {
	result, err := w.DO.Find()
	return result.([]*model.WorkflowVersion), err
}

func (w workflowVersionDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.WorkflowVersion, err error) {
	buf := make([]*model.WorkflowVersion, 0, batchSize)
	err = w.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (w workflowVersionDo) FindInBatches(result *[]*model.WorkflowVersion, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return w.DO.FindInBatches(result, batchSize, fc)
}

func (w workflowVersionDo) Attrs(attrs ...field.AssignExpr) IWorkflowVersionDo {
	return w.withDO(w.DO.Attrs(attrs...))
}

func (w workflowVersionDo) Assign(attrs ...field.AssignExpr) IWorkflowVersionDo {
	return w.withDO(w.DO.Assign(attrs...))
}

func (w workflowVersionDo) Joins(fields ...field.RelationField) IWorkflowVersionDo {
	for _, _f := range fields {
		w = *w.withDO(w.DO.Joins(_f))
	}
	return &w
}

func (w workflowVersionDo) Preload(fields ...field.RelationField) IWorkflowVersionDo {
	for _, _f := range fields {
		w = *w.withDO(w.DO.Preload(_f))
	}
	return &w
}

func (w workflowVersionDo) FirstOrInit() (*model.WorkflowVersion, error) {
	if result, err := w.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.WorkflowVersion), nil
	}
}

func (w workflowVersionDo) FirstOrCreate() (*model.WorkflowVersion, error) {
	if result, err := w.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.WorkflowVersion), nil
	}
}

func (w workflowVersionDo) FindByPage(offset int, limit int) (result []*model.WorkflowVersion, count int64, err error) {
	result, err = w.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = w.Offset(-1).Limit(-1).Count()
	return
}

func (w workflowVersionDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = w.Count()
	if err != nil {
		return
	}

	err = w.Offset(offset).Limit(limit).Scan(result)
	return
}

func (w workflowVersionDo) Scan(result interface{}) (err error) {
	return w.DO.Scan(result)
}

func (w workflowVersionDo) Delete(models ...*model.WorkflowVersion) (result gen.ResultInfo, err error) {
	return w.DO.Delete(models)
}

func (w *workflowVersionDo) withDO(do gen.Dao) *workflowVersionDo {
	w.DO = *do.(*gen.DO)
	return w
}
