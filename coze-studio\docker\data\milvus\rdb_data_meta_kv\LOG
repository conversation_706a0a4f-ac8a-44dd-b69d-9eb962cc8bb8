2025/08/06-02:18:40.645538 39 RocksDB version: 6.29.5
2025/08/06-02:18:40.648186 39 Git sha 0
2025/08/06-02:18:40.648216 39 Compile date 2024-11-15 11:22:58
2025/08/06-02:18:40.648226 39 DB SUMMARY
2025/08/06-02:18:40.648227 39 DB Session ID:  PR1D1PIPOYDFS5J9C8QG
2025/08/06-02:18:40.655915 39 CURRENT file:  CURRENT
2025/08/06-02:18:40.655935 39 IDENTITY file:  IDENTITY
2025/08/06-02:18:40.657760 39 MANIFEST file:  MANIFEST-000062 size: 312 Bytes
2025/08/06-02:18:40.657790 39 SST files in /var/lib/milvus/rdb_data_meta_kv dir, Total Num: 2, files: 000057.sst 000061.sst 
2025/08/06-02:18:40.657800 39 Write Ahead Log file in /var/lib/milvus/rdb_data_meta_kv: 000063.log size: 6571968 ; 
2025/08/06-02:18:40.657807 39                         Options.error_if_exists: 0
2025/08/06-02:18:40.657809 39                       Options.create_if_missing: 1
2025/08/06-02:18:40.657810 39                         Options.paranoid_checks: 1
2025/08/06-02:18:40.657811 39             Options.flush_verify_memtable_count: 1
2025/08/06-02:18:40.657812 39                               Options.track_and_verify_wals_in_manifest: 0
2025/08/06-02:18:40.657812 39                                     Options.env: 0x7fae250bad00
2025/08/06-02:18:40.657814 39                                      Options.fs: PosixFileSystem
2025/08/06-02:18:40.657816 39                                Options.info_log: 0x7fad48a90050
2025/08/06-02:18:40.657817 39                Options.max_file_opening_threads: 16
2025/08/06-02:18:40.657818 39                              Options.statistics: (nil)
2025/08/06-02:18:40.657820 39                               Options.use_fsync: 0
2025/08/06-02:18:40.657821 39                       Options.max_log_file_size: 0
2025/08/06-02:18:40.657822 39                  Options.max_manifest_file_size: 1073741824
2025/08/06-02:18:40.657822 39                   Options.log_file_time_to_roll: 0
2025/08/06-02:18:40.657823 39                       Options.keep_log_file_num: 1000
2025/08/06-02:18:40.657824 39                    Options.recycle_log_file_num: 0
2025/08/06-02:18:40.657825 39                         Options.allow_fallocate: 1
2025/08/06-02:18:40.657826 39                        Options.allow_mmap_reads: 0
2025/08/06-02:18:40.657827 39                       Options.allow_mmap_writes: 0
2025/08/06-02:18:40.657828 39                        Options.use_direct_reads: 0
2025/08/06-02:18:40.657829 39                        Options.use_direct_io_for_flush_and_compaction: 0
2025/08/06-02:18:40.657830 39          Options.create_missing_column_families: 0
2025/08/06-02:18:40.657831 39                              Options.db_log_dir: 
2025/08/06-02:18:40.657832 39                                 Options.wal_dir: 
2025/08/06-02:18:40.657833 39                Options.table_cache_numshardbits: 6
2025/08/06-02:18:40.657834 39                         Options.WAL_ttl_seconds: 0
2025/08/06-02:18:40.657835 39                       Options.WAL_size_limit_MB: 0
2025/08/06-02:18:40.657836 39                        Options.max_write_batch_group_size_bytes: 1048576
2025/08/06-02:18:40.657837 39             Options.manifest_preallocation_size: 4194304
2025/08/06-02:18:40.657838 39                     Options.is_fd_close_on_exec: 1
2025/08/06-02:18:40.657838 39                   Options.advise_random_on_open: 1
2025/08/06-02:18:40.657839 39                   Options.experimental_mempurge_threshold: 0.000000
2025/08/06-02:18:40.658400 39                    Options.db_write_buffer_size: 0
2025/08/06-02:18:40.658405 39                    Options.write_buffer_manager: 0x7fad4b4500a0
2025/08/06-02:18:40.658407 39         Options.access_hint_on_compaction_start: 1
2025/08/06-02:18:40.658408 39  Options.new_table_reader_for_compaction_inputs: 0
2025/08/06-02:18:40.658408 39           Options.random_access_max_buffer_size: 1048576
2025/08/06-02:18:40.658409 39                      Options.use_adaptive_mutex: 0
2025/08/06-02:18:40.658410 39                            Options.rate_limiter: (nil)
2025/08/06-02:18:40.658431 39     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/08/06-02:18:40.658433 39                       Options.wal_recovery_mode: 2
2025/08/06-02:18:40.658979 39                  Options.enable_thread_tracking: 0
2025/08/06-02:18:40.658987 39                  Options.enable_pipelined_write: 0
2025/08/06-02:18:40.658988 39                  Options.unordered_write: 0
2025/08/06-02:18:40.658990 39         Options.allow_concurrent_memtable_write: 1
2025/08/06-02:18:40.658991 39      Options.enable_write_thread_adaptive_yield: 1
2025/08/06-02:18:40.658992 39             Options.write_thread_max_yield_usec: 100
2025/08/06-02:18:40.658993 39            Options.write_thread_slow_yield_usec: 3
2025/08/06-02:18:40.658994 39                               Options.row_cache: None
2025/08/06-02:18:40.658995 39                              Options.wal_filter: None
2025/08/06-02:18:40.658996 39             Options.avoid_flush_during_recovery: 0
2025/08/06-02:18:40.658997 39             Options.allow_ingest_behind: 0
2025/08/06-02:18:40.658998 39             Options.preserve_deletes: 0
2025/08/06-02:18:40.658999 39             Options.two_write_queues: 0
2025/08/06-02:18:40.659000 39             Options.manual_wal_flush: 0
2025/08/06-02:18:40.659001 39             Options.atomic_flush: 0
2025/08/06-02:18:40.659002 39             Options.avoid_unnecessary_blocking_io: 0
2025/08/06-02:18:40.659003 39                 Options.persist_stats_to_disk: 0
2025/08/06-02:18:40.659004 39                 Options.write_dbid_to_manifest: 0
2025/08/06-02:18:40.659005 39                 Options.log_readahead_size: 0
2025/08/06-02:18:40.659006 39                 Options.file_checksum_gen_factory: Unknown
2025/08/06-02:18:40.659007 39                 Options.best_efforts_recovery: 0
2025/08/06-02:18:40.659008 39                Options.max_bgerror_resume_count: 2147483647
2025/08/06-02:18:40.659009 39            Options.bgerror_resume_retry_interval: 1000000
2025/08/06-02:18:40.659010 39             Options.allow_data_in_errors: 0
2025/08/06-02:18:40.659011 39             Options.db_host_id: __hostname__
2025/08/06-02:18:40.659019 39             Options.max_background_jobs: 2
2025/08/06-02:18:40.659020 39             Options.max_background_compactions: -1
2025/08/06-02:18:40.659021 39             Options.max_subcompactions: 1
2025/08/06-02:18:40.659022 39             Options.avoid_flush_during_shutdown: 0
2025/08/06-02:18:40.659023 39           Options.writable_file_max_buffer_size: 1048576
2025/08/06-02:18:40.659024 39             Options.delayed_write_rate : 16777216
2025/08/06-02:18:40.659025 39             Options.max_total_wal_size: 0
2025/08/06-02:18:40.659026 39             Options.delete_obsolete_files_period_micros: 21600000000
2025/08/06-02:18:40.659027 39                   Options.stats_dump_period_sec: 600
2025/08/06-02:18:40.659745 39                 Options.stats_persist_period_sec: 600
2025/08/06-02:18:40.659748 39                 Options.stats_history_buffer_size: 1048576
2025/08/06-02:18:40.659749 39                          Options.max_open_files: -1
2025/08/06-02:18:40.659750 39                          Options.bytes_per_sync: 0
2025/08/06-02:18:40.659751 39                      Options.wal_bytes_per_sync: 0
2025/08/06-02:18:40.659751 39                   Options.strict_bytes_per_sync: 0
2025/08/06-02:18:40.659753 39       Options.compaction_readahead_size: 0
2025/08/06-02:18:40.659754 39                  Options.max_background_flushes: 1
2025/08/06-02:18:40.659755 39 Compression algorithms supported:
2025/08/06-02:18:40.659758 39 	kZSTD supported: 1
2025/08/06-02:18:40.659760 39 	kXpressCompression supported: 0
2025/08/06-02:18:40.659762 39 	kBZip2Compression supported: 0
2025/08/06-02:18:40.659763 39 	kZSTDNotFinalCompression supported: 1
2025/08/06-02:18:40.659764 39 	kLZ4Compression supported: 0
2025/08/06-02:18:40.659765 39 	kZlibCompression supported: 0
2025/08/06-02:18:40.659766 39 	kLZ4HCCompression supported: 0
2025/08/06-02:18:40.659772 39 	kSnappyCompression supported: 0
2025/08/06-02:18:40.659784 39 Fast CRC32 supported: Not supported on x86
2025/08/06-02:18:40.681878 39 [db/version_set.cc:4888] Recovering from manifest file: /var/lib/milvus/rdb_data_meta_kv/MANIFEST-000062
2025/08/06-02:18:40.690276 39 [db/column_family.cc:605] --------------- Options for column family [default]:
2025/08/06-02:18:40.690298 39               Options.comparator: leveldb.BytewiseComparator
2025/08/06-02:18:40.690300 39           Options.merge_operator: None
2025/08/06-02:18:40.690302 39        Options.compaction_filter: None
2025/08/06-02:18:40.690303 39        Options.compaction_filter_factory: None
2025/08/06-02:18:40.690304 39  Options.sst_partitioner_factory: None
2025/08/06-02:18:40.690305 39         Options.memtable_factory: SkipListFactory
2025/08/06-02:18:40.690306 39            Options.table_factory: BlockBasedTable
2025/08/06-02:18:40.690371 39            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7fad4b400100)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0x7fad4b450010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 1001872834
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: (nil)
  persistent_cache: (nil)
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2025/08/06-02:18:40.690377 39        Options.write_buffer_size: 67108864
2025/08/06-02:18:40.690378 39  Options.max_write_buffer_number: 2
2025/08/06-02:18:40.690383 39        Options.compression[0]: NoCompression
2025/08/06-02:18:40.690385 39        Options.compression[1]: NoCompression
2025/08/06-02:18:40.690386 39        Options.compression[2]: ZSTD
2025/08/06-02:18:40.690387 39        Options.compression[3]: ZSTD
2025/08/06-02:18:40.690388 39        Options.compression[4]: ZSTD
2025/08/06-02:18:40.690389 39                  Options.bottommost_compression: Disabled
2025/08/06-02:18:40.690390 39       Options.prefix_extractor: nullptr
2025/08/06-02:18:40.690391 39   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/06-02:18:40.690392 39             Options.num_levels: 5
2025/08/06-02:18:40.690393 39        Options.min_write_buffer_number_to_merge: 1
2025/08/06-02:18:40.690394 39     Options.max_write_buffer_number_to_maintain: 0
2025/08/06-02:18:40.690395 39     Options.max_write_buffer_size_to_maintain: 0
2025/08/06-02:18:40.690396 39            Options.bottommost_compression_opts.window_bits: -14
2025/08/06-02:18:40.690397 39                  Options.bottommost_compression_opts.level: 32767
2025/08/06-02:18:40.690398 39               Options.bottommost_compression_opts.strategy: 0
2025/08/06-02:18:40.690399 39         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/06-02:18:40.690400 39         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/06-02:18:40.690402 39         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/06-02:18:40.690403 39                  Options.bottommost_compression_opts.enabled: false
2025/08/06-02:18:40.690404 39         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/06-02:18:40.690405 39            Options.compression_opts.window_bits: -14
2025/08/06-02:18:40.690406 39                  Options.compression_opts.level: 32767
2025/08/06-02:18:40.690407 39               Options.compression_opts.strategy: 0
2025/08/06-02:18:40.690408 39         Options.compression_opts.max_dict_bytes: 0
2025/08/06-02:18:40.690409 39         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/06-02:18:40.690804 39         Options.compression_opts.parallel_threads: 1
2025/08/06-02:18:40.690813 39                  Options.compression_opts.enabled: false
2025/08/06-02:18:40.690815 39         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/06-02:18:40.690816 39      Options.level0_file_num_compaction_trigger: 4
2025/08/06-02:18:40.690817 39          Options.level0_slowdown_writes_trigger: 20
2025/08/06-02:18:40.690818 39              Options.level0_stop_writes_trigger: 36
2025/08/06-02:18:40.690819 39                   Options.target_file_size_base: 67108864
2025/08/06-02:18:40.690820 39             Options.target_file_size_multiplier: 2
2025/08/06-02:18:40.690821 39                Options.max_bytes_for_level_base: 268435456
2025/08/06-02:18:40.690822 39 Options.level_compaction_dynamic_level_bytes: 0
2025/08/06-02:18:40.690823 39          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/06-02:18:40.690829 39 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/06-02:18:40.690830 39 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/06-02:18:40.690831 39 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/06-02:18:40.690832 39 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/06-02:18:40.690833 39 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/06-02:18:40.690834 39 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/06-02:18:40.690835 39 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/06-02:18:40.690836 39       Options.max_sequential_skip_in_iterations: 8
2025/08/06-02:18:40.690837 39                    Options.max_compaction_bytes: 1677721600
2025/08/06-02:18:40.690838 39                        Options.arena_block_size: 1048576
2025/08/06-02:18:40.690840 39   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/06-02:18:40.690841 39   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/06-02:18:40.690842 39       Options.rate_limit_delay_max_milliseconds: 100
2025/08/06-02:18:40.690843 39                Options.disable_auto_compactions: 0
2025/08/06-02:18:40.690849 39                        Options.compaction_style: kCompactionStyleLevel
2025/08/06-02:18:40.690851 39                          Options.compaction_pri: kMinOverlappingRatio
2025/08/06-02:18:40.690852 39 Options.compaction_options_universal.size_ratio: 1
2025/08/06-02:18:40.690853 39 Options.compaction_options_universal.min_merge_width: 2
2025/08/06-02:18:40.690854 39 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/06-02:18:40.690855 39 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/06-02:18:40.690856 39 Options.compaction_options_universal.compression_size_percent: -1
2025/08/06-02:18:40.690858 39 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/06-02:18:40.690859 39 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/06-02:18:40.690860 39 Options.compaction_options_fifo.allow_compaction: 0
2025/08/06-02:18:40.690869 39                   Options.table_properties_collectors: 
2025/08/06-02:18:40.690871 39                   Options.inplace_update_support: 0
2025/08/06-02:18:40.690872 39                 Options.inplace_update_num_locks: 10000
2025/08/06-02:18:40.690873 39               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/06-02:18:40.690875 39               Options.memtable_whole_key_filtering: 0
2025/08/06-02:18:40.690876 39   Options.memtable_huge_page_size: 0
2025/08/06-02:18:40.690877 39                           Options.bloom_locality: 0
2025/08/06-02:18:40.690878 39                    Options.max_successive_merges: 0
2025/08/06-02:18:40.690879 39                Options.optimize_filters_for_hits: 0
2025/08/06-02:18:40.690880 39                Options.paranoid_file_checks: 0
2025/08/06-02:18:40.690880 39                Options.force_consistency_checks: 1
2025/08/06-02:18:40.690881 39                Options.report_bg_io_stats: 0
2025/08/06-02:18:40.690884 39                               Options.ttl: 2592000
2025/08/06-02:18:40.691699 39          Options.periodic_compaction_seconds: 0
2025/08/06-02:18:40.691715 39                       Options.enable_blob_files: false
2025/08/06-02:18:40.691718 39                           Options.min_blob_size: 0
2025/08/06-02:18:40.691719 39                          Options.blob_file_size: 268435456
2025/08/06-02:18:40.691722 39                   Options.blob_compression_type: NoCompression
2025/08/06-02:18:40.691723 39          Options.enable_blob_garbage_collection: false
2025/08/06-02:18:40.691724 39      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/06-02:18:40.691733 39 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/06-02:18:40.691734 39          Options.blob_compaction_readahead_size: 0
2025/08/06-02:18:40.707738 39 [db/version_set.cc:4928] Recovered from manifest file:/var/lib/milvus/rdb_data_meta_kv/MANIFEST-000062 succeeded,manifest_file_number is 62, next_file_number is 64, last_sequence is 79163, log_number is 55,prev_log_number is 0,max_column_family is 0,min_log_number_to_keep is 0
2025/08/06-02:18:40.707757 39 [db/version_set.cc:4943] Column family [default] (ID 0), log number is 55
2025/08/06-02:18:40.716143 39 [db/version_set.cc:4409] Creating manifest 66
2025/08/06-02:18:40.741583 39 EVENT_LOG_v1 {"time_micros": 1754446720741548, "job": 1, "event": "recovery_started", "wal_files": [63]}
2025/08/06-02:18:40.741603 39 [db/db_impl/db_impl_open.cc:888] Recovering log #63 mode 2
2025/08/06-02:18:40.993104 39 EVENT_LOG_v1 {"time_micros": 1754446720993038, "cf_name": "default", "job": 1, "event": "table_file_creation", "file_number": 67, "file_size": 1032, "file_checksum": "", "file_checksum_func_name": "Unknown", "table_properties": {"data_size": 66, "index_size": 52, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 43, "raw_average_key_size": 43, "raw_value_size": 7, "raw_average_value_size": 7, "num_data_blocks": 1, "num_entries": 1, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; ", "creation_time": 1754446720, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "5d84d4a4-9f27-46dd-b1e4-572f86de492f", "db_session_id": "PR1D1PIPOYDFS5J9C8QG", "orig_file_number": 67}}
2025/08/06-02:18:40.993504 39 [db/version_set.cc:4409] Creating manifest 68
2025/08/06-02:18:41.026015 39 EVENT_LOG_v1 {"time_micros": 1754446721025999, "job": 1, "event": "recovery_finished"}
2025/08/06-02:18:41.058289 39 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data_meta_kv/000063.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/08/06-02:18:41.059184 39 [db/db_impl/db_impl_open.cc:1820] SstFileManager instance 0x7fad48680000
2025/08/06-02:18:41.062500 39 DB pointer 0x7fad48a20000
2025/08/06-02:18:41.063445 66 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/06-02:18:41.063470 66 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 0.4 total, 0.4 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0    2.06 KB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.1      0.02              0.00         1    0.015       0      0       0.0       0.0
  L1      1/0    1.72 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      3/0    3.78 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.1      0.02              0.00         1    0.015       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.1      0.02              0.00         1    0.015       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.1      0.02              0.00         1    0.015       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 0.4 total, 0.4 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fad4b450010#9 capacity: 955.46 MB collections: 1 last_copies: 0 last_secs: 9.5e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
