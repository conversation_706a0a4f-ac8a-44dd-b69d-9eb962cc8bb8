// Code generated by thriftgo (0.4.1). DO NOT EDIT.

package playground

import (
	"database/sql"
	"database/sql/driver"
	"fmt"
	"github.com/apache/thrift/lib/go/thrift"
	"github.com/coze-dev/coze-studio/backend/api/model/base"
)

type SendType int64

const (
	// Send query directly
	SendType_SendTypeQuery SendType = 0
	// use panel
	SendType_SendTypePanel SendType = 1
)

func (p SendType) String() string {
	switch p {
	case SendType_SendTypeQuery:
		return "SendTypeQuery"
	case SendType_SendTypePanel:
		return "SendTypePanel"
	}
	return "<UNSET>"
}

func SendTypeFromString(s string) (SendType, error) {
	switch s {
	case "SendTypeQuery":
		return SendType_SendTypeQuery, nil
	case "SendTypePanel":
		return SendType_SendTypePanel, nil
	}
	return SendType(0), fmt.Errorf("not a valid SendType string")
}

func SendTypePtr(v SendType) *SendType { return &v }
func (p *SendType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = SendType(result.Int64)
	return
}

func (p *SendType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type ToolType int64

const (
	// Using WorkFlow
	ToolType_ToolTypeWorkFlow ToolType = 1
	// use plug-ins
	ToolType_ToolTypePlugin ToolType = 2
)

func (p ToolType) String() string {
	switch p {
	case ToolType_ToolTypeWorkFlow:
		return "ToolTypeWorkFlow"
	case ToolType_ToolTypePlugin:
		return "ToolTypePlugin"
	}
	return "<UNSET>"
}

func ToolTypeFromString(s string) (ToolType, error) {
	switch s {
	case "ToolTypeWorkFlow":
		return ToolType_ToolTypeWorkFlow, nil
	case "ToolTypePlugin":
		return ToolType_ToolTypePlugin, nil
	}
	return ToolType(0), fmt.Errorf("not a valid ToolType string")
}

func ToolTypePtr(v ToolType) *ToolType { return &v }
func (p *ToolType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = ToolType(result.Int64)
	return
}

func (p *ToolType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type InputType int64

const (
	InputType_TextInput   InputType = 0
	InputType_Select      InputType = 1
	InputType_UploadImage InputType = 2
	InputType_UploadDoc   InputType = 3
	InputType_UploadTable InputType = 4
	InputType_UploadAudio InputType = 5
	InputType_MixUpload   InputType = 6
	InputType_VIDEO       InputType = 7
	InputType_ARCHIVE     InputType = 8
	InputType_CODE        InputType = 9
	InputType_TXT         InputType = 10
	InputType_PPT         InputType = 11
)

func (p InputType) String() string {
	switch p {
	case InputType_TextInput:
		return "TextInput"
	case InputType_Select:
		return "Select"
	case InputType_UploadImage:
		return "UploadImage"
	case InputType_UploadDoc:
		return "UploadDoc"
	case InputType_UploadTable:
		return "UploadTable"
	case InputType_UploadAudio:
		return "UploadAudio"
	case InputType_MixUpload:
		return "MixUpload"
	case InputType_VIDEO:
		return "VIDEO"
	case InputType_ARCHIVE:
		return "ARCHIVE"
	case InputType_CODE:
		return "CODE"
	case InputType_TXT:
		return "TXT"
	case InputType_PPT:
		return "PPT"
	}
	return "<UNSET>"
}

func InputTypeFromString(s string) (InputType, error) {
	switch s {
	case "TextInput":
		return InputType_TextInput, nil
	case "Select":
		return InputType_Select, nil
	case "UploadImage":
		return InputType_UploadImage, nil
	case "UploadDoc":
		return InputType_UploadDoc, nil
	case "UploadTable":
		return InputType_UploadTable, nil
	case "UploadAudio":
		return InputType_UploadAudio, nil
	case "MixUpload":
		return InputType_MixUpload, nil
	case "VIDEO":
		return InputType_VIDEO, nil
	case "ARCHIVE":
		return InputType_ARCHIVE, nil
	case "CODE":
		return InputType_CODE, nil
	case "TXT":
		return InputType_TXT, nil
	case "PPT":
		return InputType_PPT, nil
	}
	return InputType(0), fmt.Errorf("not a valid InputType string")
}

func InputTypePtr(v InputType) *InputType { return &v }
func (p *InputType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = InputType(result.Int64)
	return
}

func (p *InputType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type CreateShortcutCommandRequest struct {
	ObjectID  string           `thrift:"object_id,1" form:"object_id" json:"object_id" query:"object_id"`
	Shortcuts *ShortcutCommand `thrift:"shortcuts,2" form:"shortcuts" json:"shortcuts" query:"shortcuts"`
	Base      *base.Base       `thrift:"Base,255" form:"Base" json:"Base" query:"Base"`
}

func NewCreateShortcutCommandRequest() *CreateShortcutCommandRequest {
	return &CreateShortcutCommandRequest{}
}

func (p *CreateShortcutCommandRequest) InitDefault() {
}

func (p *CreateShortcutCommandRequest) GetObjectID() (v string) {
	return p.ObjectID
}

var CreateShortcutCommandRequest_Shortcuts_DEFAULT *ShortcutCommand

func (p *CreateShortcutCommandRequest) GetShortcuts() (v *ShortcutCommand) {
	if !p.IsSetShortcuts() {
		return CreateShortcutCommandRequest_Shortcuts_DEFAULT
	}
	return p.Shortcuts
}

var CreateShortcutCommandRequest_Base_DEFAULT *base.Base

func (p *CreateShortcutCommandRequest) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return CreateShortcutCommandRequest_Base_DEFAULT
	}
	return p.Base
}

var fieldIDToName_CreateShortcutCommandRequest = map[int16]string{
	1:   "object_id",
	2:   "shortcuts",
	255: "Base",
}

func (p *CreateShortcutCommandRequest) IsSetShortcuts() bool {
	return p.Shortcuts != nil
}

func (p *CreateShortcutCommandRequest) IsSetBase() bool {
	return p.Base != nil
}

func (p *CreateShortcutCommandRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateShortcutCommandRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *CreateShortcutCommandRequest) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ObjectID = _field
	return nil
}
func (p *CreateShortcutCommandRequest) ReadField2(iprot thrift.TProtocol) error {
	_field := NewShortcutCommand()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Shortcuts = _field
	return nil
}
func (p *CreateShortcutCommandRequest) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *CreateShortcutCommandRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("CreateShortcutCommandRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateShortcutCommandRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("object_id", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ObjectID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *CreateShortcutCommandRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("shortcuts", thrift.STRUCT, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Shortcuts.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *CreateShortcutCommandRequest) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Base.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *CreateShortcutCommandRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateShortcutCommandRequest(%+v)", *p)

}

type CreateShortcutCommandResponse struct {
	Shortcuts *ShortcutCommand `thrift:"shortcuts,1" form:"shortcuts" json:"shortcuts" query:"shortcuts"`
	BaseResp  *base.BaseResp   `thrift:"BaseResp,255,required" form:"BaseResp,required" json:"BaseResp,required" query:"BaseResp,required"`
}

func NewCreateShortcutCommandResponse() *CreateShortcutCommandResponse {
	return &CreateShortcutCommandResponse{}
}

func (p *CreateShortcutCommandResponse) InitDefault() {
}

var CreateShortcutCommandResponse_Shortcuts_DEFAULT *ShortcutCommand

func (p *CreateShortcutCommandResponse) GetShortcuts() (v *ShortcutCommand) {
	if !p.IsSetShortcuts() {
		return CreateShortcutCommandResponse_Shortcuts_DEFAULT
	}
	return p.Shortcuts
}

var CreateShortcutCommandResponse_BaseResp_DEFAULT *base.BaseResp

func (p *CreateShortcutCommandResponse) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return CreateShortcutCommandResponse_BaseResp_DEFAULT
	}
	return p.BaseResp
}

var fieldIDToName_CreateShortcutCommandResponse = map[int16]string{
	1:   "shortcuts",
	255: "BaseResp",
}

func (p *CreateShortcutCommandResponse) IsSetShortcuts() bool {
	return p.Shortcuts != nil
}

func (p *CreateShortcutCommandResponse) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *CreateShortcutCommandResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetBaseResp bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
				issetBaseResp = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetBaseResp {
		fieldId = 255
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateShortcutCommandResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateShortcutCommandResponse[fieldId]))
}

func (p *CreateShortcutCommandResponse) ReadField1(iprot thrift.TProtocol) error {
	_field := NewShortcutCommand()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Shortcuts = _field
	return nil
}
func (p *CreateShortcutCommandResponse) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *CreateShortcutCommandResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("CreateShortcutCommandResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateShortcutCommandResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("shortcuts", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Shortcuts.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *CreateShortcutCommandResponse) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.BaseResp.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *CreateShortcutCommandResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateShortcutCommandResponse(%+v)", *p)

}

type ShortcutStruct struct {
	// Shortcut ID list, bound on the entity
	ShortcutSort []string `thrift:"shortcut_sort,16,optional" form:"shortcut_sort" json:"shortcut_sort,omitempty" query:"shortcut_sort"`
	// Quick command content list
	ShortcutList []*ShortcutCommand `thrift:"shortcut_list,17,optional" form:"shortcut_list" json:"shortcut_list,omitempty" query:"shortcut_list"`
}

func NewShortcutStruct() *ShortcutStruct {
	return &ShortcutStruct{}
}

func (p *ShortcutStruct) InitDefault() {
}

var ShortcutStruct_ShortcutSort_DEFAULT []string

func (p *ShortcutStruct) GetShortcutSort() (v []string) {
	if !p.IsSetShortcutSort() {
		return ShortcutStruct_ShortcutSort_DEFAULT
	}
	return p.ShortcutSort
}

var ShortcutStruct_ShortcutList_DEFAULT []*ShortcutCommand

func (p *ShortcutStruct) GetShortcutList() (v []*ShortcutCommand) {
	if !p.IsSetShortcutList() {
		return ShortcutStruct_ShortcutList_DEFAULT
	}
	return p.ShortcutList
}

var fieldIDToName_ShortcutStruct = map[int16]string{
	16: "shortcut_sort",
	17: "shortcut_list",
}

func (p *ShortcutStruct) IsSetShortcutSort() bool {
	return p.ShortcutSort != nil
}

func (p *ShortcutStruct) IsSetShortcutList() bool {
	return p.ShortcutList != nil
}

func (p *ShortcutStruct) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 16:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField16(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 17:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField17(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ShortcutStruct[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ShortcutStruct) ReadField16(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ShortcutSort = _field
	return nil
}
func (p *ShortcutStruct) ReadField17(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*ShortcutCommand, 0, size)
	values := make([]ShortcutCommand, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ShortcutList = _field
	return nil
}

func (p *ShortcutStruct) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ShortcutStruct"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField16(oprot); err != nil {
			fieldId = 16
			goto WriteFieldError
		}
		if err = p.writeField17(oprot); err != nil {
			fieldId = 17
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ShortcutStruct) writeField16(oprot thrift.TProtocol) (err error) {
	if p.IsSetShortcutSort() {
		if err = oprot.WriteFieldBegin("shortcut_sort", thrift.LIST, 16); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.ShortcutSort)); err != nil {
			return err
		}
		for _, v := range p.ShortcutSort {
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 end error: ", p), err)
}
func (p *ShortcutStruct) writeField17(oprot thrift.TProtocol) (err error) {
	if p.IsSetShortcutList() {
		if err = oprot.WriteFieldBegin("shortcut_list", thrift.LIST, 17); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ShortcutList)); err != nil {
			return err
		}
		for _, v := range p.ShortcutList {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 end error: ", p), err)
}

func (p *ShortcutStruct) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ShortcutStruct(%+v)", *p)

}

type ShortcutCommand struct {
	// Binding Entity ID
	ObjectID int64 `thrift:"object_id,2" form:"object_id" json:"object_id,string" query:"object_id"`
	// command name
	CommandName string `thrift:"command_name,3" form:"command_name" json:"command_name" query:"command_name"`
	// Quick Instruction
	ShortcutCommand string `thrift:"shortcut_command,4" form:"shortcut_command" json:"shortcut_command" query:"shortcut_command"`
	// describe
	Description string `thrift:"description,5" form:"description" json:"description" query:"description"`
	// Send type
	SendType SendType `thrift:"send_type,6" form:"send_type" json:"send_type" query:"send_type"`
	// Use tool type
	ToolType      ToolType `thrift:"tool_type,7" form:"tool_type" json:"tool_type" query:"tool_type"`
	WorkFlowID    string   `thrift:"work_flow_id,8" form:"work_flow_id" json:"work_flow_id" query:"work_flow_id"`
	PluginID      string   `thrift:"plugin_id,9" form:"plugin_id" json:"plugin_id" query:"plugin_id"`
	PluginAPIName string   `thrift:"plugin_api_name,10" form:"plugin_api_name" json:"plugin_api_name" query:"plugin_api_name"`
	// Template query
	TemplateQuery string `thrift:"template_query,11" form:"template_query" json:"template_query" query:"template_query"`
	// Panel parameters
	ComponentsList []*Components `thrift:"components_list,12" form:"components_list" json:"components_list" query:"components_list"`
	// Form schema
	CardSchema string `thrift:"card_schema,15" form:"card_schema" json:"card_schema" query:"card_schema"`
	// Instruction ID
	CommandID int64 `thrift:"command_id,16" form:"command_id" json:"command_id,string" query:"command_id"`
	//Tool information, including name + variable list +...
	ToolInfo *ToolInfo `thrift:"tool_info,17" form:"tool_info" json:"tool_info" query:"tool_info"`
	// command icon
	ShortcutIcon *ShortcutFileInfo `thrift:"shortcut_icon,18" form:"shortcut_icon" json:"shortcut_icon" query:"shortcut_icon"`
	//Multi instruction, which node executes the instruction
	AgentID     *string `thrift:"agent_id,21,optional" form:"agent_id" json:"agent_id,omitempty" query:"agent_id"`
	PluginAPIID int64   `thrift:"plugin_api_id,22" form:"plugin_api_id" json:"plugin_api_id,string" query:"plugin_api_id"`
}

func NewShortcutCommand() *ShortcutCommand {
	return &ShortcutCommand{}
}

func (p *ShortcutCommand) InitDefault() {
}

func (p *ShortcutCommand) GetObjectID() (v int64) {
	return p.ObjectID
}

func (p *ShortcutCommand) GetCommandName() (v string) {
	return p.CommandName
}

func (p *ShortcutCommand) GetShortcutCommand() (v string) {
	return p.ShortcutCommand
}

func (p *ShortcutCommand) GetDescription() (v string) {
	return p.Description
}

func (p *ShortcutCommand) GetSendType() (v SendType) {
	return p.SendType
}

func (p *ShortcutCommand) GetToolType() (v ToolType) {
	return p.ToolType
}

func (p *ShortcutCommand) GetWorkFlowID() (v string) {
	return p.WorkFlowID
}

func (p *ShortcutCommand) GetPluginID() (v string) {
	return p.PluginID
}

func (p *ShortcutCommand) GetPluginAPIName() (v string) {
	return p.PluginAPIName
}

func (p *ShortcutCommand) GetTemplateQuery() (v string) {
	return p.TemplateQuery
}

func (p *ShortcutCommand) GetComponentsList() (v []*Components) {
	return p.ComponentsList
}

func (p *ShortcutCommand) GetCardSchema() (v string) {
	return p.CardSchema
}

func (p *ShortcutCommand) GetCommandID() (v int64) {
	return p.CommandID
}

var ShortcutCommand_ToolInfo_DEFAULT *ToolInfo

func (p *ShortcutCommand) GetToolInfo() (v *ToolInfo) {
	if !p.IsSetToolInfo() {
		return ShortcutCommand_ToolInfo_DEFAULT
	}
	return p.ToolInfo
}

var ShortcutCommand_ShortcutIcon_DEFAULT *ShortcutFileInfo

func (p *ShortcutCommand) GetShortcutIcon() (v *ShortcutFileInfo) {
	if !p.IsSetShortcutIcon() {
		return ShortcutCommand_ShortcutIcon_DEFAULT
	}
	return p.ShortcutIcon
}

var ShortcutCommand_AgentID_DEFAULT string

func (p *ShortcutCommand) GetAgentID() (v string) {
	if !p.IsSetAgentID() {
		return ShortcutCommand_AgentID_DEFAULT
	}
	return *p.AgentID
}

func (p *ShortcutCommand) GetPluginAPIID() (v int64) {
	return p.PluginAPIID
}

var fieldIDToName_ShortcutCommand = map[int16]string{
	2:  "object_id",
	3:  "command_name",
	4:  "shortcut_command",
	5:  "description",
	6:  "send_type",
	7:  "tool_type",
	8:  "work_flow_id",
	9:  "plugin_id",
	10: "plugin_api_name",
	11: "template_query",
	12: "components_list",
	15: "card_schema",
	16: "command_id",
	17: "tool_info",
	18: "shortcut_icon",
	21: "agent_id",
	22: "plugin_api_id",
}

func (p *ShortcutCommand) IsSetToolInfo() bool {
	return p.ToolInfo != nil
}

func (p *ShortcutCommand) IsSetShortcutIcon() bool {
	return p.ShortcutIcon != nil
}

func (p *ShortcutCommand) IsSetAgentID() bool {
	return p.AgentID != nil
}

func (p *ShortcutCommand) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField15(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 16:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField16(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 17:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField17(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 18:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField18(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 21:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField21(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 22:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField22(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ShortcutCommand[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ShortcutCommand) ReadField2(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ObjectID = _field
	return nil
}
func (p *ShortcutCommand) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CommandName = _field
	return nil
}
func (p *ShortcutCommand) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ShortcutCommand = _field
	return nil
}
func (p *ShortcutCommand) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Description = _field
	return nil
}
func (p *ShortcutCommand) ReadField6(iprot thrift.TProtocol) error {

	var _field SendType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = SendType(v)
	}
	p.SendType = _field
	return nil
}
func (p *ShortcutCommand) ReadField7(iprot thrift.TProtocol) error {

	var _field ToolType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = ToolType(v)
	}
	p.ToolType = _field
	return nil
}
func (p *ShortcutCommand) ReadField8(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.WorkFlowID = _field
	return nil
}
func (p *ShortcutCommand) ReadField9(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.PluginID = _field
	return nil
}
func (p *ShortcutCommand) ReadField10(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.PluginAPIName = _field
	return nil
}
func (p *ShortcutCommand) ReadField11(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TemplateQuery = _field
	return nil
}
func (p *ShortcutCommand) ReadField12(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*Components, 0, size)
	values := make([]Components, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ComponentsList = _field
	return nil
}
func (p *ShortcutCommand) ReadField15(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CardSchema = _field
	return nil
}
func (p *ShortcutCommand) ReadField16(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CommandID = _field
	return nil
}
func (p *ShortcutCommand) ReadField17(iprot thrift.TProtocol) error {
	_field := NewToolInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.ToolInfo = _field
	return nil
}
func (p *ShortcutCommand) ReadField18(iprot thrift.TProtocol) error {
	_field := NewShortcutFileInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.ShortcutIcon = _field
	return nil
}
func (p *ShortcutCommand) ReadField21(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.AgentID = _field
	return nil
}
func (p *ShortcutCommand) ReadField22(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.PluginAPIID = _field
	return nil
}

func (p *ShortcutCommand) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ShortcutCommand"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField15(oprot); err != nil {
			fieldId = 15
			goto WriteFieldError
		}
		if err = p.writeField16(oprot); err != nil {
			fieldId = 16
			goto WriteFieldError
		}
		if err = p.writeField17(oprot); err != nil {
			fieldId = 17
			goto WriteFieldError
		}
		if err = p.writeField18(oprot); err != nil {
			fieldId = 18
			goto WriteFieldError
		}
		if err = p.writeField21(oprot); err != nil {
			fieldId = 21
			goto WriteFieldError
		}
		if err = p.writeField22(oprot); err != nil {
			fieldId = 22
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ShortcutCommand) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("object_id", thrift.I64, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.ObjectID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ShortcutCommand) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("command_name", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CommandName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *ShortcutCommand) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("shortcut_command", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ShortcutCommand); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *ShortcutCommand) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("description", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Description); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *ShortcutCommand) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("send_type", thrift.I32, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.SendType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *ShortcutCommand) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("tool_type", thrift.I32, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.ToolType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}
func (p *ShortcutCommand) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("work_flow_id", thrift.STRING, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.WorkFlowID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}
func (p *ShortcutCommand) writeField9(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("plugin_id", thrift.STRING, 9); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.PluginID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}
func (p *ShortcutCommand) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("plugin_api_name", thrift.STRING, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.PluginAPIName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}
func (p *ShortcutCommand) writeField11(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("template_query", thrift.STRING, 11); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TemplateQuery); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}
func (p *ShortcutCommand) writeField12(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("components_list", thrift.LIST, 12); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ComponentsList)); err != nil {
		return err
	}
	for _, v := range p.ComponentsList {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}
func (p *ShortcutCommand) writeField15(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("card_schema", thrift.STRING, 15); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CardSchema); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 end error: ", p), err)
}
func (p *ShortcutCommand) writeField16(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("command_id", thrift.I64, 16); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.CommandID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 end error: ", p), err)
}
func (p *ShortcutCommand) writeField17(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("tool_info", thrift.STRUCT, 17); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.ToolInfo.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 end error: ", p), err)
}
func (p *ShortcutCommand) writeField18(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("shortcut_icon", thrift.STRUCT, 18); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.ShortcutIcon.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 18 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 18 end error: ", p), err)
}
func (p *ShortcutCommand) writeField21(oprot thrift.TProtocol) (err error) {
	if p.IsSetAgentID() {
		if err = oprot.WriteFieldBegin("agent_id", thrift.STRING, 21); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.AgentID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 21 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 21 end error: ", p), err)
}
func (p *ShortcutCommand) writeField22(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("plugin_api_id", thrift.I64, 22); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.PluginAPIID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 22 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 22 end error: ", p), err)
}

func (p *ShortcutCommand) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ShortcutCommand(%+v)", *p)

}

type ShortcutFileInfo struct {
	URL string `thrift:"url,1" form:"url" json:"url" query:"url"`
	URI string `thrift:"uri,2" form:"uri" json:"uri" query:"uri"`
}

func NewShortcutFileInfo() *ShortcutFileInfo {
	return &ShortcutFileInfo{}
}

func (p *ShortcutFileInfo) InitDefault() {
}

func (p *ShortcutFileInfo) GetURL() (v string) {
	return p.URL
}

func (p *ShortcutFileInfo) GetURI() (v string) {
	return p.URI
}

var fieldIDToName_ShortcutFileInfo = map[int16]string{
	1: "url",
	2: "uri",
}

func (p *ShortcutFileInfo) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ShortcutFileInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ShortcutFileInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.URL = _field
	return nil
}
func (p *ShortcutFileInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.URI = _field
	return nil
}

func (p *ShortcutFileInfo) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ShortcutFileInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ShortcutFileInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("url", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.URL); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ShortcutFileInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("uri", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.URI); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ShortcutFileInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ShortcutFileInfo(%+v)", *p)

}

type Components struct {
	// Panel parameters
	Name        string    `thrift:"name,1" form:"name" json:"name" query:"name"`
	Description string    `thrift:"description,2" form:"description" json:"description" query:"description"`
	InputType   InputType `thrift:"input_type,3" form:"input_type" json:"input_type" query:"input_type"`
	// When requesting the tool, the key of the parameter
	Parameter    string        `thrift:"parameter,4" form:"parameter" json:"parameter" query:"parameter"`
	Options      []string      `thrift:"options,5" form:"options" json:"options" query:"options"`
	DefaultValue *DefaultValue `thrift:"default_value,6" form:"default_value" json:"default_value" query:"default_value"`
	// Whether to hide or not to show
	Hide bool `thrift:"hide,7" form:"hide" json:"hide" query:"hide"`
	// What types are supported input_type MixUpload
	UploadOptions []InputType `thrift:"upload_options,8" form:"upload_options" json:"upload_options" query:"upload_options"`
}

func NewComponents() *Components {
	return &Components{}
}

func (p *Components) InitDefault() {
}

func (p *Components) GetName() (v string) {
	return p.Name
}

func (p *Components) GetDescription() (v string) {
	return p.Description
}

func (p *Components) GetInputType() (v InputType) {
	return p.InputType
}

func (p *Components) GetParameter() (v string) {
	return p.Parameter
}

func (p *Components) GetOptions() (v []string) {
	return p.Options
}

var Components_DefaultValue_DEFAULT *DefaultValue

func (p *Components) GetDefaultValue() (v *DefaultValue) {
	if !p.IsSetDefaultValue() {
		return Components_DefaultValue_DEFAULT
	}
	return p.DefaultValue
}

func (p *Components) GetHide() (v bool) {
	return p.Hide
}

func (p *Components) GetUploadOptions() (v []InputType) {
	return p.UploadOptions
}

var fieldIDToName_Components = map[int16]string{
	1: "name",
	2: "description",
	3: "input_type",
	4: "parameter",
	5: "options",
	6: "default_value",
	7: "hide",
	8: "upload_options",
}

func (p *Components) IsSetDefaultValue() bool {
	return p.DefaultValue != nil
}

func (p *Components) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_Components[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *Components) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Name = _field
	return nil
}
func (p *Components) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Description = _field
	return nil
}
func (p *Components) ReadField3(iprot thrift.TProtocol) error {

	var _field InputType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InputType(v)
	}
	p.InputType = _field
	return nil
}
func (p *Components) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Parameter = _field
	return nil
}
func (p *Components) ReadField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Options = _field
	return nil
}
func (p *Components) ReadField6(iprot thrift.TProtocol) error {
	_field := NewDefaultValue()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.DefaultValue = _field
	return nil
}
func (p *Components) ReadField7(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Hide = _field
	return nil
}
func (p *Components) ReadField8(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]InputType, 0, size)
	for i := 0; i < size; i++ {

		var _elem InputType
		if v, err := iprot.ReadI32(); err != nil {
			return err
		} else {
			_elem = InputType(v)
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.UploadOptions = _field
	return nil
}

func (p *Components) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("Components"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *Components) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("name", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Name); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *Components) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("description", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Description); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *Components) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("input_type", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InputType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *Components) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("parameter", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Parameter); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *Components) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("options", thrift.LIST, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.Options)); err != nil {
		return err
	}
	for _, v := range p.Options {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *Components) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("default_value", thrift.STRUCT, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.DefaultValue.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *Components) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("hide", thrift.BOOL, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.Hide); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}
func (p *Components) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("upload_options", thrift.LIST, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.I32, len(p.UploadOptions)); err != nil {
		return err
	}
	for _, v := range p.UploadOptions {
		if err := oprot.WriteI32(int32(v)); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *Components) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Components(%+v)", *p)

}

type DefaultValue struct {
	Value string    `thrift:"value,1" form:"value" json:"value" query:"value"`
	Type  InputType `thrift:"type,2" form:"type" json:"type" query:"type"`
}

func NewDefaultValue() *DefaultValue {
	return &DefaultValue{}
}

func (p *DefaultValue) InitDefault() {
}

func (p *DefaultValue) GetValue() (v string) {
	return p.Value
}

func (p *DefaultValue) GetType() (v InputType) {
	return p.Type
}

var fieldIDToName_DefaultValue = map[int16]string{
	1: "value",
	2: "type",
}

func (p *DefaultValue) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DefaultValue[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DefaultValue) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Value = _field
	return nil
}
func (p *DefaultValue) ReadField2(iprot thrift.TProtocol) error {

	var _field InputType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InputType(v)
	}
	p.Type = _field
	return nil
}

func (p *DefaultValue) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("DefaultValue"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DefaultValue) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("value", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Value); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *DefaultValue) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("type", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.Type)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DefaultValue) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DefaultValue(%+v)", *p)

}

type ToolInfo struct {
	ToolName string `thrift:"tool_name,1" form:"tool_name" json:"tool_name" query:"tool_name"`
	// Variable lists, plugins & workFLow
	ToolParamsList []*ToolParams `thrift:"tool_params_list,2" form:"tool_params_list" json:"tool_params_list" query:"tool_params_list"`
}

func NewToolInfo() *ToolInfo {
	return &ToolInfo{}
}

func (p *ToolInfo) InitDefault() {
}

func (p *ToolInfo) GetToolName() (v string) {
	return p.ToolName
}

func (p *ToolInfo) GetToolParamsList() (v []*ToolParams) {
	return p.ToolParamsList
}

var fieldIDToName_ToolInfo = map[int16]string{
	1: "tool_name",
	2: "tool_params_list",
}

func (p *ToolInfo) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ToolInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ToolInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ToolName = _field
	return nil
}
func (p *ToolInfo) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*ToolParams, 0, size)
	values := make([]ToolParams, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ToolParamsList = _field
	return nil
}

func (p *ToolInfo) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ToolInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ToolInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("tool_name", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ToolName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ToolInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("tool_params_list", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ToolParamsList)); err != nil {
		return err
	}
	for _, v := range p.ToolParamsList {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ToolInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ToolInfo(%+v)", *p)

}

type ToolParams struct {
	// parameter list
	Name     string `thrift:"name,1" form:"name" json:"name" query:"name"`
	Required bool   `thrift:"required,2" form:"required" json:"required" query:"required"`
	Desc     string `thrift:"desc,3" form:"desc" json:"desc" query:"desc"`
	Type     string `thrift:"type,4" form:"type" json:"type" query:"type"`
	// default value
	DefaultValue string `thrift:"default_value,6" form:"default_value" json:"default_value" query:"default_value"`
	// Is it a panel parameter?
	ReferComponent bool `thrift:"refer_component,8" form:"refer_component" json:"refer_component" query:"refer_component"`
}

func NewToolParams() *ToolParams {
	return &ToolParams{}
}

func (p *ToolParams) InitDefault() {
}

func (p *ToolParams) GetName() (v string) {
	return p.Name
}

func (p *ToolParams) GetRequired() (v bool) {
	return p.Required
}

func (p *ToolParams) GetDesc() (v string) {
	return p.Desc
}

func (p *ToolParams) GetType() (v string) {
	return p.Type
}

func (p *ToolParams) GetDefaultValue() (v string) {
	return p.DefaultValue
}

func (p *ToolParams) GetReferComponent() (v bool) {
	return p.ReferComponent
}

var fieldIDToName_ToolParams = map[int16]string{
	1: "name",
	2: "required",
	3: "desc",
	4: "type",
	6: "default_value",
	8: "refer_component",
}

func (p *ToolParams) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ToolParams[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ToolParams) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Name = _field
	return nil
}
func (p *ToolParams) ReadField2(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Required = _field
	return nil
}
func (p *ToolParams) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Desc = _field
	return nil
}
func (p *ToolParams) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Type = _field
	return nil
}
func (p *ToolParams) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DefaultValue = _field
	return nil
}
func (p *ToolParams) ReadField8(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ReferComponent = _field
	return nil
}

func (p *ToolParams) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ToolParams"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ToolParams) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("name", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Name); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ToolParams) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("required", thrift.BOOL, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.Required); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ToolParams) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("desc", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Desc); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *ToolParams) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("type", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Type); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *ToolParams) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("default_value", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DefaultValue); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *ToolParams) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("refer_component", thrift.BOOL, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.ReferComponent); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *ToolParams) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ToolParams(%+v)", *p)

}

type CreateUpdateShortcutCommandRequest struct {
	ObjectID  int64            `thrift:"object_id,1,required" form:"object_id,required" json:"object_id,string,required" query:"object_id,required"`
	SpaceID   int64            `thrift:"space_id,2,required" form:"space_id,required" json:"space_id,string,required" query:"space_id,required"`
	Shortcuts *ShortcutCommand `thrift:"shortcuts,3,required" form:"shortcuts,required" json:"shortcuts,required" query:"shortcuts,required"`
	Base      *base.Base       `thrift:"Base,255" form:"Base" json:"Base" query:"Base"`
}

func NewCreateUpdateShortcutCommandRequest() *CreateUpdateShortcutCommandRequest {
	return &CreateUpdateShortcutCommandRequest{}
}

func (p *CreateUpdateShortcutCommandRequest) InitDefault() {
}

func (p *CreateUpdateShortcutCommandRequest) GetObjectID() (v int64) {
	return p.ObjectID
}

func (p *CreateUpdateShortcutCommandRequest) GetSpaceID() (v int64) {
	return p.SpaceID
}

var CreateUpdateShortcutCommandRequest_Shortcuts_DEFAULT *ShortcutCommand

func (p *CreateUpdateShortcutCommandRequest) GetShortcuts() (v *ShortcutCommand) {
	if !p.IsSetShortcuts() {
		return CreateUpdateShortcutCommandRequest_Shortcuts_DEFAULT
	}
	return p.Shortcuts
}

var CreateUpdateShortcutCommandRequest_Base_DEFAULT *base.Base

func (p *CreateUpdateShortcutCommandRequest) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return CreateUpdateShortcutCommandRequest_Base_DEFAULT
	}
	return p.Base
}

var fieldIDToName_CreateUpdateShortcutCommandRequest = map[int16]string{
	1:   "object_id",
	2:   "space_id",
	3:   "shortcuts",
	255: "Base",
}

func (p *CreateUpdateShortcutCommandRequest) IsSetShortcuts() bool {
	return p.Shortcuts != nil
}

func (p *CreateUpdateShortcutCommandRequest) IsSetBase() bool {
	return p.Base != nil
}

func (p *CreateUpdateShortcutCommandRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetObjectID bool = false
	var issetSpaceID bool = false
	var issetShortcuts bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetObjectID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetSpaceID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetShortcuts = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetObjectID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetSpaceID {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetShortcuts {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateUpdateShortcutCommandRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateUpdateShortcutCommandRequest[fieldId]))
}

func (p *CreateUpdateShortcutCommandRequest) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ObjectID = _field
	return nil
}
func (p *CreateUpdateShortcutCommandRequest) ReadField2(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SpaceID = _field
	return nil
}
func (p *CreateUpdateShortcutCommandRequest) ReadField3(iprot thrift.TProtocol) error {
	_field := NewShortcutCommand()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Shortcuts = _field
	return nil
}
func (p *CreateUpdateShortcutCommandRequest) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *CreateUpdateShortcutCommandRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("CreateUpdateShortcutCommandRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateUpdateShortcutCommandRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("object_id", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.ObjectID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *CreateUpdateShortcutCommandRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("space_id", thrift.I64, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.SpaceID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *CreateUpdateShortcutCommandRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("shortcuts", thrift.STRUCT, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Shortcuts.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *CreateUpdateShortcutCommandRequest) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Base.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *CreateUpdateShortcutCommandRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateUpdateShortcutCommandRequest(%+v)", *p)

}

type CreateUpdateShortcutCommandResponse struct {
	Shortcuts *ShortcutCommand `thrift:"shortcuts,1" form:"shortcuts" json:"shortcuts" query:"shortcuts"`
	Code      int64            `thrift:"code,253,required" form:"code,required" json:"code,required" query:"code,required"`
	Msg       string           `thrift:"msg,254,required" form:"msg,required" json:"msg,required" query:"msg,required"`
	BaseResp  *base.BaseResp   `thrift:"BaseResp,255,required" form:"BaseResp,required" json:"BaseResp,required" query:"BaseResp,required"`
}

func NewCreateUpdateShortcutCommandResponse() *CreateUpdateShortcutCommandResponse {
	return &CreateUpdateShortcutCommandResponse{}
}

func (p *CreateUpdateShortcutCommandResponse) InitDefault() {
}

var CreateUpdateShortcutCommandResponse_Shortcuts_DEFAULT *ShortcutCommand

func (p *CreateUpdateShortcutCommandResponse) GetShortcuts() (v *ShortcutCommand) {
	if !p.IsSetShortcuts() {
		return CreateUpdateShortcutCommandResponse_Shortcuts_DEFAULT
	}
	return p.Shortcuts
}

func (p *CreateUpdateShortcutCommandResponse) GetCode() (v int64) {
	return p.Code
}

func (p *CreateUpdateShortcutCommandResponse) GetMsg() (v string) {
	return p.Msg
}

var CreateUpdateShortcutCommandResponse_BaseResp_DEFAULT *base.BaseResp

func (p *CreateUpdateShortcutCommandResponse) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return CreateUpdateShortcutCommandResponse_BaseResp_DEFAULT
	}
	return p.BaseResp
}

var fieldIDToName_CreateUpdateShortcutCommandResponse = map[int16]string{
	1:   "shortcuts",
	253: "code",
	254: "msg",
	255: "BaseResp",
}

func (p *CreateUpdateShortcutCommandResponse) IsSetShortcuts() bool {
	return p.Shortcuts != nil
}

func (p *CreateUpdateShortcutCommandResponse) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *CreateUpdateShortcutCommandResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetCode bool = false
	var issetMsg bool = false
	var issetBaseResp bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 253:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField253(iprot); err != nil {
					goto ReadFieldError
				}
				issetCode = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 254:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField254(iprot); err != nil {
					goto ReadFieldError
				}
				issetMsg = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
				issetBaseResp = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetCode {
		fieldId = 253
		goto RequiredFieldNotSetError
	}

	if !issetMsg {
		fieldId = 254
		goto RequiredFieldNotSetError
	}

	if !issetBaseResp {
		fieldId = 255
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateUpdateShortcutCommandResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateUpdateShortcutCommandResponse[fieldId]))
}

func (p *CreateUpdateShortcutCommandResponse) ReadField1(iprot thrift.TProtocol) error {
	_field := NewShortcutCommand()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Shortcuts = _field
	return nil
}
func (p *CreateUpdateShortcutCommandResponse) ReadField253(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Code = _field
	return nil
}
func (p *CreateUpdateShortcutCommandResponse) ReadField254(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Msg = _field
	return nil
}
func (p *CreateUpdateShortcutCommandResponse) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *CreateUpdateShortcutCommandResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("CreateUpdateShortcutCommandResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField253(oprot); err != nil {
			fieldId = 253
			goto WriteFieldError
		}
		if err = p.writeField254(oprot); err != nil {
			fieldId = 254
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateUpdateShortcutCommandResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("shortcuts", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Shortcuts.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *CreateUpdateShortcutCommandResponse) writeField253(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("code", thrift.I64, 253); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.Code); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 253 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 253 end error: ", p), err)
}
func (p *CreateUpdateShortcutCommandResponse) writeField254(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("msg", thrift.STRING, 254); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Msg); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 254 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 254 end error: ", p), err)
}
func (p *CreateUpdateShortcutCommandResponse) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.BaseResp.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *CreateUpdateShortcutCommandResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateUpdateShortcutCommandResponse(%+v)", *p)

}
