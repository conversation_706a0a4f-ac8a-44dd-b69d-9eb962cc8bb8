import os
from pydoc import doc
from docx import Document
from docx.shared import Inches,Pt
from flask import Flask, json, request, send_file,Blueprint
from docx.enum.text import WD_BREAK 
import io
from docx.oxml.shared import OxmlElement
from docx.oxml.shared import qn
from .routes import generate_app


def insert_content_paragraphs(document, current_paragraph, content):
    # 根据\n分割内容
    content_list = content.split('\n')
    for index,content_item in enumerate(content_list):
        if content_item.strip():
                new_paragraph = current_paragraph.insert_paragraph_before('', style='Normal')
                new_paragraph.text = content_item
                new_paragraph.paragraph_format.first_line_indent = Inches(0.3)
                new_paragraph.paragraph_format.left_indent = Inches(0.2)
                new_paragraph.paragraph_format.right_indent = Inches(0.2)
    return current_paragraph
        
def insert_content(doc, paragraph, content):
   # 一对花括号{{}}在f-string中会被解释为一个字面意义上的单个花括号
    parsed_value = json.loads(content)
        
    # 剔除占位
    paragraph.text = paragraph.text.replace("{{content}}", '')
    # 识别是对象|数组|字符串
    if isinstance(parsed_value, dict):
        # "对象"
        insert_content_paragraphs(doc, paragraph, parsed_value.get('content', ''))
    elif isinstance(parsed_value, list):
        # "数组"
        for index,item in enumerate(parsed_value):
            # parseItem = json.loads(item)
            level = item.get('level', 1)
            target = item.get("target", '')
            content = item.get('content', '')

            # 创建新段落并设置文本
            new_paragraph = paragraph.insert_paragraph_before()
            
            new_paragraph.text = target
            
            # 确保段落有文本内容后再设置字体
            if new_paragraph.runs:
                new_paragraph.runs[0].font.size = Pt(13)  # 设置level 1的字体大小为13磅
            
            # 根据level设置缩进
            if level == 1:
                new_paragraph.paragraph_format.left_indent = Inches(0)
            elif level == 2:
                new_paragraph.paragraph_format.left_indent = Inches(0.2)
            else:
                new_paragraph.paragraph_format.left_indent = Inches(0.4)
            
            # 添加内容
            if content:
                content_paragraph = insert_content_paragraphs(doc, new_paragraph, content)

            # 每个章节下，再插入一个空行
            if level == 1:
                paragraph.insert_paragraph_before('')

            # 正文
            # insert_content_paragraphs(doc, paragraph, content)

            # 每个小章节尾部插入一个空行,最后一个不插入
            if level != 1 and index != len(parsed_value) - 1:
                paragraph.insert_paragraph_before('')       
                
                
def get_paragraph_text(paragraph):
    """获取段落文本内容"""
    return ''.join([run.text for run in paragraph.runs]) if hasattr(paragraph, 'runs') else paragraph.text


def process_paragraph(doc, paragraph, title, content):
    """处理单个段落"""
    text = get_paragraph_text(paragraph)
    if not text or text.strip() == '':
        return
    
    if "{{title}}" in text:
        # 创建新的run并设置文本
        paragraph.text = paragraph.text.replace("{{title}}", title)
    elif "{{content}}" in text:
        insert_content(doc, paragraph, content)

@generate_app.route('/report', methods=['POST'])
def generate():
    # 获取表单数据
    title = request.form.get('title','')
    content = request.form.get('content','')
    
    print("Content-Type:", request.headers.get('Content-Type'))
    # 打开模板文档
    current_dir = os.path.dirname(os.path.abspath(__file__))
    template_path = os.path.join(current_dir, "..", "..", "static", "open_report.docx")
    doc = Document(template_path)
    
    paragraphs_to_remove = []
    
    # 遍历文档中的所有段落和表格
    all_tables = doc.tables
    
    for table in all_tables:
        for row in table.rows:
            for cell in row.cells:
                for paragraph in cell.paragraphs:
                    process_paragraph(doc, paragraph, title, content)


    # 循环结束后，删除标记的空段落
    for para in paragraphs_to_remove:
        p = para._element
        p.getparent().remove(p)
        
    
    # 将修改后的文档保存到内存中
    output = io.BytesIO()
    doc.save(output)
    output.seek(0)

    # 保存到本地
    # output_dir = os.path.join(current_dir, "..", "..", "output")
    # os.makedirs(output_dir, exist_ok=True)
    # output_path = os.path.join(output_dir, "report.docx")
    # doc.save(output_path)
    
    # 返回生成的文档
    return send_file(output, mimetype='application/vnd.openxmlformats-officedocument.wordprocessingml.document', as_attachment=True, download_name='开题报告.docx')
