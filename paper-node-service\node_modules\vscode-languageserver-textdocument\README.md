# Text Document implementation for a LSP Node server

[![NPM Version](https://img.shields.io/npm/v/vscode-languageserver-textdocument.svg)](https://npmjs.org/package/vscode-languageserver-textdocument)
[![NPM Downloads](https://img.shields.io/npm/dm/vscode-languageserver-textdocument.svg)](https://npmjs.org/package/vscode-languageserver-textdocument)

Npm module containing a simple text document implementation for [Node.js](https://nodejs.org/) language server

Click [here](https://code.visualstudio.com/docs/extensions/example-language-server) for a detailed document on how
to implement language servers for [VSCode](https://code.visualstudio.com/).

## History

### 1.04

- Introduced ESLint rule for member delimiter style.

### 1.03

- Moved to ES2020 target and lib

### 1.0.2

- JSDoc updates

### 1.0.0

Initial version.

## License
[MIT](https://github.com/Microsoft/vscode-languageserver-node/blob/master/License.txt)
