// Code generated by MockGen. DO NOT EDIT.
// Source: service.go
//
// Generated by this command:
//
//	mockgen -destination ../../../internal/mock/domain/plugin/interface.go --package mockPlugin -source service.go
//

// Package mockPlugin is a generated GoMock package.
package mockPlugin

import (
	context "context"
	reflect "reflect"

	entity "github.com/coze-dev/coze-studio/backend/domain/plugin/entity"
	service "github.com/coze-dev/coze-studio/backend/domain/plugin/service"
	gomock "go.uber.org/mock/gomock"
)

// MockPluginService is a mock of PluginService interface.
type MockPluginService struct {
	ctrl     *gomock.Controller
	recorder *MockPluginServiceMockRecorder
}

// MockPluginServiceMockRecorder is the mock recorder for MockPluginService.
type MockPluginServiceMockRecorder struct {
	mock *MockPluginService
}

// NewMockPluginService creates a new mock instance.
func NewMockPluginService(ctrl *gomock.Controller) *MockPluginService {
	mock := &MockPluginService{ctrl: ctrl}
	mock.recorder = &MockPluginServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPluginService) EXPECT() *MockPluginServiceMockRecorder {
	return m.recorder
}

// BindAgentTools mocks base method.
func (m *MockPluginService) BindAgentTools(ctx context.Context, agentID int64, toolIDs []int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BindAgentTools", ctx, agentID, toolIDs)
	ret0, _ := ret[0].(error)
	return ret0
}

// BindAgentTools indicates an expected call of BindAgentTools.
func (mr *MockPluginServiceMockRecorder) BindAgentTools(ctx, agentID, toolIDs any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BindAgentTools", reflect.TypeOf((*MockPluginService)(nil).BindAgentTools), ctx, agentID, toolIDs)
}

// CheckPluginToolsDebugStatus mocks base method.
func (m *MockPluginService) CheckPluginToolsDebugStatus(ctx context.Context, pluginID int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckPluginToolsDebugStatus", ctx, pluginID)
	ret0, _ := ret[0].(error)
	return ret0
}

// CheckPluginToolsDebugStatus indicates an expected call of CheckPluginToolsDebugStatus.
func (mr *MockPluginServiceMockRecorder) CheckPluginToolsDebugStatus(ctx, pluginID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckPluginToolsDebugStatus", reflect.TypeOf((*MockPluginService)(nil).CheckPluginToolsDebugStatus), ctx, pluginID)
}

// ConvertToOpenapi3Doc mocks base method.
func (m *MockPluginService) ConvertToOpenapi3Doc(ctx context.Context, req *service.ConvertToOpenapi3DocRequest) *service.ConvertToOpenapi3DocResponse {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ConvertToOpenapi3Doc", ctx, req)
	ret0, _ := ret[0].(*service.ConvertToOpenapi3DocResponse)
	return ret0
}

// ConvertToOpenapi3Doc indicates an expected call of ConvertToOpenapi3Doc.
func (mr *MockPluginServiceMockRecorder) ConvertToOpenapi3Doc(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConvertToOpenapi3Doc", reflect.TypeOf((*MockPluginService)(nil).ConvertToOpenapi3Doc), ctx, req)
}

// CopyPlugin mocks base method.
func (m *MockPluginService) CopyPlugin(ctx context.Context, req *service.CopyPluginRequest) (*service.CopyPluginResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CopyPlugin", ctx, req)
	ret0, _ := ret[0].(*service.CopyPluginResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CopyPlugin indicates an expected call of CopyPlugin.
func (mr *MockPluginServiceMockRecorder) CopyPlugin(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CopyPlugin", reflect.TypeOf((*MockPluginService)(nil).CopyPlugin), ctx, req)
}

// CreateDraftPlugin mocks base method.
func (m *MockPluginService) CreateDraftPlugin(ctx context.Context, req *service.CreateDraftPluginRequest) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateDraftPlugin", ctx, req)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateDraftPlugin indicates an expected call of CreateDraftPlugin.
func (mr *MockPluginServiceMockRecorder) CreateDraftPlugin(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateDraftPlugin", reflect.TypeOf((*MockPluginService)(nil).CreateDraftPlugin), ctx, req)
}

// CreateDraftPluginWithCode mocks base method.
func (m *MockPluginService) CreateDraftPluginWithCode(ctx context.Context, req *service.CreateDraftPluginWithCodeRequest) (*service.CreateDraftPluginWithCodeResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateDraftPluginWithCode", ctx, req)
	ret0, _ := ret[0].(*service.CreateDraftPluginWithCodeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateDraftPluginWithCode indicates an expected call of CreateDraftPluginWithCode.
func (mr *MockPluginServiceMockRecorder) CreateDraftPluginWithCode(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateDraftPluginWithCode", reflect.TypeOf((*MockPluginService)(nil).CreateDraftPluginWithCode), ctx, req)
}

// CreateDraftToolsWithCode mocks base method.
func (m *MockPluginService) CreateDraftToolsWithCode(ctx context.Context, req *service.CreateDraftToolsWithCodeRequest) (*service.CreateDraftToolsWithCodeResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateDraftToolsWithCode", ctx, req)
	ret0, _ := ret[0].(*service.CreateDraftToolsWithCodeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateDraftToolsWithCode indicates an expected call of CreateDraftToolsWithCode.
func (mr *MockPluginServiceMockRecorder) CreateDraftToolsWithCode(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateDraftToolsWithCode", reflect.TypeOf((*MockPluginService)(nil).CreateDraftToolsWithCode), ctx, req)
}

// DeleteAPPAllPlugins mocks base method.
func (m *MockPluginService) DeleteAPPAllPlugins(ctx context.Context, appID int64) ([]int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteAPPAllPlugins", ctx, appID)
	ret0, _ := ret[0].([]int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteAPPAllPlugins indicates an expected call of DeleteAPPAllPlugins.
func (mr *MockPluginServiceMockRecorder) DeleteAPPAllPlugins(ctx, appID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteAPPAllPlugins", reflect.TypeOf((*MockPluginService)(nil).DeleteAPPAllPlugins), ctx, appID)
}

// DeleteDraftPlugin mocks base method.
func (m *MockPluginService) DeleteDraftPlugin(ctx context.Context, pluginID int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteDraftPlugin", ctx, pluginID)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteDraftPlugin indicates an expected call of DeleteDraftPlugin.
func (mr *MockPluginServiceMockRecorder) DeleteDraftPlugin(ctx, pluginID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteDraftPlugin", reflect.TypeOf((*MockPluginService)(nil).DeleteDraftPlugin), ctx, pluginID)
}

// DuplicateDraftAgentTools mocks base method.
func (m *MockPluginService) DuplicateDraftAgentTools(ctx context.Context, fromAgentID, toAgentID int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DuplicateDraftAgentTools", ctx, fromAgentID, toAgentID)
	ret0, _ := ret[0].(error)
	return ret0
}

// DuplicateDraftAgentTools indicates an expected call of DuplicateDraftAgentTools.
func (mr *MockPluginServiceMockRecorder) DuplicateDraftAgentTools(ctx, fromAgentID, toAgentID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DuplicateDraftAgentTools", reflect.TypeOf((*MockPluginService)(nil).DuplicateDraftAgentTools), ctx, fromAgentID, toAgentID)
}

// ExecuteTool mocks base method.
func (m *MockPluginService) ExecuteTool(ctx context.Context, req *service.ExecuteToolRequest, opts ...entity.ExecuteToolOpt) (*service.ExecuteToolResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ExecuteTool", varargs...)
	ret0, _ := ret[0].(*service.ExecuteToolResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ExecuteTool indicates an expected call of ExecuteTool.
func (mr *MockPluginServiceMockRecorder) ExecuteTool(ctx, req any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExecuteTool", reflect.TypeOf((*MockPluginService)(nil).ExecuteTool), varargs...)
}

// GetAPPAllPlugins mocks base method.
func (m *MockPluginService) GetAPPAllPlugins(ctx context.Context, appID int64) ([]*entity.PluginInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAPPAllPlugins", ctx, appID)
	ret0, _ := ret[0].([]*entity.PluginInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAPPAllPlugins indicates an expected call of GetAPPAllPlugins.
func (mr *MockPluginServiceMockRecorder) GetAPPAllPlugins(ctx, appID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAPPAllPlugins", reflect.TypeOf((*MockPluginService)(nil).GetAPPAllPlugins), ctx, appID)
}

// GetAccessToken mocks base method.
func (m *MockPluginService) GetAccessToken(ctx context.Context, oa *entity.OAuthInfo) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAccessToken", ctx, oa)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAccessToken indicates an expected call of GetAccessToken.
func (mr *MockPluginServiceMockRecorder) GetAccessToken(ctx, oa any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccessToken", reflect.TypeOf((*MockPluginService)(nil).GetAccessToken), ctx, oa)
}

// GetAgentPluginsOAuthStatus mocks base method.
func (m *MockPluginService) GetAgentPluginsOAuthStatus(ctx context.Context, userID, agentID int64) ([]*service.AgentPluginOAuthStatus, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAgentPluginsOAuthStatus", ctx, userID, agentID)
	ret0, _ := ret[0].([]*service.AgentPluginOAuthStatus)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAgentPluginsOAuthStatus indicates an expected call of GetAgentPluginsOAuthStatus.
func (mr *MockPluginServiceMockRecorder) GetAgentPluginsOAuthStatus(ctx, userID, agentID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAgentPluginsOAuthStatus", reflect.TypeOf((*MockPluginService)(nil).GetAgentPluginsOAuthStatus), ctx, userID, agentID)
}

// GetDraftAgentToolByName mocks base method.
func (m *MockPluginService) GetDraftAgentToolByName(ctx context.Context, agentID int64, toolName string) (*entity.ToolInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDraftAgentToolByName", ctx, agentID, toolName)
	ret0, _ := ret[0].(*entity.ToolInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDraftAgentToolByName indicates an expected call of GetDraftAgentToolByName.
func (mr *MockPluginServiceMockRecorder) GetDraftAgentToolByName(ctx, agentID, toolName any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDraftAgentToolByName", reflect.TypeOf((*MockPluginService)(nil).GetDraftAgentToolByName), ctx, agentID, toolName)
}

// GetDraftPlugin mocks base method.
func (m *MockPluginService) GetDraftPlugin(ctx context.Context, pluginID int64) (*entity.PluginInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDraftPlugin", ctx, pluginID)
	ret0, _ := ret[0].(*entity.PluginInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDraftPlugin indicates an expected call of GetDraftPlugin.
func (mr *MockPluginServiceMockRecorder) GetDraftPlugin(ctx, pluginID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDraftPlugin", reflect.TypeOf((*MockPluginService)(nil).GetDraftPlugin), ctx, pluginID)
}

// GetOAuthStatus mocks base method.
func (m *MockPluginService) GetOAuthStatus(ctx context.Context, userID, pluginID int64) (*service.GetOAuthStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOAuthStatus", ctx, userID, pluginID)
	ret0, _ := ret[0].(*service.GetOAuthStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOAuthStatus indicates an expected call of GetOAuthStatus.
func (mr *MockPluginServiceMockRecorder) GetOAuthStatus(ctx, userID, pluginID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOAuthStatus", reflect.TypeOf((*MockPluginService)(nil).GetOAuthStatus), ctx, userID, pluginID)
}

// GetOnlinePlugin mocks base method.
func (m *MockPluginService) GetOnlinePlugin(ctx context.Context, pluginID int64) (*entity.PluginInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOnlinePlugin", ctx, pluginID)
	ret0, _ := ret[0].(*entity.PluginInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOnlinePlugin indicates an expected call of GetOnlinePlugin.
func (mr *MockPluginServiceMockRecorder) GetOnlinePlugin(ctx, pluginID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOnlinePlugin", reflect.TypeOf((*MockPluginService)(nil).GetOnlinePlugin), ctx, pluginID)
}

// GetOnlineTool mocks base method.
func (m *MockPluginService) GetOnlineTool(ctx context.Context, toolID int64) (*entity.ToolInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOnlineTool", ctx, toolID)
	ret0, _ := ret[0].(*entity.ToolInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOnlineTool indicates an expected call of GetOnlineTool.
func (mr *MockPluginServiceMockRecorder) GetOnlineTool(ctx, toolID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOnlineTool", reflect.TypeOf((*MockPluginService)(nil).GetOnlineTool), ctx, toolID)
}

// GetPluginNextVersion mocks base method.
func (m *MockPluginService) GetPluginNextVersion(ctx context.Context, pluginID int64) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPluginNextVersion", ctx, pluginID)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPluginNextVersion indicates an expected call of GetPluginNextVersion.
func (mr *MockPluginServiceMockRecorder) GetPluginNextVersion(ctx, pluginID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPluginNextVersion", reflect.TypeOf((*MockPluginService)(nil).GetPluginNextVersion), ctx, pluginID)
}

// GetPluginProductAllTools mocks base method.
func (m *MockPluginService) GetPluginProductAllTools(ctx context.Context, pluginID int64) ([]*entity.ToolInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPluginProductAllTools", ctx, pluginID)
	ret0, _ := ret[0].([]*entity.ToolInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPluginProductAllTools indicates an expected call of GetPluginProductAllTools.
func (mr *MockPluginServiceMockRecorder) GetPluginProductAllTools(ctx, pluginID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPluginProductAllTools", reflect.TypeOf((*MockPluginService)(nil).GetPluginProductAllTools), ctx, pluginID)
}

// ListCustomOnlinePlugins mocks base method.
func (m *MockPluginService) ListCustomOnlinePlugins(ctx context.Context, spaceID int64, pageInfo entity.PageInfo) ([]*entity.PluginInfo, int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListCustomOnlinePlugins", ctx, spaceID, pageInfo)
	ret0, _ := ret[0].([]*entity.PluginInfo)
	ret1, _ := ret[1].(int64)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ListCustomOnlinePlugins indicates an expected call of ListCustomOnlinePlugins.
func (mr *MockPluginServiceMockRecorder) ListCustomOnlinePlugins(ctx, spaceID, pageInfo any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListCustomOnlinePlugins", reflect.TypeOf((*MockPluginService)(nil).ListCustomOnlinePlugins), ctx, spaceID, pageInfo)
}

// ListDraftPlugins mocks base method.
func (m *MockPluginService) ListDraftPlugins(ctx context.Context, req *service.ListDraftPluginsRequest) (*service.ListDraftPluginsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListDraftPlugins", ctx, req)
	ret0, _ := ret[0].(*service.ListDraftPluginsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListDraftPlugins indicates an expected call of ListDraftPlugins.
func (mr *MockPluginServiceMockRecorder) ListDraftPlugins(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListDraftPlugins", reflect.TypeOf((*MockPluginService)(nil).ListDraftPlugins), ctx, req)
}

// ListPluginProducts mocks base method.
func (m *MockPluginService) ListPluginProducts(ctx context.Context, req *service.ListPluginProductsRequest) (*service.ListPluginProductsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListPluginProducts", ctx, req)
	ret0, _ := ret[0].(*service.ListPluginProductsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListPluginProducts indicates an expected call of ListPluginProducts.
func (mr *MockPluginServiceMockRecorder) ListPluginProducts(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListPluginProducts", reflect.TypeOf((*MockPluginService)(nil).ListPluginProducts), ctx, req)
}

// MGetAgentTools mocks base method.
func (m *MockPluginService) MGetAgentTools(ctx context.Context, req *service.MGetAgentToolsRequest) ([]*entity.ToolInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MGetAgentTools", ctx, req)
	ret0, _ := ret[0].([]*entity.ToolInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MGetAgentTools indicates an expected call of MGetAgentTools.
func (mr *MockPluginServiceMockRecorder) MGetAgentTools(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MGetAgentTools", reflect.TypeOf((*MockPluginService)(nil).MGetAgentTools), ctx, req)
}

// MGetDraftPlugins mocks base method.
func (m *MockPluginService) MGetDraftPlugins(ctx context.Context, pluginIDs []int64) ([]*entity.PluginInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MGetDraftPlugins", ctx, pluginIDs)
	ret0, _ := ret[0].([]*entity.PluginInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MGetDraftPlugins indicates an expected call of MGetDraftPlugins.
func (mr *MockPluginServiceMockRecorder) MGetDraftPlugins(ctx, pluginIDs any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MGetDraftPlugins", reflect.TypeOf((*MockPluginService)(nil).MGetDraftPlugins), ctx, pluginIDs)
}

// MGetDraftTools mocks base method.
func (m *MockPluginService) MGetDraftTools(ctx context.Context, toolIDs []int64) ([]*entity.ToolInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MGetDraftTools", ctx, toolIDs)
	ret0, _ := ret[0].([]*entity.ToolInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MGetDraftTools indicates an expected call of MGetDraftTools.
func (mr *MockPluginServiceMockRecorder) MGetDraftTools(ctx, toolIDs any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MGetDraftTools", reflect.TypeOf((*MockPluginService)(nil).MGetDraftTools), ctx, toolIDs)
}

// MGetOnlinePlugins mocks base method.
func (m *MockPluginService) MGetOnlinePlugins(ctx context.Context, pluginIDs []int64) ([]*entity.PluginInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MGetOnlinePlugins", ctx, pluginIDs)
	ret0, _ := ret[0].([]*entity.PluginInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MGetOnlinePlugins indicates an expected call of MGetOnlinePlugins.
func (mr *MockPluginServiceMockRecorder) MGetOnlinePlugins(ctx, pluginIDs any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MGetOnlinePlugins", reflect.TypeOf((*MockPluginService)(nil).MGetOnlinePlugins), ctx, pluginIDs)
}

// MGetOnlineTools mocks base method.
func (m *MockPluginService) MGetOnlineTools(ctx context.Context, toolIDs []int64) ([]*entity.ToolInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MGetOnlineTools", ctx, toolIDs)
	ret0, _ := ret[0].([]*entity.ToolInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MGetOnlineTools indicates an expected call of MGetOnlineTools.
func (mr *MockPluginServiceMockRecorder) MGetOnlineTools(ctx, toolIDs any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MGetOnlineTools", reflect.TypeOf((*MockPluginService)(nil).MGetOnlineTools), ctx, toolIDs)
}

// MGetPluginLatestVersion mocks base method.
func (m *MockPluginService) MGetPluginLatestVersion(ctx context.Context, pluginIDs []int64) (*service.MGetPluginLatestVersionResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MGetPluginLatestVersion", ctx, pluginIDs)
	ret0, _ := ret[0].(*service.MGetPluginLatestVersionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MGetPluginLatestVersion indicates an expected call of MGetPluginLatestVersion.
func (mr *MockPluginServiceMockRecorder) MGetPluginLatestVersion(ctx, pluginIDs any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MGetPluginLatestVersion", reflect.TypeOf((*MockPluginService)(nil).MGetPluginLatestVersion), ctx, pluginIDs)
}

// MGetVersionPlugins mocks base method.
func (m *MockPluginService) MGetVersionPlugins(ctx context.Context, versionPlugins []entity.VersionPlugin) ([]*entity.PluginInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MGetVersionPlugins", ctx, versionPlugins)
	ret0, _ := ret[0].([]*entity.PluginInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MGetVersionPlugins indicates an expected call of MGetVersionPlugins.
func (mr *MockPluginServiceMockRecorder) MGetVersionPlugins(ctx, versionPlugins any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MGetVersionPlugins", reflect.TypeOf((*MockPluginService)(nil).MGetVersionPlugins), ctx, versionPlugins)
}

// MGetVersionTools mocks base method.
func (m *MockPluginService) MGetVersionTools(ctx context.Context, versionTools []entity.VersionTool) ([]*entity.ToolInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MGetVersionTools", ctx, versionTools)
	ret0, _ := ret[0].([]*entity.ToolInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MGetVersionTools indicates an expected call of MGetVersionTools.
func (mr *MockPluginServiceMockRecorder) MGetVersionTools(ctx, versionTools any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MGetVersionTools", reflect.TypeOf((*MockPluginService)(nil).MGetVersionTools), ctx, versionTools)
}

// MoveAPPPluginToLibrary mocks base method.
func (m *MockPluginService) MoveAPPPluginToLibrary(ctx context.Context, pluginID int64) (*entity.PluginInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MoveAPPPluginToLibrary", ctx, pluginID)
	ret0, _ := ret[0].(*entity.PluginInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MoveAPPPluginToLibrary indicates an expected call of MoveAPPPluginToLibrary.
func (mr *MockPluginServiceMockRecorder) MoveAPPPluginToLibrary(ctx, pluginID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MoveAPPPluginToLibrary", reflect.TypeOf((*MockPluginService)(nil).MoveAPPPluginToLibrary), ctx, pluginID)
}

// OAuthCode mocks base method.
func (m *MockPluginService) OAuthCode(ctx context.Context, code string, state *entity.OAuthState) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OAuthCode", ctx, code, state)
	ret0, _ := ret[0].(error)
	return ret0
}

// OAuthCode indicates an expected call of OAuthCode.
func (mr *MockPluginServiceMockRecorder) OAuthCode(ctx, code, state any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OAuthCode", reflect.TypeOf((*MockPluginService)(nil).OAuthCode), ctx, code, state)
}

// PublishAPPPlugins mocks base method.
func (m *MockPluginService) PublishAPPPlugins(ctx context.Context, req *service.PublishAPPPluginsRequest) (*service.PublishAPPPluginsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PublishAPPPlugins", ctx, req)
	ret0, _ := ret[0].(*service.PublishAPPPluginsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PublishAPPPlugins indicates an expected call of PublishAPPPlugins.
func (mr *MockPluginServiceMockRecorder) PublishAPPPlugins(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PublishAPPPlugins", reflect.TypeOf((*MockPluginService)(nil).PublishAPPPlugins), ctx, req)
}

// PublishAgentTools mocks base method.
func (m *MockPluginService) PublishAgentTools(ctx context.Context, agentID int64, agentVersion string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PublishAgentTools", ctx, agentID, agentVersion)
	ret0, _ := ret[0].(error)
	return ret0
}

// PublishAgentTools indicates an expected call of PublishAgentTools.
func (mr *MockPluginServiceMockRecorder) PublishAgentTools(ctx, agentID, agentVersion any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PublishAgentTools", reflect.TypeOf((*MockPluginService)(nil).PublishAgentTools), ctx, agentID, agentVersion)
}

// PublishPlugin mocks base method.
func (m *MockPluginService) PublishPlugin(ctx context.Context, req *service.PublishPluginRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PublishPlugin", ctx, req)
	ret0, _ := ret[0].(error)
	return ret0
}

// PublishPlugin indicates an expected call of PublishPlugin.
func (mr *MockPluginServiceMockRecorder) PublishPlugin(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PublishPlugin", reflect.TypeOf((*MockPluginService)(nil).PublishPlugin), ctx, req)
}

// RevokeAccessToken mocks base method.
func (m *MockPluginService) RevokeAccessToken(ctx context.Context, meta *entity.AuthorizationCodeMeta) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RevokeAccessToken", ctx, meta)
	ret0, _ := ret[0].(error)
	return ret0
}

// RevokeAccessToken indicates an expected call of RevokeAccessToken.
func (mr *MockPluginServiceMockRecorder) RevokeAccessToken(ctx, meta any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RevokeAccessToken", reflect.TypeOf((*MockPluginService)(nil).RevokeAccessToken), ctx, meta)
}

// UpdateBotDefaultParams mocks base method.
func (m *MockPluginService) UpdateBotDefaultParams(ctx context.Context, req *service.UpdateBotDefaultParamsRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateBotDefaultParams", ctx, req)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateBotDefaultParams indicates an expected call of UpdateBotDefaultParams.
func (mr *MockPluginServiceMockRecorder) UpdateBotDefaultParams(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateBotDefaultParams", reflect.TypeOf((*MockPluginService)(nil).UpdateBotDefaultParams), ctx, req)
}

// UpdateDraftPlugin mocks base method.
func (m *MockPluginService) UpdateDraftPlugin(ctx context.Context, plugin *service.UpdateDraftPluginRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateDraftPlugin", ctx, plugin)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateDraftPlugin indicates an expected call of UpdateDraftPlugin.
func (mr *MockPluginServiceMockRecorder) UpdateDraftPlugin(ctx, plugin any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateDraftPlugin", reflect.TypeOf((*MockPluginService)(nil).UpdateDraftPlugin), ctx, plugin)
}

// UpdateDraftPluginWithCode mocks base method.
func (m *MockPluginService) UpdateDraftPluginWithCode(ctx context.Context, req *service.UpdateDraftPluginWithCodeRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateDraftPluginWithCode", ctx, req)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateDraftPluginWithCode indicates an expected call of UpdateDraftPluginWithCode.
func (mr *MockPluginServiceMockRecorder) UpdateDraftPluginWithCode(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateDraftPluginWithCode", reflect.TypeOf((*MockPluginService)(nil).UpdateDraftPluginWithCode), ctx, req)
}

// UpdateDraftTool mocks base method.
func (m *MockPluginService) UpdateDraftTool(ctx context.Context, req *service.UpdateDraftToolRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateDraftTool", ctx, req)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateDraftTool indicates an expected call of UpdateDraftTool.
func (mr *MockPluginServiceMockRecorder) UpdateDraftTool(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateDraftTool", reflect.TypeOf((*MockPluginService)(nil).UpdateDraftTool), ctx, req)
}
