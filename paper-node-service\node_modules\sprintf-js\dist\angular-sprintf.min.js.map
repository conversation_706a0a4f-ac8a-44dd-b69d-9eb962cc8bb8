{"version": 3, "sources": ["angular-sprintf.js"], "names": ["angular", "module", "filter", "sprintf", "apply", "arguments", "$filter", "format", "argv", "vsprintf"], "mappings": ";CAEC,WACG,aAEAA,QACIC,OAAO,UAAW,IAClBC,OAAO,UAAW,WACd,OAAO,WACH,OAAOC,QAAQC,MAAM,KAAMC,cAGnCH,OAAO,MAAO,CAAC,UAAW,SAASI,GAC/B,OAAOA,EAAQ,cAEnBJ,OAAO,WAAY,WACf,OAAO,SAASK,EAAQC,GACpB,OAAOC,SAASF,EAAQC,MAGhCN,OAAO,OAAQ,CAAC,UAAW,SAASI,GAChC,OAAOA,EAAQ,eAnB1B", "file": "angular-sprintf.min.js", "sourcesContent": ["/* global angular, sprintf, vsprintf */\n\n!function() {\n    'use strict'\n\n    angular.\n        module('sprintf', []).\n        filter('sprintf', function() {\n            return function() {\n                return sprintf.apply(null, arguments)\n            }\n        }).\n        filter('fmt', ['$filter', function($filter) {\n            return $filter('sprintf')\n        }]).\n        filter('vsprintf', function() {\n            return function(format, argv) {\n                return vsprintf(format, argv)\n            }\n        }).\n        filter('vfmt', ['$filter', function($filter) {\n            return $filter('vsprintf')\n        }])\n}(); // eslint-disable-line\n"]}