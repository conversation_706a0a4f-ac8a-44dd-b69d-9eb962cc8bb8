# coding: utf-8

# flake8: noqa

"""
    privatelink

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


from __future__ import absolute_import

# import apis into sdk package
from volcenginesdkprivatelink.api.privatelink_api import PRIVATELINKApi

# import models into sdk package
from volcenginesdkprivatelink.models.add_permission_to_vpc_endpoint_service_request import AddPermissionToVpcEndpointServiceRequest
from volcenginesdkprivatelink.models.add_permission_to_vpc_endpoint_service_response import AddPermissionToVpcEndpointServiceResponse
from volcenginesdkprivatelink.models.add_zone_to_vpc_endpoint_request import AddZoneToVpcEndpointRequest
from volcenginesdkprivatelink.models.add_zone_to_vpc_endpoint_response import AddZoneToVpcEndpointResponse
from volcenginesdkprivatelink.models.assign_private_ip_addresses_to_vpc_link_request import AssignPrivateIpAddressesToVpcLinkRequest
from volcenginesdkprivatelink.models.assign_private_ip_addresses_to_vpc_link_response import AssignPrivateIpAddressesToVpcLinkResponse
from volcenginesdkprivatelink.models.attach_resource_to_vpc_endpoint_service_request import AttachResourceToVpcEndpointServiceRequest
from volcenginesdkprivatelink.models.attach_resource_to_vpc_endpoint_service_response import AttachResourceToVpcEndpointServiceResponse
from volcenginesdkprivatelink.models.attach_security_group_to_vpc_endpoint_request import AttachSecurityGroupToVpcEndpointRequest
from volcenginesdkprivatelink.models.attach_security_group_to_vpc_endpoint_response import AttachSecurityGroupToVpcEndpointResponse
from volcenginesdkprivatelink.models.attach_security_groups_to_private_link_gateway_request import AttachSecurityGroupsToPrivateLinkGatewayRequest
from volcenginesdkprivatelink.models.attach_security_groups_to_private_link_gateway_response import AttachSecurityGroupsToPrivateLinkGatewayResponse
from volcenginesdkprivatelink.models.create_private_link_gateway_request import CreatePrivateLinkGatewayRequest
from volcenginesdkprivatelink.models.create_private_link_gateway_response import CreatePrivateLinkGatewayResponse
from volcenginesdkprivatelink.models.create_unique_resource_type_vpc_endpoint_service_request import CreateUniqueResourceTypeVpcEndpointServiceRequest
from volcenginesdkprivatelink.models.create_unique_resource_type_vpc_endpoint_service_response import CreateUniqueResourceTypeVpcEndpointServiceResponse
from volcenginesdkprivatelink.models.create_vpc_endpoint_request import CreateVpcEndpointRequest
from volcenginesdkprivatelink.models.create_vpc_endpoint_response import CreateVpcEndpointResponse
from volcenginesdkprivatelink.models.create_vpc_endpoint_service_request import CreateVpcEndpointServiceRequest
from volcenginesdkprivatelink.models.create_vpc_endpoint_service_response import CreateVpcEndpointServiceResponse
from volcenginesdkprivatelink.models.create_vpc_gateway_endpoint_request import CreateVpcGatewayEndpointRequest
from volcenginesdkprivatelink.models.create_vpc_gateway_endpoint_response import CreateVpcGatewayEndpointResponse
from volcenginesdkprivatelink.models.create_vpc_link_request import CreateVpcLinkRequest
from volcenginesdkprivatelink.models.create_vpc_link_response import CreateVpcLinkResponse
from volcenginesdkprivatelink.models.delete_private_link_gateway_request import DeletePrivateLinkGatewayRequest
from volcenginesdkprivatelink.models.delete_private_link_gateway_response import DeletePrivateLinkGatewayResponse
from volcenginesdkprivatelink.models.delete_vpc_endpoint_request import DeleteVpcEndpointRequest
from volcenginesdkprivatelink.models.delete_vpc_endpoint_response import DeleteVpcEndpointResponse
from volcenginesdkprivatelink.models.delete_vpc_endpoint_service_request import DeleteVpcEndpointServiceRequest
from volcenginesdkprivatelink.models.delete_vpc_endpoint_service_response import DeleteVpcEndpointServiceResponse
from volcenginesdkprivatelink.models.delete_vpc_gateway_endpoint_request import DeleteVpcGatewayEndpointRequest
from volcenginesdkprivatelink.models.delete_vpc_gateway_endpoint_response import DeleteVpcGatewayEndpointResponse
from volcenginesdkprivatelink.models.delete_vpc_link_request import DeleteVpcLinkRequest
from volcenginesdkprivatelink.models.delete_vpc_link_response import DeleteVpcLinkResponse
from volcenginesdkprivatelink.models.describe_private_link_available_zones_request import DescribePrivateLinkAvailableZonesRequest
from volcenginesdkprivatelink.models.describe_private_link_available_zones_response import DescribePrivateLinkAvailableZonesResponse
from volcenginesdkprivatelink.models.describe_private_link_gateway_attributes_request import DescribePrivateLinkGatewayAttributesRequest
from volcenginesdkprivatelink.models.describe_private_link_gateway_attributes_response import DescribePrivateLinkGatewayAttributesResponse
from volcenginesdkprivatelink.models.describe_private_link_gateway_available_zones_request import DescribePrivateLinkGatewayAvailableZonesRequest
from volcenginesdkprivatelink.models.describe_private_link_gateway_available_zones_response import DescribePrivateLinkGatewayAvailableZonesResponse
from volcenginesdkprivatelink.models.describe_private_link_gateway_security_groups_request import DescribePrivateLinkGatewaySecurityGroupsRequest
from volcenginesdkprivatelink.models.describe_private_link_gateway_security_groups_response import DescribePrivateLinkGatewaySecurityGroupsResponse
from volcenginesdkprivatelink.models.describe_private_link_gateways_request import DescribePrivateLinkGatewaysRequest
from volcenginesdkprivatelink.models.describe_private_link_gateways_response import DescribePrivateLinkGatewaysResponse
from volcenginesdkprivatelink.models.describe_vpc_endpoint_attributes_request import DescribeVpcEndpointAttributesRequest
from volcenginesdkprivatelink.models.describe_vpc_endpoint_attributes_response import DescribeVpcEndpointAttributesResponse
from volcenginesdkprivatelink.models.describe_vpc_endpoint_connections_request import DescribeVpcEndpointConnectionsRequest
from volcenginesdkprivatelink.models.describe_vpc_endpoint_connections_response import DescribeVpcEndpointConnectionsResponse
from volcenginesdkprivatelink.models.describe_vpc_endpoint_security_groups_request import DescribeVpcEndpointSecurityGroupsRequest
from volcenginesdkprivatelink.models.describe_vpc_endpoint_security_groups_response import DescribeVpcEndpointSecurityGroupsResponse
from volcenginesdkprivatelink.models.describe_vpc_endpoint_service_attributes_request import DescribeVpcEndpointServiceAttributesRequest
from volcenginesdkprivatelink.models.describe_vpc_endpoint_service_attributes_response import DescribeVpcEndpointServiceAttributesResponse
from volcenginesdkprivatelink.models.describe_vpc_endpoint_service_permissions_request import DescribeVpcEndpointServicePermissionsRequest
from volcenginesdkprivatelink.models.describe_vpc_endpoint_service_permissions_response import DescribeVpcEndpointServicePermissionsResponse
from volcenginesdkprivatelink.models.describe_vpc_endpoint_service_resources_request import DescribeVpcEndpointServiceResourcesRequest
from volcenginesdkprivatelink.models.describe_vpc_endpoint_service_resources_response import DescribeVpcEndpointServiceResourcesResponse
from volcenginesdkprivatelink.models.describe_vpc_endpoint_services_by_end_user_request import DescribeVpcEndpointServicesByEndUserRequest
from volcenginesdkprivatelink.models.describe_vpc_endpoint_services_by_end_user_response import DescribeVpcEndpointServicesByEndUserResponse
from volcenginesdkprivatelink.models.describe_vpc_endpoint_services_request import DescribeVpcEndpointServicesRequest
from volcenginesdkprivatelink.models.describe_vpc_endpoint_services_response import DescribeVpcEndpointServicesResponse
from volcenginesdkprivatelink.models.describe_vpc_endpoint_zones_request import DescribeVpcEndpointZonesRequest
from volcenginesdkprivatelink.models.describe_vpc_endpoint_zones_response import DescribeVpcEndpointZonesResponse
from volcenginesdkprivatelink.models.describe_vpc_endpoints_request import DescribeVpcEndpointsRequest
from volcenginesdkprivatelink.models.describe_vpc_endpoints_response import DescribeVpcEndpointsResponse
from volcenginesdkprivatelink.models.describe_vpc_gateway_endpoint_attributes_request import DescribeVpcGatewayEndpointAttributesRequest
from volcenginesdkprivatelink.models.describe_vpc_gateway_endpoint_attributes_response import DescribeVpcGatewayEndpointAttributesResponse
from volcenginesdkprivatelink.models.describe_vpc_gateway_endpoint_services_request import DescribeVpcGatewayEndpointServicesRequest
from volcenginesdkprivatelink.models.describe_vpc_gateway_endpoint_services_response import DescribeVpcGatewayEndpointServicesResponse
from volcenginesdkprivatelink.models.describe_vpc_gateway_endpoints_request import DescribeVpcGatewayEndpointsRequest
from volcenginesdkprivatelink.models.describe_vpc_gateway_endpoints_response import DescribeVpcGatewayEndpointsResponse
from volcenginesdkprivatelink.models.describe_vpc_link_attributes_request import DescribeVpcLinkAttributesRequest
from volcenginesdkprivatelink.models.describe_vpc_link_attributes_response import DescribeVpcLinkAttributesResponse
from volcenginesdkprivatelink.models.describe_vpc_links_request import DescribeVpcLinksRequest
from volcenginesdkprivatelink.models.describe_vpc_links_response import DescribeVpcLinksResponse
from volcenginesdkprivatelink.models.detach_resource_from_vpc_endpoint_service_request import DetachResourceFromVpcEndpointServiceRequest
from volcenginesdkprivatelink.models.detach_resource_from_vpc_endpoint_service_response import DetachResourceFromVpcEndpointServiceResponse
from volcenginesdkprivatelink.models.detach_security_group_from_vpc_endpoint_request import DetachSecurityGroupFromVpcEndpointRequest
from volcenginesdkprivatelink.models.detach_security_group_from_vpc_endpoint_response import DetachSecurityGroupFromVpcEndpointResponse
from volcenginesdkprivatelink.models.detach_security_groups_from_private_link_gateway_request import DetachSecurityGroupsFromPrivateLinkGatewayRequest
from volcenginesdkprivatelink.models.detach_security_groups_from_private_link_gateway_response import DetachSecurityGroupsFromPrivateLinkGatewayResponse
from volcenginesdkprivatelink.models.disable_vpc_endpoint_connection_request import DisableVpcEndpointConnectionRequest
from volcenginesdkprivatelink.models.disable_vpc_endpoint_connection_response import DisableVpcEndpointConnectionResponse
from volcenginesdkprivatelink.models.enable_vpc_endpoint_connection_request import EnableVpcEndpointConnectionRequest
from volcenginesdkprivatelink.models.enable_vpc_endpoint_connection_response import EnableVpcEndpointConnectionResponse
from volcenginesdkprivatelink.models.endpoint_connection_for_describe_vpc_endpoint_connections_output import EndpointConnectionForDescribeVpcEndpointConnectionsOutput
from volcenginesdkprivatelink.models.endpoint_for_describe_vpc_endpoint_attributes_output import EndpointForDescribeVpcEndpointAttributesOutput
from volcenginesdkprivatelink.models.endpoint_for_describe_vpc_endpoints_output import EndpointForDescribeVpcEndpointsOutput
from volcenginesdkprivatelink.models.endpoint_for_describe_vpc_gateway_endpoints_output import EndpointForDescribeVpcGatewayEndpointsOutput
from volcenginesdkprivatelink.models.endpoint_zone_for_describe_vpc_endpoint_zones_output import EndpointZoneForDescribeVpcEndpointZonesOutput
from volcenginesdkprivatelink.models.gateway_endpoint_for_describe_vpc_gateway_endpoints_output import GatewayEndpointForDescribeVpcGatewayEndpointsOutput
from volcenginesdkprivatelink.models.list_tags_for_resources_request import ListTagsForResourcesRequest
from volcenginesdkprivatelink.models.list_tags_for_resources_response import ListTagsForResourcesResponse
from volcenginesdkprivatelink.models.modify_endpoint_payer_account_request import ModifyEndpointPayerAccountRequest
from volcenginesdkprivatelink.models.modify_endpoint_payer_account_response import ModifyEndpointPayerAccountResponse
from volcenginesdkprivatelink.models.modify_private_link_gateway_attributes_request import ModifyPrivateLinkGatewayAttributesRequest
from volcenginesdkprivatelink.models.modify_private_link_gateway_attributes_response import ModifyPrivateLinkGatewayAttributesResponse
from volcenginesdkprivatelink.models.modify_unique_resource_type_vpc_endpoint_service_attributes_request import ModifyUniqueResourceTypeVpcEndpointServiceAttributesRequest
from volcenginesdkprivatelink.models.modify_unique_resource_type_vpc_endpoint_service_attributes_response import ModifyUniqueResourceTypeVpcEndpointServiceAttributesResponse
from volcenginesdkprivatelink.models.modify_vpc_endpoint_attributes_request import ModifyVpcEndpointAttributesRequest
from volcenginesdkprivatelink.models.modify_vpc_endpoint_attributes_response import ModifyVpcEndpointAttributesResponse
from volcenginesdkprivatelink.models.modify_vpc_endpoint_connections_request import ModifyVpcEndpointConnectionsRequest
from volcenginesdkprivatelink.models.modify_vpc_endpoint_connections_response import ModifyVpcEndpointConnectionsResponse
from volcenginesdkprivatelink.models.modify_vpc_endpoint_service_attributes_request import ModifyVpcEndpointServiceAttributesRequest
from volcenginesdkprivatelink.models.modify_vpc_endpoint_service_attributes_response import ModifyVpcEndpointServiceAttributesResponse
from volcenginesdkprivatelink.models.modify_vpc_endpoint_service_resource_attributes_request import ModifyVpcEndpointServiceResourceAttributesRequest
from volcenginesdkprivatelink.models.modify_vpc_endpoint_service_resource_attributes_response import ModifyVpcEndpointServiceResourceAttributesResponse
from volcenginesdkprivatelink.models.modify_vpc_gateway_endpoint_attributes_request import ModifyVpcGatewayEndpointAttributesRequest
from volcenginesdkprivatelink.models.modify_vpc_gateway_endpoint_attributes_response import ModifyVpcGatewayEndpointAttributesResponse
from volcenginesdkprivatelink.models.modify_vpc_link_attributes_request import ModifyVpcLinkAttributesRequest
from volcenginesdkprivatelink.models.modify_vpc_link_attributes_response import ModifyVpcLinkAttributesResponse
from volcenginesdkprivatelink.models.permission_for_describe_vpc_endpoint_service_permissions_output import PermissionForDescribeVpcEndpointServicePermissionsOutput
from volcenginesdkprivatelink.models.private_dns_name_configuration_for_describe_vpc_endpoint_service_attributes_output import PrivateDNSNameConfigurationForDescribeVpcEndpointServiceAttributesOutput
from volcenginesdkprivatelink.models.private_dns_name_configuration_for_describe_vpc_endpoint_services_by_end_user_output import PrivateDNSNameConfigurationForDescribeVpcEndpointServicesByEndUserOutput
from volcenginesdkprivatelink.models.private_dns_name_configuration_for_describe_vpc_endpoint_services_output import PrivateDNSNameConfigurationForDescribeVpcEndpointServicesOutput
from volcenginesdkprivatelink.models.private_link_gateway_for_describe_private_link_gateways_output import PrivateLinkGatewayForDescribePrivateLinkGatewaysOutput
from volcenginesdkprivatelink.models.remove_permission_from_vpc_endpoint_service_request import RemovePermissionFromVpcEndpointServiceRequest
from volcenginesdkprivatelink.models.remove_permission_from_vpc_endpoint_service_response import RemovePermissionFromVpcEndpointServiceResponse
from volcenginesdkprivatelink.models.remove_zone_from_vpc_endpoint_request import RemoveZoneFromVpcEndpointRequest
from volcenginesdkprivatelink.models.remove_zone_from_vpc_endpoint_response import RemoveZoneFromVpcEndpointResponse
from volcenginesdkprivatelink.models.resource_for_create_unique_resource_type_vpc_endpoint_service_input import ResourceForCreateUniqueResourceTypeVpcEndpointServiceInput
from volcenginesdkprivatelink.models.resource_for_create_vpc_endpoint_service_input import ResourceForCreateVpcEndpointServiceInput
from volcenginesdkprivatelink.models.resource_for_describe_vpc_endpoint_service_resources_output import ResourceForDescribeVpcEndpointServiceResourcesOutput
from volcenginesdkprivatelink.models.resource_for_modify_unique_resource_type_vpc_endpoint_service_attributes_input import ResourceForModifyUniqueResourceTypeVpcEndpointServiceAttributesInput
from volcenginesdkprivatelink.models.resource_tag_for_list_tags_for_resources_output import ResourceTagForListTagsForResourcesOutput
from volcenginesdkprivatelink.models.resources_allocate_for_enable_vpc_endpoint_connection_input import ResourcesAllocateForEnableVpcEndpointConnectionInput
from volcenginesdkprivatelink.models.tag_filter_for_describe_vpc_endpoint_services_input import TagFilterForDescribeVpcEndpointServicesInput
from volcenginesdkprivatelink.models.tag_filter_for_describe_vpc_endpoints_input import TagFilterForDescribeVpcEndpointsInput
from volcenginesdkprivatelink.models.tag_filter_for_describe_vpc_gateway_endpoint_services_input import TagFilterForDescribeVpcGatewayEndpointServicesInput
from volcenginesdkprivatelink.models.tag_filter_for_describe_vpc_gateway_endpoints_input import TagFilterForDescribeVpcGatewayEndpointsInput
from volcenginesdkprivatelink.models.tag_filter_for_list_tags_for_resources_input import TagFilterForListTagsForResourcesInput
from volcenginesdkprivatelink.models.tag_for_create_vpc_endpoint_input import TagForCreateVpcEndpointInput
from volcenginesdkprivatelink.models.tag_for_create_vpc_endpoint_service_input import TagForCreateVpcEndpointServiceInput
from volcenginesdkprivatelink.models.tag_for_create_vpc_gateway_endpoint_input import TagForCreateVpcGatewayEndpointInput
from volcenginesdkprivatelink.models.tag_for_describe_vpc_endpoint_attributes_output import TagForDescribeVpcEndpointAttributesOutput
from volcenginesdkprivatelink.models.tag_for_describe_vpc_endpoint_service_attributes_output import TagForDescribeVpcEndpointServiceAttributesOutput
from volcenginesdkprivatelink.models.tag_for_describe_vpc_endpoint_services_by_end_user_output import TagForDescribeVpcEndpointServicesByEndUserOutput
from volcenginesdkprivatelink.models.tag_for_describe_vpc_endpoint_services_output import TagForDescribeVpcEndpointServicesOutput
from volcenginesdkprivatelink.models.tag_for_describe_vpc_endpoints_output import TagForDescribeVpcEndpointsOutput
from volcenginesdkprivatelink.models.tag_for_describe_vpc_gateway_endpoint_attributes_output import TagForDescribeVpcGatewayEndpointAttributesOutput
from volcenginesdkprivatelink.models.tag_for_describe_vpc_gateway_endpoints_output import TagForDescribeVpcGatewayEndpointsOutput
from volcenginesdkprivatelink.models.tag_for_tag_resources_input import TagForTagResourcesInput
from volcenginesdkprivatelink.models.tag_resources_request import TagResourcesRequest
from volcenginesdkprivatelink.models.tag_resources_response import TagResourcesResponse
from volcenginesdkprivatelink.models.tags_for_create_unique_resource_type_vpc_endpoint_service_input import TagsForCreateUniqueResourceTypeVpcEndpointServiceInput
from volcenginesdkprivatelink.models.un_assign_private_ip_addresses_from_vpc_link_request import UnAssignPrivateIpAddressesFromVpcLinkRequest
from volcenginesdkprivatelink.models.un_assign_private_ip_addresses_from_vpc_link_response import UnAssignPrivateIpAddressesFromVpcLinkResponse
from volcenginesdkprivatelink.models.untag_resources_request import UntagResourcesRequest
from volcenginesdkprivatelink.models.untag_resources_response import UntagResourcesResponse
from volcenginesdkprivatelink.models.verify_vpc_endpoint_service_private_dns_request import VerifyVpcEndpointServicePrivateDNSRequest
from volcenginesdkprivatelink.models.verify_vpc_endpoint_service_private_dns_response import VerifyVpcEndpointServicePrivateDNSResponse
from volcenginesdkprivatelink.models.vpc_endpoint_service_for_describe_vpc_endpoint_services_by_end_user_output import VpcEndpointServiceForDescribeVpcEndpointServicesByEndUserOutput
from volcenginesdkprivatelink.models.vpc_endpoint_service_for_describe_vpc_endpoint_services_output import VpcEndpointServiceForDescribeVpcEndpointServicesOutput
from volcenginesdkprivatelink.models.vpc_gateway_endpoint_service_for_describe_vpc_gateway_endpoint_services_output import VpcGatewayEndpointServiceForDescribeVpcGatewayEndpointServicesOutput
from volcenginesdkprivatelink.models.vpc_link_for_describe_vpc_links_output import VpcLinkForDescribeVpcLinksOutput
from volcenginesdkprivatelink.models.zone_for_create_vpc_endpoint_input import ZoneForCreateVpcEndpointInput
from volcenginesdkprivatelink.models.zone_for_describe_vpc_endpoint_connections_output import ZoneForDescribeVpcEndpointConnectionsOutput
