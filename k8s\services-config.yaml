# 服务配置 ConfigMap
apiVersion: v1
kind: ConfigMap
metadata:
  name: services-config
  namespace: paper-services
data:
  # 服务发现配置
  services.yaml: |
    services:
      paper-editor-api:
        host: paper-editor-api
        port: 8890
        endpoints:
          - /api/v1/health
          - /api/v1/files
          - /api/v1/knowledge
      
      paper-node-service:
        host: paper-node-service
        port: 9529
        endpoints:
          - /api/mermaid
          - /api/katex
          - /api/health
      
      paper-py-service:
        host: paper-py-service
        port: 9528
        endpoints:
          - /generate/word
          - /analyze/pdf
          - /health
      
      lunwen-generate-ui:
        host: lunwen-generate-ui
        port: 3000
        type: frontend
  
  # 环境变量配置
  app.env: |
    # API 服务地址
    PAPER_EDITOR_API_URL=http://paper-editor-api:8890
    PAPER_NODE_SERVICE_URL=http://paper-node-service:9529
    PAPER_PY_SERVICE_URL=http://paper-py-service:9528
    
    # 数据库配置 (如果需要)
    DB_HOST=postgres
    DB_PORT=5432
    REDIS_HOST=redis
    REDIS_PORT=6379
