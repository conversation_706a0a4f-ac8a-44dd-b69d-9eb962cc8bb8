# This is a RocksDB option file.
#
# For detailed file format spec, please refer to the example file
# in examples/rocksdb_option_file_example.ini
#

[Version]
  rocksdb_version=6.29.5
  options_file_version=1.1

[DBOptions]
  compaction_readahead_size=0
  strict_bytes_per_sync=false
  bytes_per_sync=0
  max_background_jobs=2
  base_background_compactions=-1
  avoid_flush_during_shutdown=false
  max_background_flushes=1
  delayed_write_rate=16777216
  max_open_files=-1
  max_subcompactions=1
  writable_file_max_buffer_size=1048576
  wal_bytes_per_sync=0
  max_background_compactions=-1
  max_total_wal_size=0
  delete_obsolete_files_period_micros=21600000000
  stats_dump_period_sec=600
  stats_history_buffer_size=1048576
  stats_persist_period_sec=600
  lowest_used_cache_tier=kNonVolatileBlockTier
  bgerror_resume_retry_interval=1000000
  best_efforts_recovery=false
  log_readahead_size=0
  write_dbid_to_manifest=false
  atomic_flush=false
  manual_wal_flush=false
  db_host_id=__hostname__
  two_write_queues=false
  rate_limiter=nullptr
  random_access_max_buffer_size=1048576
  avoid_unnecessary_blocking_io=false
  skip_checking_sst_file_sizes_on_db_open=false
  flush_verify_memtable_count=true
  fail_if_options_file_error=false
  track_and_verify_wals_in_manifest=false
  experimental_mempurge_threshold=0.000000
  paranoid_checks=true
  create_if_missing=true
  max_write_batch_group_size_bytes=1048576
  avoid_flush_during_recovery=false
  skip_stats_update_on_db_open=false
  file_checksum_gen_factory=nullptr
  enable_thread_tracking=false
  use_fsync=false
  allow_fallocate=true
  preserve_deletes=false
  new_table_reader_for_compaction_inputs=false
  allow_data_in_errors=false
  error_if_exists=false
  use_direct_io_for_flush_and_compaction=false
  create_missing_column_families=true
  WAL_size_limit_MB=0
  use_direct_reads=false
  persist_stats_to_disk=false
  allow_mmap_reads=false
  allow_mmap_writes=false
  use_adaptive_mutex=false
  allow_2pc=false
  is_fd_close_on_exec=true
  max_log_file_size=0
  access_hint_on_compaction_start=NORMAL
  max_file_opening_threads=16
  wal_filter=nullptr
  table_cache_numshardbits=6
  dump_malloc_stats=false
  db_write_buffer_size=0
  allow_ingest_behind=false
  keep_log_file_num=1000
  max_bgerror_resume_count=2147483647
  allow_concurrent_memtable_write=true
  recycle_log_file_num=0
  log_file_time_to_roll=0
  manifest_preallocation_size=4194304
  enable_write_thread_adaptive_yield=true
  WAL_ttl_seconds=0
  max_manifest_file_size=1073741824
  wal_recovery_mode=kPointInTimeRecovery
  enable_pipelined_write=false
  write_thread_slow_yield_usec=3
  unordered_write=false
  write_thread_max_yield_usec=100
  advise_random_on_open=true
  info_log_level=INFO_LEVEL
  

[CFOptions "default"]
  compression_opts={max_dict_buffer_bytes=0;enabled=false;max_dict_bytes=0;parallel_threads=1;zstd_max_train_bytes=0;level=32767;strategy=0;window_bits=-14;}
  bottommost_compression=kDisableCompressionOption
  enable_blob_garbage_collection=false
  blob_file_size=268435456
  sample_for_compression=0
  periodic_compaction_seconds=0
  ttl=2592000
  blob_garbage_collection_age_cutoff=0.250000
  compaction_options_universal={incremental=false;compression_size_percent=-1;allow_trivial_move=false;max_size_amplification_percent=200;max_merge_width=4294967295;stop_style=kCompactionStopStyleTotalSize;min_merge_width=2;size_ratio=1;}
  compression=kNoCompression
  max_sequential_skip_in_iterations=8
  max_bytes_for_level_multiplier_additional=1:1:1:1:1:1:1
  max_bytes_for_level_multiplier=10.000000
  min_blob_size=0
  check_flush_compaction_key_order=true
  disable_auto_compactions=false
  bottommost_compression_opts={max_dict_buffer_bytes=0;enabled=false;max_dict_bytes=0;parallel_threads=1;zstd_max_train_bytes=0;level=32767;strategy=0;window_bits=-14;}
  compaction_options_fifo={allow_compaction=false;age_for_warm=0;max_table_files_size=1073741824;}
  level0_file_num_compaction_trigger=4
  target_file_size_base=67108864
  soft_pending_compaction_bytes_limit=68719476736
  hard_pending_compaction_bytes_limit=274877906944
  level0_slowdown_writes_trigger=20
  blob_compression_type=kNoCompression
  level0_stop_writes_trigger=36
  enable_blob_files=false
  blob_garbage_collection_force_threshold=1.000000
  paranoid_file_checks=false
  prefix_extractor=nullptr
  max_write_buffer_number=2
  report_bg_io_stats=false
  memtable_prefix_bloom_size_ratio=0.000000
  target_file_size_multiplier=2
  arena_block_size=1048576
  blob_compaction_readahead_size=0
  inplace_update_num_locks=10000
  max_compaction_bytes=1677721600
  write_buffer_size=67108864
  memtable_huge_page_size=0
  max_successive_merges=0
  max_bytes_for_level_base=268435456
  memtable_whole_key_filtering=false
  compaction_pri=kMinOverlappingRatio
  compaction_filter_factory=nullptr
  comparator=leveldb.BytewiseComparator
  table_factory=BlockBasedTable
  inplace_update_support=false
  max_write_buffer_number_to_maintain=0
  bloom_locality=0
  compaction_filter=nullptr
  level_compaction_dynamic_level_bytes=false
  optimize_filters_for_hits=false
  merge_operator=nullptr
  max_write_buffer_size_to_maintain=0
  sst_partitioner_factory=nullptr
  compaction_style=kCompactionStyleLevel
  min_write_buffer_number_to_merge=1
  memtable_factory=SkipListFactory
  memtable_insert_with_hint_prefix_extractor=nullptr
  compression_per_level=kNoCompression:kNoCompression:kZSTD:kZSTD:kZSTD
  force_consistency_checks=true
  num_levels=5
  
[TableOptions/BlockBasedTable "default"]
  block_size_deviation=10
  checksum=kCRC32c
  index_shortening=kShortenSeparators
  whole_key_filtering=true
  data_block_index_type=kDataBlockBinarySearch
  index_type=kBinarySearch
  no_block_cache=false
  index_block_restart_interval=1
  data_block_hash_table_util_ratio=0.750000
  prepopulate_block_cache=kDisable
  pin_l0_filter_and_index_blocks_in_cache=false
  filter_policy=nullptr
  cache_index_and_filter_blocks_with_high_priority=true
  verify_compression=false
  block_restart_interval=16
  max_auto_readahead_size=262144
  hash_index_allow_collision=true
  flush_block_policy_factory=FlushBlockBySizePolicyFactory
  partition_filters=false
  cache_index_and_filter_blocks=false
  block_size=65536
  reserve_table_builder_memory=false
  metadata_block_size=4096
  block_align=false
  optimize_filters_for_memory=false
  format_version=5
  metadata_cache_options={unpartitioned_pinning=kFallback;partition_pinning=kFallback;top_level_index_pinning=kFallback;}
  read_amp_bytes_per_bit=0
  enable_index_compression=true
  pin_top_level_index_and_filter=true
  

[CFOptions "properties"]
  compression_opts={max_dict_buffer_bytes=0;enabled=false;max_dict_bytes=0;parallel_threads=1;zstd_max_train_bytes=0;level=32767;strategy=0;window_bits=-14;}
  bottommost_compression=kDisableCompressionOption
  enable_blob_garbage_collection=false
  blob_file_size=268435456
  sample_for_compression=0
  periodic_compaction_seconds=0
  ttl=2592000
  blob_garbage_collection_age_cutoff=0.250000
  compaction_options_universal={incremental=false;compression_size_percent=-1;allow_trivial_move=false;max_size_amplification_percent=200;max_merge_width=4294967295;stop_style=kCompactionStopStyleTotalSize;min_merge_width=2;size_ratio=1;}
  compression=kNoCompression
  max_sequential_skip_in_iterations=8
  max_bytes_for_level_multiplier_additional=1:1:1:1:1:1:1
  max_bytes_for_level_multiplier=10.000000
  min_blob_size=0
  check_flush_compaction_key_order=true
  disable_auto_compactions=false
  bottommost_compression_opts={max_dict_buffer_bytes=0;enabled=false;max_dict_bytes=0;parallel_threads=1;zstd_max_train_bytes=0;level=32767;strategy=0;window_bits=-14;}
  compaction_options_fifo={allow_compaction=false;age_for_warm=0;max_table_files_size=1073741824;}
  level0_file_num_compaction_trigger=4
  target_file_size_base=67108864
  soft_pending_compaction_bytes_limit=68719476736
  hard_pending_compaction_bytes_limit=274877906944
  level0_slowdown_writes_trigger=20
  blob_compression_type=kNoCompression
  level0_stop_writes_trigger=36
  enable_blob_files=false
  blob_garbage_collection_force_threshold=1.000000
  paranoid_file_checks=false
  prefix_extractor=nullptr
  max_write_buffer_number=2
  report_bg_io_stats=false
  memtable_prefix_bloom_size_ratio=0.000000
  target_file_size_multiplier=2
  arena_block_size=1048576
  blob_compaction_readahead_size=0
  inplace_update_num_locks=10000
  max_compaction_bytes=1677721600
  write_buffer_size=67108864
  memtable_huge_page_size=0
  max_successive_merges=0
  max_bytes_for_level_base=268435456
  memtable_whole_key_filtering=false
  compaction_pri=kMinOverlappingRatio
  compaction_filter_factory=nullptr
  comparator=leveldb.BytewiseComparator
  table_factory=BlockBasedTable
  inplace_update_support=false
  max_write_buffer_number_to_maintain=0
  bloom_locality=0
  compaction_filter=nullptr
  level_compaction_dynamic_level_bytes=false
  optimize_filters_for_hits=false
  merge_operator=nullptr
  max_write_buffer_size_to_maintain=0
  sst_partitioner_factory=nullptr
  compaction_style=kCompactionStyleLevel
  min_write_buffer_number_to_merge=1
  memtable_factory=SkipListFactory
  memtable_insert_with_hint_prefix_extractor=nullptr
  compression_per_level=kNoCompression:kNoCompression:kZSTD:kZSTD:kZSTD
  force_consistency_checks=true
  num_levels=5
  
[TableOptions/BlockBasedTable "properties"]
  block_size_deviation=10
  checksum=kCRC32c
  index_shortening=kShortenSeparators
  whole_key_filtering=true
  data_block_index_type=kDataBlockBinarySearch
  index_type=kBinarySearch
  no_block_cache=false
  index_block_restart_interval=1
  data_block_hash_table_util_ratio=0.750000
  prepopulate_block_cache=kDisable
  pin_l0_filter_and_index_blocks_in_cache=false
  filter_policy=nullptr
  cache_index_and_filter_blocks_with_high_priority=true
  verify_compression=false
  block_restart_interval=16
  max_auto_readahead_size=262144
  hash_index_allow_collision=true
  flush_block_policy_factory=FlushBlockBySizePolicyFactory
  partition_filters=false
  cache_index_and_filter_blocks=false
  block_size=65536
  reserve_table_builder_memory=false
  metadata_block_size=4096
  block_align=false
  optimize_filters_for_memory=false
  format_version=5
  metadata_cache_options={unpartitioned_pinning=kFallback;partition_pinning=kFallback;top_level_index_pinning=kFallback;}
  read_amp_bytes_per_bit=0
  enable_index_compression=true
  pin_top_level_index_and_filter=true
  
