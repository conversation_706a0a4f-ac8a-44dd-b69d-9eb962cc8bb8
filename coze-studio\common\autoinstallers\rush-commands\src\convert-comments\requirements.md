# 代码仓库中的中文备注转换为英文

## 简介

一段 JS 脚本，用于将代码仓库内的中文备注转换为英文。

## 执行逻辑：

1. 调用 git 命令，先查出本仓库下所有源码文件
2. 读取文件内容，分析文件中(可能包含 go 和ts、js、md 等代码，主要关注文本型文件)是否包含中文备注，是的话将文件目录加进待处理数组 tasks 中
3. 遍历 tasks 中的文件，提取出中文代码备注，调用 openai 接口，进行翻译，翻译后插入源位置
4. 所有文件均处理完毕后，输出报告

## 命令行参数：

命令名称：ai-translate
参数：

- `root`: 需要执行命令的目录
- `exts`: 需要进行处理的文件扩展名，支持数组，默认为空

## 技术约束

- 使用 ts 编写代码
- 调用 openai api 做翻译
- 使用 commander 实现命令行交互
- 优先使用 FP 编写代码
