import fs from "fs/promises";
import path from "path";
import { run } from "@mermaid-js/mermaid-cli";
import { fileURLToPath } from "url"; // 添加这行

/**
 * 处理 mermaid 图表生成的路由
 * @param {*} req
 * @param {*} res
 * @returns Promise<void>
 */
export async function generateChart(req, res) {
  try {
    // 获取请求体中的 mermaid 代码
    const { mermaidCode } = req.body;

    if (!mermaidCode) {
      return res.status(400).json({ error: "缺少 mermaid 代码" });
    }

    // 创建临时文件路径
    const currentDir = path.dirname(fileURLToPath(import.meta.url));
    const tempDir = path.join(currentDir, "..", "..", "temp");
    const tempInputPath = path.join(tempDir, `input-${Date.now()}.mmd`);
    const tempOutputPath = path.join(tempDir, `output-${Date.now()}.png`);

    // 确保临时目录存在
    await fs.mkdir(tempDir, { recursive: true });

    // 将 mermaid 代码写入临时文件
    await fs.writeFile(tempInputPath, mermaidCode);

    // 使用 mermaid-cli 生成图片
    await run(tempInputPath, tempOutputPath, {
      puppeteerConfig: {
        headless: "new",
        timeout: 30000,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--disable-gpu',
          '--window-size=3840,2160'
        ],
        defaultViewport: {
          width: 3840,
          height: 2160,
          deviceScaleFactor: 2
        }
      },
      quiet: true,
      backgroundColor: 'white',
      scale: 4.0,
      width: 2400,
      height: 1600,
      cssFile: path.join(currentDir, '..', '..', 'config', 'mermaid.css')
    });

    // 读取生成的图片
    const imageBuffer = await fs.readFile(tempOutputPath);

    // 清理临时文件
    await Promise.all([fs.unlink(tempInputPath), fs.unlink(tempOutputPath)]);

    // 设置响应头并返回图片
    res.setHeader("Content-Type", "image/png");
    res.send(imageBuffer);
  } catch (error) {
    console.error("生成图表时出错:", error);
    res.status(500).json({ error: "生成图表失败" });
  }
}

export default { generateChart };
