2025/08/04-13:21:05.741066 41 RocksDB version: 6.29.5
2025/08/04-13:21:05.741661 41 Git sha 0
2025/08/04-13:21:05.741666 41 Compile date 2024-11-15 11:22:58
2025/08/04-13:21:05.741675 41 DB SUMMARY
2025/08/04-13:21:05.741676 41 DB Session ID:  FVTE2ZW2IVPDHMNW2XRD
2025/08/04-13:21:05.747677 41 CURRENT file:  CURRENT
2025/08/04-13:21:05.747687 41 IDENTITY file:  IDENTITY
2025/08/04-13:21:05.748455 41 MANIFEST file:  MANIFEST-000030 size: 172 Bytes
2025/08/04-13:21:05.748464 41 SST files in /var/lib/milvus/rdb_data_meta_kv dir, Total Num: 1, files: 000009.sst 
2025/08/04-13:21:05.748470 41 Write Ahead Log file in /var/lib/milvus/rdb_data_meta_kv: 000011.log size: 0 ; 000016.log size: 0 ; 000021.log size: 0 ; 000026.log size: 0 ; 000031.log size: 0 ; 
2025/08/04-13:21:05.748473 41                         Options.error_if_exists: 0
2025/08/04-13:21:05.748474 41                       Options.create_if_missing: 1
2025/08/04-13:21:05.748474 41                         Options.paranoid_checks: 1
2025/08/04-13:21:05.748475 41             Options.flush_verify_memtable_count: 1
2025/08/04-13:21:05.748476 41                               Options.track_and_verify_wals_in_manifest: 0
2025/08/04-13:21:05.748476 41                                     Options.env: 0x7f37f67bbd00
2025/08/04-13:21:05.748477 41                                      Options.fs: PosixFileSystem
2025/08/04-13:21:05.748478 41                                Options.info_log: 0x7f3719e90050
2025/08/04-13:21:05.748479 41                Options.max_file_opening_threads: 16
2025/08/04-13:21:05.748479 41                              Options.statistics: (nil)
2025/08/04-13:21:05.748480 41                               Options.use_fsync: 0
2025/08/04-13:21:05.748480 41                       Options.max_log_file_size: 0
2025/08/04-13:21:05.748481 41                  Options.max_manifest_file_size: 1073741824
2025/08/04-13:21:05.748482 41                   Options.log_file_time_to_roll: 0
2025/08/04-13:21:05.748482 41                       Options.keep_log_file_num: 1000
2025/08/04-13:21:05.748483 41                    Options.recycle_log_file_num: 0
2025/08/04-13:21:05.748484 41                         Options.allow_fallocate: 1
2025/08/04-13:21:05.748484 41                        Options.allow_mmap_reads: 0
2025/08/04-13:21:05.748485 41                       Options.allow_mmap_writes: 0
2025/08/04-13:21:05.748485 41                        Options.use_direct_reads: 0
2025/08/04-13:21:05.748486 41                        Options.use_direct_io_for_flush_and_compaction: 0
2025/08/04-13:21:05.748486 41          Options.create_missing_column_families: 0
2025/08/04-13:21:05.748487 41                              Options.db_log_dir: 
2025/08/04-13:21:05.748488 41                                 Options.wal_dir: 
2025/08/04-13:21:05.748488 41                Options.table_cache_numshardbits: 6
2025/08/04-13:21:05.748489 41                         Options.WAL_ttl_seconds: 0
2025/08/04-13:21:05.748489 41                       Options.WAL_size_limit_MB: 0
2025/08/04-13:21:05.748490 41                        Options.max_write_batch_group_size_bytes: 1048576
2025/08/04-13:21:05.748490 41             Options.manifest_preallocation_size: 4194304
2025/08/04-13:21:05.748491 41                     Options.is_fd_close_on_exec: 1
2025/08/04-13:21:05.748492 41                   Options.advise_random_on_open: 1
2025/08/04-13:21:05.748492 41                   Options.experimental_mempurge_threshold: 0.000000
2025/08/04-13:21:05.748502 41                    Options.db_write_buffer_size: 0
2025/08/04-13:21:05.748502 41                    Options.write_buffer_manager: 0x7f371c8400a0
2025/08/04-13:21:05.748503 41         Options.access_hint_on_compaction_start: 1
2025/08/04-13:21:05.748504 41  Options.new_table_reader_for_compaction_inputs: 0
2025/08/04-13:21:05.748504 41           Options.random_access_max_buffer_size: 1048576
2025/08/04-13:21:05.748505 41                      Options.use_adaptive_mutex: 0
2025/08/04-13:21:05.748505 41                            Options.rate_limiter: (nil)
2025/08/04-13:21:05.748509 41     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/08/04-13:21:05.748993 41                       Options.wal_recovery_mode: 2
2025/08/04-13:21:05.748995 41                  Options.enable_thread_tracking: 0
2025/08/04-13:21:05.748996 41                  Options.enable_pipelined_write: 0
2025/08/04-13:21:05.748997 41                  Options.unordered_write: 0
2025/08/04-13:21:05.748997 41         Options.allow_concurrent_memtable_write: 1
2025/08/04-13:21:05.748998 41      Options.enable_write_thread_adaptive_yield: 1
2025/08/04-13:21:05.748998 41             Options.write_thread_max_yield_usec: 100
2025/08/04-13:21:05.748999 41            Options.write_thread_slow_yield_usec: 3
2025/08/04-13:21:05.749000 41                               Options.row_cache: None
2025/08/04-13:21:05.749001 41                              Options.wal_filter: None
2025/08/04-13:21:05.749001 41             Options.avoid_flush_during_recovery: 0
2025/08/04-13:21:05.749002 41             Options.allow_ingest_behind: 0
2025/08/04-13:21:05.749003 41             Options.preserve_deletes: 0
2025/08/04-13:21:05.749003 41             Options.two_write_queues: 0
2025/08/04-13:21:05.749004 41             Options.manual_wal_flush: 0
2025/08/04-13:21:05.749004 41             Options.atomic_flush: 0
2025/08/04-13:21:05.749005 41             Options.avoid_unnecessary_blocking_io: 0
2025/08/04-13:21:05.749005 41                 Options.persist_stats_to_disk: 0
2025/08/04-13:21:05.749006 41                 Options.write_dbid_to_manifest: 0
2025/08/04-13:21:05.749006 41                 Options.log_readahead_size: 0
2025/08/04-13:21:05.749007 41                 Options.file_checksum_gen_factory: Unknown
2025/08/04-13:21:05.749008 41                 Options.best_efforts_recovery: 0
2025/08/04-13:21:05.749008 41                Options.max_bgerror_resume_count: 2147483647
2025/08/04-13:21:05.749009 41            Options.bgerror_resume_retry_interval: 1000000
2025/08/04-13:21:05.749010 41             Options.allow_data_in_errors: 0
2025/08/04-13:21:05.749010 41             Options.db_host_id: __hostname__
2025/08/04-13:21:05.749012 41             Options.max_background_jobs: 2
2025/08/04-13:21:05.749013 41             Options.max_background_compactions: -1
2025/08/04-13:21:05.749013 41             Options.max_subcompactions: 1
2025/08/04-13:21:05.749014 41             Options.avoid_flush_during_shutdown: 0
2025/08/04-13:21:05.749014 41           Options.writable_file_max_buffer_size: 1048576
2025/08/04-13:21:05.749015 41             Options.delayed_write_rate : 16777216
2025/08/04-13:21:05.749016 41             Options.max_total_wal_size: 0
2025/08/04-13:21:05.749016 41             Options.delete_obsolete_files_period_micros: 21600000000
2025/08/04-13:21:05.749018 41                   Options.stats_dump_period_sec: 600
2025/08/04-13:21:05.749020 41                 Options.stats_persist_period_sec: 600
2025/08/04-13:21:05.749021 41                 Options.stats_history_buffer_size: 1048576
2025/08/04-13:21:05.749021 41                          Options.max_open_files: -1
2025/08/04-13:21:05.749022 41                          Options.bytes_per_sync: 0
2025/08/04-13:21:05.749022 41                      Options.wal_bytes_per_sync: 0
2025/08/04-13:21:05.749023 41                   Options.strict_bytes_per_sync: 0
2025/08/04-13:21:05.749024 41       Options.compaction_readahead_size: 0
2025/08/04-13:21:05.749024 41                  Options.max_background_flushes: 1
2025/08/04-13:21:05.749025 41 Compression algorithms supported:
2025/08/04-13:21:05.749027 41 	kZSTD supported: 1
2025/08/04-13:21:05.749028 41 	kXpressCompression supported: 0
2025/08/04-13:21:05.749028 41 	kBZip2Compression supported: 0
2025/08/04-13:21:05.749029 41 	kZSTDNotFinalCompression supported: 1
2025/08/04-13:21:05.749030 41 	kLZ4Compression supported: 0
2025/08/04-13:21:05.749031 41 	kZlibCompression supported: 0
2025/08/04-13:21:05.749031 41 	kLZ4HCCompression supported: 0
2025/08/04-13:21:05.749032 41 	kSnappyCompression supported: 0
2025/08/04-13:21:05.749035 41 Fast CRC32 supported: Not supported on x86
2025/08/04-13:21:05.754801 41 [db/version_set.cc:4888] Recovering from manifest file: /var/lib/milvus/rdb_data_meta_kv/MANIFEST-000030
2025/08/04-13:21:05.756586 41 [db/column_family.cc:605] --------------- Options for column family [default]:
2025/08/04-13:21:05.756593 41               Options.comparator: leveldb.BytewiseComparator
2025/08/04-13:21:05.756594 41           Options.merge_operator: None
2025/08/04-13:21:05.756597 41        Options.compaction_filter: None
2025/08/04-13:21:05.756598 41        Options.compaction_filter_factory: None
2025/08/04-13:21:05.756598 41  Options.sst_partitioner_factory: None
2025/08/04-13:21:05.756599 41         Options.memtable_factory: SkipListFactory
2025/08/04-13:21:05.756600 41            Options.table_factory: BlockBasedTable
2025/08/04-13:21:05.756626 41            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f371c9000c0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0x7f371c840010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 1001872834
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: (nil)
  persistent_cache: (nil)
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2025/08/04-13:21:05.756628 41        Options.write_buffer_size: 67108864
2025/08/04-13:21:05.756629 41  Options.max_write_buffer_number: 2
2025/08/04-13:21:05.756631 41        Options.compression[0]: NoCompression
2025/08/04-13:21:05.756632 41        Options.compression[1]: NoCompression
2025/08/04-13:21:05.756635 41        Options.compression[2]: ZSTD
2025/08/04-13:21:05.756636 41        Options.compression[3]: ZSTD
2025/08/04-13:21:05.756637 41        Options.compression[4]: ZSTD
2025/08/04-13:21:05.756637 41                  Options.bottommost_compression: Disabled
2025/08/04-13:21:05.756638 41       Options.prefix_extractor: nullptr
2025/08/04-13:21:05.756639 41   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/04-13:21:05.756639 41             Options.num_levels: 5
2025/08/04-13:21:05.756640 41        Options.min_write_buffer_number_to_merge: 1
2025/08/04-13:21:05.756640 41     Options.max_write_buffer_number_to_maintain: 0
2025/08/04-13:21:05.756641 41     Options.max_write_buffer_size_to_maintain: 0
2025/08/04-13:21:05.756642 41            Options.bottommost_compression_opts.window_bits: -14
2025/08/04-13:21:05.756642 41                  Options.bottommost_compression_opts.level: 32767
2025/08/04-13:21:05.756643 41               Options.bottommost_compression_opts.strategy: 0
2025/08/04-13:21:05.756643 41         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/04-13:21:05.756644 41         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/04-13:21:05.756645 41         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/04-13:21:05.756645 41                  Options.bottommost_compression_opts.enabled: false
2025/08/04-13:21:05.756646 41         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/04-13:21:05.756648 41            Options.compression_opts.window_bits: -14
2025/08/04-13:21:05.756649 41                  Options.compression_opts.level: 32767
2025/08/04-13:21:05.756650 41               Options.compression_opts.strategy: 0
2025/08/04-13:21:05.756651 41         Options.compression_opts.max_dict_bytes: 0
2025/08/04-13:21:05.756651 41         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/04-13:21:05.756830 41         Options.compression_opts.parallel_threads: 1
2025/08/04-13:21:05.756832 41                  Options.compression_opts.enabled: false
2025/08/04-13:21:05.756833 41         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/04-13:21:05.756834 41      Options.level0_file_num_compaction_trigger: 4
2025/08/04-13:21:05.756835 41          Options.level0_slowdown_writes_trigger: 20
2025/08/04-13:21:05.756836 41              Options.level0_stop_writes_trigger: 36
2025/08/04-13:21:05.756836 41                   Options.target_file_size_base: 67108864
2025/08/04-13:21:05.756839 41             Options.target_file_size_multiplier: 2
2025/08/04-13:21:05.756840 41                Options.max_bytes_for_level_base: 268435456
2025/08/04-13:21:05.756841 41 Options.level_compaction_dynamic_level_bytes: 0
2025/08/04-13:21:05.756841 41          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/04-13:21:05.756843 41 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/04-13:21:05.756844 41 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/04-13:21:05.756844 41 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/04-13:21:05.756845 41 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/04-13:21:05.756846 41 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/04-13:21:05.756846 41 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/04-13:21:05.756847 41 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/04-13:21:05.756847 41       Options.max_sequential_skip_in_iterations: 8
2025/08/04-13:21:05.756848 41                    Options.max_compaction_bytes: 1677721600
2025/08/04-13:21:05.756849 41                        Options.arena_block_size: 1048576
2025/08/04-13:21:05.756849 41   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/04-13:21:05.756850 41   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/04-13:21:05.756850 41       Options.rate_limit_delay_max_milliseconds: 100
2025/08/04-13:21:05.756851 41                Options.disable_auto_compactions: 0
2025/08/04-13:21:05.756853 41                        Options.compaction_style: kCompactionStyleLevel
2025/08/04-13:21:05.756854 41                          Options.compaction_pri: kMinOverlappingRatio
2025/08/04-13:21:05.756855 41 Options.compaction_options_universal.size_ratio: 1
2025/08/04-13:21:05.756855 41 Options.compaction_options_universal.min_merge_width: 2
2025/08/04-13:21:05.756856 41 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/04-13:21:05.756857 41 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/04-13:21:05.756857 41 Options.compaction_options_universal.compression_size_percent: -1
2025/08/04-13:21:05.756858 41 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/04-13:21:05.756859 41 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/04-13:21:05.756859 41 Options.compaction_options_fifo.allow_compaction: 0
2025/08/04-13:21:05.756864 41                   Options.table_properties_collectors: 
2025/08/04-13:21:05.756865 41                   Options.inplace_update_support: 0
2025/08/04-13:21:05.756865 41                 Options.inplace_update_num_locks: 10000
2025/08/04-13:21:05.756866 41               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/04-13:21:05.756867 41               Options.memtable_whole_key_filtering: 0
2025/08/04-13:21:05.756868 41   Options.memtable_huge_page_size: 0
2025/08/04-13:21:05.756868 41                           Options.bloom_locality: 0
2025/08/04-13:21:05.756869 41                    Options.max_successive_merges: 0
2025/08/04-13:21:05.756869 41                Options.optimize_filters_for_hits: 0
2025/08/04-13:21:05.756870 41                Options.paranoid_file_checks: 0
2025/08/04-13:21:05.756871 41                Options.force_consistency_checks: 1
2025/08/04-13:21:05.756871 41                Options.report_bg_io_stats: 0
2025/08/04-13:21:05.756872 41                               Options.ttl: 2592000
2025/08/04-13:21:05.757068 41          Options.periodic_compaction_seconds: 0
2025/08/04-13:21:05.757070 41                       Options.enable_blob_files: false
2025/08/04-13:21:05.757073 41                           Options.min_blob_size: 0
2025/08/04-13:21:05.757074 41                          Options.blob_file_size: 268435456
2025/08/04-13:21:05.757075 41                   Options.blob_compression_type: NoCompression
2025/08/04-13:21:05.757075 41          Options.enable_blob_garbage_collection: false
2025/08/04-13:21:05.757076 41      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/04-13:21:05.757077 41 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/04-13:21:05.757078 41          Options.blob_compaction_readahead_size: 0
2025/08/04-13:21:05.762482 41 [db/version_set.cc:4928] Recovered from manifest file:/var/lib/milvus/rdb_data_meta_kv/MANIFEST-000030 succeeded,manifest_file_number is 30, next_file_number is 32, last_sequence is 34, log_number is 27,prev_log_number is 0,max_column_family is 0,min_log_number_to_keep is 0
2025/08/04-13:21:05.762493 41 [db/version_set.cc:4943] Column family [default] (ID 0), log number is 27
2025/08/04-13:21:05.766033 41 [db/version_set.cc:4409] Creating manifest 34
2025/08/04-13:21:05.788095 41 EVENT_LOG_v1 {"time_micros": 1754313665788068, "job": 1, "event": "recovery_started", "wal_files": [11, 16, 21, 26, 31]}
2025/08/04-13:21:05.788111 41 [db/db_impl/db_impl_open.cc:874] Skipping log #11 since it is older than min log to keep #27
2025/08/04-13:21:05.788114 41 [db/db_impl/db_impl_open.cc:874] Skipping log #16 since it is older than min log to keep #27
2025/08/04-13:21:05.788116 41 [db/db_impl/db_impl_open.cc:874] Skipping log #21 since it is older than min log to keep #27
2025/08/04-13:21:05.788117 41 [db/db_impl/db_impl_open.cc:874] Skipping log #26 since it is older than min log to keep #27
2025/08/04-13:21:05.788124 41 [db/db_impl/db_impl_open.cc:888] Recovering log #31 mode 2
2025/08/04-13:21:05.789825 41 [db/version_set.cc:4409] Creating manifest 35
2025/08/04-13:21:05.814188 41 EVENT_LOG_v1 {"time_micros": 1754313665814177, "job": 1, "event": "recovery_finished"}
2025/08/04-13:21:05.838679 41 [db/db_impl/db_impl_open.cc:1820] SstFileManager instance 0x7f3719880000
2025/08/04-13:21:05.840584 41 DB pointer 0x7f3719e20000
2025/08/04-13:21:05.841182 64 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/04-13:21:05.841198 64 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 0.1 total, 0.1 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    1.69 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      1/0    1.69 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 0.1 total, 0.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f371c840010#8 capacity: 955.46 MB collections: 1 last_copies: 0 last_secs: 6.8e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/08/04-13:31:05.841486 64 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/04-13:31:05.850919 64 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 600.1 total, 600.0 interval
Cumulative writes: 1770 writes, 1770 keys, 1770 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 1770 writes, 0 syncs, 1770.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 1770 writes, 1770 keys, 1770 commit groups, 1.0 writes per commit group, ingest: 0.09 MB, 0.00 MB/s
Interval WAL: 1770 writes, 0 syncs, 1770.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    1.69 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      1/0    1.69 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 600.1 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f371c840010#8 capacity: 955.46 MB collections: 2 last_copies: 0 last_secs: 6.7e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(1,0.83 KB,8.46415e-05%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
