# coding: utf-8

"""
    privatelink

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribePrivateLinkGatewaysRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'page_number': 'int',
        'page_size': 'int',
        'private_link_gateway_ids': 'list[str]',
        'private_link_gateway_name': 'str',
        'vpc_id': 'str',
        'zone_id': 'str'
    }

    attribute_map = {
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'private_link_gateway_ids': 'PrivateLinkGatewayIds',
        'private_link_gateway_name': 'PrivateLinkGatewayName',
        'vpc_id': 'VpcId',
        'zone_id': 'ZoneId'
    }

    def __init__(self, page_number=None, page_size=None, private_link_gateway_ids=None, private_link_gateway_name=None, vpc_id=None, zone_id=None, _configuration=None):  # noqa: E501
        """DescribePrivateLinkGatewaysRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._page_number = None
        self._page_size = None
        self._private_link_gateway_ids = None
        self._private_link_gateway_name = None
        self._vpc_id = None
        self._zone_id = None
        self.discriminator = None

        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if private_link_gateway_ids is not None:
            self.private_link_gateway_ids = private_link_gateway_ids
        if private_link_gateway_name is not None:
            self.private_link_gateway_name = private_link_gateway_name
        if vpc_id is not None:
            self.vpc_id = vpc_id
        if zone_id is not None:
            self.zone_id = zone_id

    @property
    def page_number(self):
        """Gets the page_number of this DescribePrivateLinkGatewaysRequest.  # noqa: E501


        :return: The page_number of this DescribePrivateLinkGatewaysRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this DescribePrivateLinkGatewaysRequest.


        :param page_number: The page_number of this DescribePrivateLinkGatewaysRequest.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this DescribePrivateLinkGatewaysRequest.  # noqa: E501


        :return: The page_size of this DescribePrivateLinkGatewaysRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this DescribePrivateLinkGatewaysRequest.


        :param page_size: The page_size of this DescribePrivateLinkGatewaysRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def private_link_gateway_ids(self):
        """Gets the private_link_gateway_ids of this DescribePrivateLinkGatewaysRequest.  # noqa: E501


        :return: The private_link_gateway_ids of this DescribePrivateLinkGatewaysRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._private_link_gateway_ids

    @private_link_gateway_ids.setter
    def private_link_gateway_ids(self, private_link_gateway_ids):
        """Sets the private_link_gateway_ids of this DescribePrivateLinkGatewaysRequest.


        :param private_link_gateway_ids: The private_link_gateway_ids of this DescribePrivateLinkGatewaysRequest.  # noqa: E501
        :type: list[str]
        """

        self._private_link_gateway_ids = private_link_gateway_ids

    @property
    def private_link_gateway_name(self):
        """Gets the private_link_gateway_name of this DescribePrivateLinkGatewaysRequest.  # noqa: E501


        :return: The private_link_gateway_name of this DescribePrivateLinkGatewaysRequest.  # noqa: E501
        :rtype: str
        """
        return self._private_link_gateway_name

    @private_link_gateway_name.setter
    def private_link_gateway_name(self, private_link_gateway_name):
        """Sets the private_link_gateway_name of this DescribePrivateLinkGatewaysRequest.


        :param private_link_gateway_name: The private_link_gateway_name of this DescribePrivateLinkGatewaysRequest.  # noqa: E501
        :type: str
        """

        self._private_link_gateway_name = private_link_gateway_name

    @property
    def vpc_id(self):
        """Gets the vpc_id of this DescribePrivateLinkGatewaysRequest.  # noqa: E501


        :return: The vpc_id of this DescribePrivateLinkGatewaysRequest.  # noqa: E501
        :rtype: str
        """
        return self._vpc_id

    @vpc_id.setter
    def vpc_id(self, vpc_id):
        """Sets the vpc_id of this DescribePrivateLinkGatewaysRequest.


        :param vpc_id: The vpc_id of this DescribePrivateLinkGatewaysRequest.  # noqa: E501
        :type: str
        """

        self._vpc_id = vpc_id

    @property
    def zone_id(self):
        """Gets the zone_id of this DescribePrivateLinkGatewaysRequest.  # noqa: E501


        :return: The zone_id of this DescribePrivateLinkGatewaysRequest.  # noqa: E501
        :rtype: str
        """
        return self._zone_id

    @zone_id.setter
    def zone_id(self, zone_id):
        """Sets the zone_id of this DescribePrivateLinkGatewaysRequest.


        :param zone_id: The zone_id of this DescribePrivateLinkGatewaysRequest.  # noqa: E501
        :type: str
        """

        self._zone_id = zone_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribePrivateLinkGatewaysRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribePrivateLinkGatewaysRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribePrivateLinkGatewaysRequest):
            return True

        return self.to_dict() != other.to_dict()
