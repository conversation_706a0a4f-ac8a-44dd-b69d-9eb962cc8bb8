# 🚀 Paper Services K8s 快速上手指南

## 📋 服务列表

| 服务名称 | 技术栈 | 端口 | 功能描述 | 外部访问 |
|---------|--------|------|----------|----------|
| lunwen-generate-ui | Next.js | 3000 | 论文生成前端界面 | :30000 |
| dify-web | Next.js | 3000 | Dify AI 平台前端 | :30001 |
| paper-editor-api | Go | 8890 | 主 API 服务 | 内部 |
| dify-api | Python | 5001 | Dify AI API 服务 | 内部 |
| paper-node-service | Node.js | 9529 | Node.js 工具服务 | 内部 |
| paper-py-service | Python | 9528 | Python 分析服务 | 内部 |
| postgres | PostgreSQL | 5432 | 数据库 | 内部 |
| redis | Redis | 6379 | 缓存 | 内部 |

## 🎯 一键启动

```powershell
# 进入 k8s 目录
cd k8s

# 快速启动（推荐）
.\quick-start.ps1
```

## 📖 详细步骤

### 1. 环境检查

```powershell
# 检查 Docker
docker --version
docker ps

# 检查 Kubernetes
kubectl version --client
kubectl cluster-info
```

### 2. 构建镜像

```powershell
# 构建所有服务的 Docker 镜像
.\build-images.ps1

# 查看构建的镜像
docker images | findstr "paper-\|lunwen-\|dify"
```

### 3. 部署服务

```powershell
# 部署到 Kubernetes
.\deploy.ps1

# 查看部署状态
kubectl get pods -n paper-services -w
```

## 🌐 访问服务

启动完成后，通过浏览器访问：

- **📝 论文生成系统**: http://localhost:30000
- **🤖 Dify AI 平台**: http://localhost:30001

## 🔧 服务间通信示例

### 前端调用 API
```javascript
// lunwen-generate-ui 调用 paper-editor-api
const response = await fetch('http://paper-editor-api:8890/api/v1/files');

// 调用 Dify AI
const aiResponse = await fetch('http://dify-api:5001/v1/chat-messages');
```

### API 服务间调用
```go
// paper-editor-api 调用 paper-node-service
resp, err := http.Get("http://paper-node-service:9529/api/mermaid")

// 调用 paper-py-service
resp, err := http.Get("http://paper-py-service:9528/generate/word")
```

## 🐛 故障排除

### 常见问题及解决方案

1. **Pod 一直处于 Pending 状态**
   ```powershell
   kubectl describe pod <pod-name> -n paper-services
   # 通常是资源不足或镜像拉取问题
   ```

2. **服务无法访问**
   ```powershell
   # 检查服务端点
   kubectl get endpoints -n paper-services
   
   # 端口转发测试
   kubectl port-forward svc/paper-editor-api 8890:8890 -n paper-services
   ```

3. **镜像拉取失败**
   ```powershell
   # 重新构建镜像
   .\build-images.ps1
   
   # 检查镜像是否存在
   docker images | findstr paper-
   ```

### 调试命令

```powershell
# 查看所有资源
kubectl get all -n paper-services

# 查看 Pod 详细信息
kubectl describe pod <pod-name> -n paper-services

# 查看实时日志
kubectl logs -f deployment/paper-editor-api-deployment -n paper-services

# 进入容器调试
kubectl exec -it <pod-name> -n paper-services -- /bin/sh
```

## 🧹 清理

```powershell
# 清理部署（保留数据）
.\cleanup.ps1

# 完全清理
.\cleanup.ps1 -All
```

## 💡 开发提示

### 快速重新部署单个服务
```powershell
# 重新构建并部署 paper-editor-api
docker build -t paper-editor-api:latest ../paper-editor-api
kubectl rollout restart deployment/paper-editor-api-deployment -n paper-services
```

### 查看服务健康状态
```powershell
# 检查所有 Pod 状态
kubectl get pods -n paper-services

# 检查服务可达性
kubectl run test-pod --image=busybox -n paper-services --rm -it -- /bin/sh
# 在容器内测试: wget -qO- http://paper-editor-api:8890/health
```
