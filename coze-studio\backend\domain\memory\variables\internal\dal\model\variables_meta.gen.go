// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import "github.com/coze-dev/coze-studio/backend/domain/memory/variables/entity"

const TableNameVariablesMeta = "variables_meta"

// VariablesMeta KV Memory meta
type VariablesMeta struct {
	ID           int64                  `gorm:"column:id;primaryKey;comment:id" json:"id"`                                                              // id
	CreatorID    int64                  `gorm:"column:creator_id;not null;comment:creator id" json:"creator_id"`                                        // creator id
	BizType      int32                  `gorm:"column:biz_type;not null;comment:1 for agent，2 for app" json:"biz_type"`                                 // 1 for agent，2 for app
	BizID        string                 `gorm:"column:biz_id;not null;comment:1 for agent_id，2 for app_id" json:"biz_id"`                               // 1 for agent_id，2 for app_id
	VariableList []*entity.VariableMeta `gorm:"column:variable_list;comment:JSON data for variable configuration;serializer:json" json:"variable_list"` // JSON data for variable configuration
	CreatedAt    int64                  `gorm:"column:created_at;not null;autoCreateTime:milli;comment:Create Time in Milliseconds" json:"created_at"`  // Create Time in Milliseconds
	UpdatedAt    int64                  `gorm:"column:updated_at;not null;autoUpdateTime:milli;comment:Update Time in Milliseconds" json:"updated_at"`  // Update Time in Milliseconds
	Version      string                 `gorm:"column:version;not null;comment:Project version, empty represents draft status" json:"version"`          // Project version, empty represents draft status
}

// TableName VariablesMeta's table name
func (*VariablesMeta) TableName() string {
	return TableNameVariablesMeta
}
