# coding: utf-8

"""
    privatelink

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateVpcEndpointServiceRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'auto_accept_enabled': 'bool',
        'client_token': 'str',
        'description': 'str',
        'ip_address_versions': 'list[str]',
        'payer': 'str',
        'private_dns_enabled': 'bool',
        'private_dns_name': 'str',
        'private_dns_type': 'str',
        'project_name': 'str',
        'resources': 'list[ResourceForCreateVpcEndpointServiceInput]',
        'service_name_managed': 'str',
        'service_name_suffix': 'str',
        'service_owner': 'str',
        'service_resource_type': 'str',
        'service_type': 'str',
        'tags': 'list[TagForCreateVpcEndpointServiceInput]',
        'wildcard_domain_enabled': 'bool'
    }

    attribute_map = {
        'auto_accept_enabled': 'AutoAcceptEnabled',
        'client_token': 'ClientToken',
        'description': 'Description',
        'ip_address_versions': 'IpAddressVersions',
        'payer': 'Payer',
        'private_dns_enabled': 'PrivateDNSEnabled',
        'private_dns_name': 'PrivateDNSName',
        'private_dns_type': 'PrivateDNSType',
        'project_name': 'ProjectName',
        'resources': 'Resources',
        'service_name_managed': 'ServiceNameManaged',
        'service_name_suffix': 'ServiceNameSuffix',
        'service_owner': 'ServiceOwner',
        'service_resource_type': 'ServiceResourceType',
        'service_type': 'ServiceType',
        'tags': 'Tags',
        'wildcard_domain_enabled': 'WildcardDomainEnabled'
    }

    def __init__(self, auto_accept_enabled=None, client_token=None, description=None, ip_address_versions=None, payer=None, private_dns_enabled=None, private_dns_name=None, private_dns_type=None, project_name=None, resources=None, service_name_managed=None, service_name_suffix=None, service_owner=None, service_resource_type=None, service_type=None, tags=None, wildcard_domain_enabled=None, _configuration=None):  # noqa: E501
        """CreateVpcEndpointServiceRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._auto_accept_enabled = None
        self._client_token = None
        self._description = None
        self._ip_address_versions = None
        self._payer = None
        self._private_dns_enabled = None
        self._private_dns_name = None
        self._private_dns_type = None
        self._project_name = None
        self._resources = None
        self._service_name_managed = None
        self._service_name_suffix = None
        self._service_owner = None
        self._service_resource_type = None
        self._service_type = None
        self._tags = None
        self._wildcard_domain_enabled = None
        self.discriminator = None

        if auto_accept_enabled is not None:
            self.auto_accept_enabled = auto_accept_enabled
        if client_token is not None:
            self.client_token = client_token
        if description is not None:
            self.description = description
        if ip_address_versions is not None:
            self.ip_address_versions = ip_address_versions
        if payer is not None:
            self.payer = payer
        if private_dns_enabled is not None:
            self.private_dns_enabled = private_dns_enabled
        if private_dns_name is not None:
            self.private_dns_name = private_dns_name
        if private_dns_type is not None:
            self.private_dns_type = private_dns_type
        if project_name is not None:
            self.project_name = project_name
        if resources is not None:
            self.resources = resources
        if service_name_managed is not None:
            self.service_name_managed = service_name_managed
        if service_name_suffix is not None:
            self.service_name_suffix = service_name_suffix
        if service_owner is not None:
            self.service_owner = service_owner
        if service_resource_type is not None:
            self.service_resource_type = service_resource_type
        if service_type is not None:
            self.service_type = service_type
        if tags is not None:
            self.tags = tags
        if wildcard_domain_enabled is not None:
            self.wildcard_domain_enabled = wildcard_domain_enabled

    @property
    def auto_accept_enabled(self):
        """Gets the auto_accept_enabled of this CreateVpcEndpointServiceRequest.  # noqa: E501


        :return: The auto_accept_enabled of this CreateVpcEndpointServiceRequest.  # noqa: E501
        :rtype: bool
        """
        return self._auto_accept_enabled

    @auto_accept_enabled.setter
    def auto_accept_enabled(self, auto_accept_enabled):
        """Sets the auto_accept_enabled of this CreateVpcEndpointServiceRequest.


        :param auto_accept_enabled: The auto_accept_enabled of this CreateVpcEndpointServiceRequest.  # noqa: E501
        :type: bool
        """

        self._auto_accept_enabled = auto_accept_enabled

    @property
    def client_token(self):
        """Gets the client_token of this CreateVpcEndpointServiceRequest.  # noqa: E501


        :return: The client_token of this CreateVpcEndpointServiceRequest.  # noqa: E501
        :rtype: str
        """
        return self._client_token

    @client_token.setter
    def client_token(self, client_token):
        """Sets the client_token of this CreateVpcEndpointServiceRequest.


        :param client_token: The client_token of this CreateVpcEndpointServiceRequest.  # noqa: E501
        :type: str
        """

        self._client_token = client_token

    @property
    def description(self):
        """Gets the description of this CreateVpcEndpointServiceRequest.  # noqa: E501


        :return: The description of this CreateVpcEndpointServiceRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CreateVpcEndpointServiceRequest.


        :param description: The description of this CreateVpcEndpointServiceRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def ip_address_versions(self):
        """Gets the ip_address_versions of this CreateVpcEndpointServiceRequest.  # noqa: E501


        :return: The ip_address_versions of this CreateVpcEndpointServiceRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._ip_address_versions

    @ip_address_versions.setter
    def ip_address_versions(self, ip_address_versions):
        """Sets the ip_address_versions of this CreateVpcEndpointServiceRequest.


        :param ip_address_versions: The ip_address_versions of this CreateVpcEndpointServiceRequest.  # noqa: E501
        :type: list[str]
        """

        self._ip_address_versions = ip_address_versions

    @property
    def payer(self):
        """Gets the payer of this CreateVpcEndpointServiceRequest.  # noqa: E501


        :return: The payer of this CreateVpcEndpointServiceRequest.  # noqa: E501
        :rtype: str
        """
        return self._payer

    @payer.setter
    def payer(self, payer):
        """Sets the payer of this CreateVpcEndpointServiceRequest.


        :param payer: The payer of this CreateVpcEndpointServiceRequest.  # noqa: E501
        :type: str
        """

        self._payer = payer

    @property
    def private_dns_enabled(self):
        """Gets the private_dns_enabled of this CreateVpcEndpointServiceRequest.  # noqa: E501


        :return: The private_dns_enabled of this CreateVpcEndpointServiceRequest.  # noqa: E501
        :rtype: bool
        """
        return self._private_dns_enabled

    @private_dns_enabled.setter
    def private_dns_enabled(self, private_dns_enabled):
        """Sets the private_dns_enabled of this CreateVpcEndpointServiceRequest.


        :param private_dns_enabled: The private_dns_enabled of this CreateVpcEndpointServiceRequest.  # noqa: E501
        :type: bool
        """

        self._private_dns_enabled = private_dns_enabled

    @property
    def private_dns_name(self):
        """Gets the private_dns_name of this CreateVpcEndpointServiceRequest.  # noqa: E501


        :return: The private_dns_name of this CreateVpcEndpointServiceRequest.  # noqa: E501
        :rtype: str
        """
        return self._private_dns_name

    @private_dns_name.setter
    def private_dns_name(self, private_dns_name):
        """Sets the private_dns_name of this CreateVpcEndpointServiceRequest.


        :param private_dns_name: The private_dns_name of this CreateVpcEndpointServiceRequest.  # noqa: E501
        :type: str
        """

        self._private_dns_name = private_dns_name

    @property
    def private_dns_type(self):
        """Gets the private_dns_type of this CreateVpcEndpointServiceRequest.  # noqa: E501


        :return: The private_dns_type of this CreateVpcEndpointServiceRequest.  # noqa: E501
        :rtype: str
        """
        return self._private_dns_type

    @private_dns_type.setter
    def private_dns_type(self, private_dns_type):
        """Sets the private_dns_type of this CreateVpcEndpointServiceRequest.


        :param private_dns_type: The private_dns_type of this CreateVpcEndpointServiceRequest.  # noqa: E501
        :type: str
        """

        self._private_dns_type = private_dns_type

    @property
    def project_name(self):
        """Gets the project_name of this CreateVpcEndpointServiceRequest.  # noqa: E501


        :return: The project_name of this CreateVpcEndpointServiceRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this CreateVpcEndpointServiceRequest.


        :param project_name: The project_name of this CreateVpcEndpointServiceRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def resources(self):
        """Gets the resources of this CreateVpcEndpointServiceRequest.  # noqa: E501


        :return: The resources of this CreateVpcEndpointServiceRequest.  # noqa: E501
        :rtype: list[ResourceForCreateVpcEndpointServiceInput]
        """
        return self._resources

    @resources.setter
    def resources(self, resources):
        """Sets the resources of this CreateVpcEndpointServiceRequest.


        :param resources: The resources of this CreateVpcEndpointServiceRequest.  # noqa: E501
        :type: list[ResourceForCreateVpcEndpointServiceInput]
        """

        self._resources = resources

    @property
    def service_name_managed(self):
        """Gets the service_name_managed of this CreateVpcEndpointServiceRequest.  # noqa: E501


        :return: The service_name_managed of this CreateVpcEndpointServiceRequest.  # noqa: E501
        :rtype: str
        """
        return self._service_name_managed

    @service_name_managed.setter
    def service_name_managed(self, service_name_managed):
        """Sets the service_name_managed of this CreateVpcEndpointServiceRequest.


        :param service_name_managed: The service_name_managed of this CreateVpcEndpointServiceRequest.  # noqa: E501
        :type: str
        """

        self._service_name_managed = service_name_managed

    @property
    def service_name_suffix(self):
        """Gets the service_name_suffix of this CreateVpcEndpointServiceRequest.  # noqa: E501


        :return: The service_name_suffix of this CreateVpcEndpointServiceRequest.  # noqa: E501
        :rtype: str
        """
        return self._service_name_suffix

    @service_name_suffix.setter
    def service_name_suffix(self, service_name_suffix):
        """Sets the service_name_suffix of this CreateVpcEndpointServiceRequest.


        :param service_name_suffix: The service_name_suffix of this CreateVpcEndpointServiceRequest.  # noqa: E501
        :type: str
        """

        self._service_name_suffix = service_name_suffix

    @property
    def service_owner(self):
        """Gets the service_owner of this CreateVpcEndpointServiceRequest.  # noqa: E501


        :return: The service_owner of this CreateVpcEndpointServiceRequest.  # noqa: E501
        :rtype: str
        """
        return self._service_owner

    @service_owner.setter
    def service_owner(self, service_owner):
        """Sets the service_owner of this CreateVpcEndpointServiceRequest.


        :param service_owner: The service_owner of this CreateVpcEndpointServiceRequest.  # noqa: E501
        :type: str
        """

        self._service_owner = service_owner

    @property
    def service_resource_type(self):
        """Gets the service_resource_type of this CreateVpcEndpointServiceRequest.  # noqa: E501


        :return: The service_resource_type of this CreateVpcEndpointServiceRequest.  # noqa: E501
        :rtype: str
        """
        return self._service_resource_type

    @service_resource_type.setter
    def service_resource_type(self, service_resource_type):
        """Sets the service_resource_type of this CreateVpcEndpointServiceRequest.


        :param service_resource_type: The service_resource_type of this CreateVpcEndpointServiceRequest.  # noqa: E501
        :type: str
        """

        self._service_resource_type = service_resource_type

    @property
    def service_type(self):
        """Gets the service_type of this CreateVpcEndpointServiceRequest.  # noqa: E501


        :return: The service_type of this CreateVpcEndpointServiceRequest.  # noqa: E501
        :rtype: str
        """
        return self._service_type

    @service_type.setter
    def service_type(self, service_type):
        """Sets the service_type of this CreateVpcEndpointServiceRequest.


        :param service_type: The service_type of this CreateVpcEndpointServiceRequest.  # noqa: E501
        :type: str
        """

        self._service_type = service_type

    @property
    def tags(self):
        """Gets the tags of this CreateVpcEndpointServiceRequest.  # noqa: E501


        :return: The tags of this CreateVpcEndpointServiceRequest.  # noqa: E501
        :rtype: list[TagForCreateVpcEndpointServiceInput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this CreateVpcEndpointServiceRequest.


        :param tags: The tags of this CreateVpcEndpointServiceRequest.  # noqa: E501
        :type: list[TagForCreateVpcEndpointServiceInput]
        """

        self._tags = tags

    @property
    def wildcard_domain_enabled(self):
        """Gets the wildcard_domain_enabled of this CreateVpcEndpointServiceRequest.  # noqa: E501


        :return: The wildcard_domain_enabled of this CreateVpcEndpointServiceRequest.  # noqa: E501
        :rtype: bool
        """
        return self._wildcard_domain_enabled

    @wildcard_domain_enabled.setter
    def wildcard_domain_enabled(self, wildcard_domain_enabled):
        """Sets the wildcard_domain_enabled of this CreateVpcEndpointServiceRequest.


        :param wildcard_domain_enabled: The wildcard_domain_enabled of this CreateVpcEndpointServiceRequest.  # noqa: E501
        :type: bool
        """

        self._wildcard_domain_enabled = wildcard_domain_enabled

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateVpcEndpointServiceRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateVpcEndpointServiceRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateVpcEndpointServiceRequest):
            return True

        return self.to_dict() != other.to_dict()
