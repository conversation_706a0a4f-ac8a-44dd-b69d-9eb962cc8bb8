// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/coze-dev/coze-studio/backend/domain/workflow/internal/repo/dal/model"
)

func newWorkflowDraft(db *gorm.DB, opts ...gen.DOOption) workflowDraft {
	_workflowDraft := workflowDraft{}

	_workflowDraft.workflowDraftDo.UseDB(db, opts...)
	_workflowDraft.workflowDraftDo.UseModel(&model.WorkflowDraft{})

	tableName := _workflowDraft.workflowDraftDo.TableName()
	_workflowDraft.ALL = field.NewAsterisk(tableName)
	_workflowDraft.ID = field.NewInt64(tableName, "id")
	_workflowDraft.Canvas = field.NewString(tableName, "canvas")
	_workflowDraft.InputParams = field.NewString(tableName, "input_params")
	_workflowDraft.OutputParams = field.NewString(tableName, "output_params")
	_workflowDraft.TestRunSuccess = field.NewBool(tableName, "test_run_success")
	_workflowDraft.Modified = field.NewBool(tableName, "modified")
	_workflowDraft.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_workflowDraft.DeletedAt = field.NewField(tableName, "deleted_at")
	_workflowDraft.CommitID = field.NewString(tableName, "commit_id")

	_workflowDraft.fillFieldMap()

	return _workflowDraft
}

// workflowDraft Workflow canvas draft table, used to record the latest draft canvas information of workflow
type workflowDraft struct {
	workflowDraftDo

	ALL            field.Asterisk
	ID             field.Int64  // workflow ID
	Canvas         field.String // Front end schema
	InputParams    field.String // Input schema
	OutputParams   field.String // Output parameter schema
	TestRunSuccess field.Bool   // 0 not running, 1 running successfully
	Modified       field.Bool   // 0 has not been modified, 1 has been modified
	UpdatedAt      field.Int64  // Update Time in Milliseconds
	DeletedAt      field.Field  // Delete Time
	CommitID       field.String // used to uniquely identify a draft snapshot

	fieldMap map[string]field.Expr
}

func (w workflowDraft) Table(newTableName string) *workflowDraft {
	w.workflowDraftDo.UseTable(newTableName)
	return w.updateTableName(newTableName)
}

func (w workflowDraft) As(alias string) *workflowDraft {
	w.workflowDraftDo.DO = *(w.workflowDraftDo.As(alias).(*gen.DO))
	return w.updateTableName(alias)
}

func (w *workflowDraft) updateTableName(table string) *workflowDraft {
	w.ALL = field.NewAsterisk(table)
	w.ID = field.NewInt64(table, "id")
	w.Canvas = field.NewString(table, "canvas")
	w.InputParams = field.NewString(table, "input_params")
	w.OutputParams = field.NewString(table, "output_params")
	w.TestRunSuccess = field.NewBool(table, "test_run_success")
	w.Modified = field.NewBool(table, "modified")
	w.UpdatedAt = field.NewInt64(table, "updated_at")
	w.DeletedAt = field.NewField(table, "deleted_at")
	w.CommitID = field.NewString(table, "commit_id")

	w.fillFieldMap()

	return w
}

func (w *workflowDraft) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := w.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (w *workflowDraft) fillFieldMap() {
	w.fieldMap = make(map[string]field.Expr, 9)
	w.fieldMap["id"] = w.ID
	w.fieldMap["canvas"] = w.Canvas
	w.fieldMap["input_params"] = w.InputParams
	w.fieldMap["output_params"] = w.OutputParams
	w.fieldMap["test_run_success"] = w.TestRunSuccess
	w.fieldMap["modified"] = w.Modified
	w.fieldMap["updated_at"] = w.UpdatedAt
	w.fieldMap["deleted_at"] = w.DeletedAt
	w.fieldMap["commit_id"] = w.CommitID
}

func (w workflowDraft) clone(db *gorm.DB) workflowDraft {
	w.workflowDraftDo.ReplaceConnPool(db.Statement.ConnPool)
	return w
}

func (w workflowDraft) replaceDB(db *gorm.DB) workflowDraft {
	w.workflowDraftDo.ReplaceDB(db)
	return w
}

type workflowDraftDo struct{ gen.DO }

type IWorkflowDraftDo interface {
	gen.SubQuery
	Debug() IWorkflowDraftDo
	WithContext(ctx context.Context) IWorkflowDraftDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IWorkflowDraftDo
	WriteDB() IWorkflowDraftDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IWorkflowDraftDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IWorkflowDraftDo
	Not(conds ...gen.Condition) IWorkflowDraftDo
	Or(conds ...gen.Condition) IWorkflowDraftDo
	Select(conds ...field.Expr) IWorkflowDraftDo
	Where(conds ...gen.Condition) IWorkflowDraftDo
	Order(conds ...field.Expr) IWorkflowDraftDo
	Distinct(cols ...field.Expr) IWorkflowDraftDo
	Omit(cols ...field.Expr) IWorkflowDraftDo
	Join(table schema.Tabler, on ...field.Expr) IWorkflowDraftDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IWorkflowDraftDo
	RightJoin(table schema.Tabler, on ...field.Expr) IWorkflowDraftDo
	Group(cols ...field.Expr) IWorkflowDraftDo
	Having(conds ...gen.Condition) IWorkflowDraftDo
	Limit(limit int) IWorkflowDraftDo
	Offset(offset int) IWorkflowDraftDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IWorkflowDraftDo
	Unscoped() IWorkflowDraftDo
	Create(values ...*model.WorkflowDraft) error
	CreateInBatches(values []*model.WorkflowDraft, batchSize int) error
	Save(values ...*model.WorkflowDraft) error
	First() (*model.WorkflowDraft, error)
	Take() (*model.WorkflowDraft, error)
	Last() (*model.WorkflowDraft, error)
	Find() ([]*model.WorkflowDraft, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.WorkflowDraft, err error)
	FindInBatches(result *[]*model.WorkflowDraft, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.WorkflowDraft) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IWorkflowDraftDo
	Assign(attrs ...field.AssignExpr) IWorkflowDraftDo
	Joins(fields ...field.RelationField) IWorkflowDraftDo
	Preload(fields ...field.RelationField) IWorkflowDraftDo
	FirstOrInit() (*model.WorkflowDraft, error)
	FirstOrCreate() (*model.WorkflowDraft, error)
	FindByPage(offset int, limit int) (result []*model.WorkflowDraft, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IWorkflowDraftDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (w workflowDraftDo) Debug() IWorkflowDraftDo {
	return w.withDO(w.DO.Debug())
}

func (w workflowDraftDo) WithContext(ctx context.Context) IWorkflowDraftDo {
	return w.withDO(w.DO.WithContext(ctx))
}

func (w workflowDraftDo) ReadDB() IWorkflowDraftDo {
	return w.Clauses(dbresolver.Read)
}

func (w workflowDraftDo) WriteDB() IWorkflowDraftDo {
	return w.Clauses(dbresolver.Write)
}

func (w workflowDraftDo) Session(config *gorm.Session) IWorkflowDraftDo {
	return w.withDO(w.DO.Session(config))
}

func (w workflowDraftDo) Clauses(conds ...clause.Expression) IWorkflowDraftDo {
	return w.withDO(w.DO.Clauses(conds...))
}

func (w workflowDraftDo) Returning(value interface{}, columns ...string) IWorkflowDraftDo {
	return w.withDO(w.DO.Returning(value, columns...))
}

func (w workflowDraftDo) Not(conds ...gen.Condition) IWorkflowDraftDo {
	return w.withDO(w.DO.Not(conds...))
}

func (w workflowDraftDo) Or(conds ...gen.Condition) IWorkflowDraftDo {
	return w.withDO(w.DO.Or(conds...))
}

func (w workflowDraftDo) Select(conds ...field.Expr) IWorkflowDraftDo {
	return w.withDO(w.DO.Select(conds...))
}

func (w workflowDraftDo) Where(conds ...gen.Condition) IWorkflowDraftDo {
	return w.withDO(w.DO.Where(conds...))
}

func (w workflowDraftDo) Order(conds ...field.Expr) IWorkflowDraftDo {
	return w.withDO(w.DO.Order(conds...))
}

func (w workflowDraftDo) Distinct(cols ...field.Expr) IWorkflowDraftDo {
	return w.withDO(w.DO.Distinct(cols...))
}

func (w workflowDraftDo) Omit(cols ...field.Expr) IWorkflowDraftDo {
	return w.withDO(w.DO.Omit(cols...))
}

func (w workflowDraftDo) Join(table schema.Tabler, on ...field.Expr) IWorkflowDraftDo {
	return w.withDO(w.DO.Join(table, on...))
}

func (w workflowDraftDo) LeftJoin(table schema.Tabler, on ...field.Expr) IWorkflowDraftDo {
	return w.withDO(w.DO.LeftJoin(table, on...))
}

func (w workflowDraftDo) RightJoin(table schema.Tabler, on ...field.Expr) IWorkflowDraftDo {
	return w.withDO(w.DO.RightJoin(table, on...))
}

func (w workflowDraftDo) Group(cols ...field.Expr) IWorkflowDraftDo {
	return w.withDO(w.DO.Group(cols...))
}

func (w workflowDraftDo) Having(conds ...gen.Condition) IWorkflowDraftDo {
	return w.withDO(w.DO.Having(conds...))
}

func (w workflowDraftDo) Limit(limit int) IWorkflowDraftDo {
	return w.withDO(w.DO.Limit(limit))
}

func (w workflowDraftDo) Offset(offset int) IWorkflowDraftDo {
	return w.withDO(w.DO.Offset(offset))
}

func (w workflowDraftDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IWorkflowDraftDo {
	return w.withDO(w.DO.Scopes(funcs...))
}

func (w workflowDraftDo) Unscoped() IWorkflowDraftDo {
	return w.withDO(w.DO.Unscoped())
}

func (w workflowDraftDo) Create(values ...*model.WorkflowDraft) error {
	if len(values) == 0 {
		return nil
	}
	return w.DO.Create(values)
}

func (w workflowDraftDo) CreateInBatches(values []*model.WorkflowDraft, batchSize int) error {
	return w.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (w workflowDraftDo) Save(values ...*model.WorkflowDraft) error {
	if len(values) == 0 {
		return nil
	}
	return w.DO.Save(values)
}

func (w workflowDraftDo) First() (*model.WorkflowDraft, error) {
	if result, err := w.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.WorkflowDraft), nil
	}
}

func (w workflowDraftDo) Take() (*model.WorkflowDraft, error) {
	if result, err := w.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.WorkflowDraft), nil
	}
}

func (w workflowDraftDo) Last() (*model.WorkflowDraft, error) {
	if result, err := w.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.WorkflowDraft), nil
	}
}

func (w workflowDraftDo) Find() ([]*model.WorkflowDraft, error) {
	result, err := w.DO.Find()
	return result.([]*model.WorkflowDraft), err
}

func (w workflowDraftDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.WorkflowDraft, err error) {
	buf := make([]*model.WorkflowDraft, 0, batchSize)
	err = w.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (w workflowDraftDo) FindInBatches(result *[]*model.WorkflowDraft, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return w.DO.FindInBatches(result, batchSize, fc)
}

func (w workflowDraftDo) Attrs(attrs ...field.AssignExpr) IWorkflowDraftDo {
	return w.withDO(w.DO.Attrs(attrs...))
}

func (w workflowDraftDo) Assign(attrs ...field.AssignExpr) IWorkflowDraftDo {
	return w.withDO(w.DO.Assign(attrs...))
}

func (w workflowDraftDo) Joins(fields ...field.RelationField) IWorkflowDraftDo {
	for _, _f := range fields {
		w = *w.withDO(w.DO.Joins(_f))
	}
	return &w
}

func (w workflowDraftDo) Preload(fields ...field.RelationField) IWorkflowDraftDo {
	for _, _f := range fields {
		w = *w.withDO(w.DO.Preload(_f))
	}
	return &w
}

func (w workflowDraftDo) FirstOrInit() (*model.WorkflowDraft, error) {
	if result, err := w.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.WorkflowDraft), nil
	}
}

func (w workflowDraftDo) FirstOrCreate() (*model.WorkflowDraft, error) {
	if result, err := w.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.WorkflowDraft), nil
	}
}

func (w workflowDraftDo) FindByPage(offset int, limit int) (result []*model.WorkflowDraft, count int64, err error) {
	result, err = w.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = w.Offset(-1).Limit(-1).Count()
	return
}

func (w workflowDraftDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = w.Count()
	if err != nil {
		return
	}

	err = w.Offset(offset).Limit(limit).Scan(result)
	return
}

func (w workflowDraftDo) Scan(result interface{}) (err error) {
	return w.DO.Scan(result)
}

func (w workflowDraftDo) Delete(models ...*model.WorkflowDraft) (result gen.ResultInfo, err error) {
	return w.DO.Delete(models)
}

func (w *workflowDraftDo) withDO(do gen.Dao) *workflowDraftDo {
	w.DO = *do.(*gen.DO)
	return w
}
