// Code generated by thriftgo (0.4.1). DO NOT EDIT.

package memory

import (
	"context"
	"fmt"
	"github.com/apache/thrift/lib/go/thrift"
	"github.com/coze-dev/coze-studio/backend/api/model/knowledge/document"
	"github.com/coze-dev/coze-studio/backend/api/model/kvmemory"
	"github.com/coze-dev/coze-studio/backend/api/model/project_memory"
	"github.com/coze-dev/coze-studio/backend/api/model/table"
)

type MemoryService interface {
	// --- variable
	GetProjectVariableList(ctx context.Context, req *project_memory.GetProjectVariableListReq) (r *project_memory.GetProjectVariableListResp, err error)

	UpdateProjectVariable(ctx context.Context, req *project_memory.UpdateProjectVariableReq) (r *project_memory.UpdateProjectVariableResp, err error)

	GetMemoryVariableMeta(ctx context.Context, req *project_memory.GetMemoryVariableMetaReq) (r *project_memory.GetMemoryVariableMetaResp, err error)

	DelProfileMemory(ctx context.Context, req *kvmemory.DelProfileMemoryRequest) (r *kvmemory.DelProfileMemoryResponse, err error)

	GetPlayGroundMemory(ctx context.Context, req *kvmemory.GetProfileMemoryRequest) (r *kvmemory.GetProfileMemoryResponse, err error)

	GetSysVariableConf(ctx context.Context, req *kvmemory.GetSysVariableConfRequest) (r *kvmemory.GetSysVariableConfResponse, err error)

	SetKvMemory(ctx context.Context, req *kvmemory.SetKvMemoryReq) (r *kvmemory.SetKvMemoryResp, err error)
	// ---
	GetModeConfig(ctx context.Context, req *table.GetModeConfigRequest) (r *table.GetModeConfigResponse, err error)

	GetDocumentTableInfo(ctx context.Context, req *document.GetDocumentTableInfoRequest) (r *document.GetDocumentTableInfoResponse, err error)
}

type MemoryServiceClient struct {
	c thrift.TClient
}

func NewMemoryServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *MemoryServiceClient {
	return &MemoryServiceClient{
		c: thrift.NewTStandardClient(f.GetProtocol(t), f.GetProtocol(t)),
	}
}

func NewMemoryServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *MemoryServiceClient {
	return &MemoryServiceClient{
		c: thrift.NewTStandardClient(iprot, oprot),
	}
}

func NewMemoryServiceClient(c thrift.TClient) *MemoryServiceClient {
	return &MemoryServiceClient{
		c: c,
	}
}

func (p *MemoryServiceClient) Client_() thrift.TClient {
	return p.c
}

func (p *MemoryServiceClient) GetProjectVariableList(ctx context.Context, req *project_memory.GetProjectVariableListReq) (r *project_memory.GetProjectVariableListResp, err error) {
	var _args MemoryServiceGetProjectVariableListArgs
	_args.Req = req
	var _result MemoryServiceGetProjectVariableListResult
	if err = p.Client_().Call(ctx, "GetProjectVariableList", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *MemoryServiceClient) UpdateProjectVariable(ctx context.Context, req *project_memory.UpdateProjectVariableReq) (r *project_memory.UpdateProjectVariableResp, err error) {
	var _args MemoryServiceUpdateProjectVariableArgs
	_args.Req = req
	var _result MemoryServiceUpdateProjectVariableResult
	if err = p.Client_().Call(ctx, "UpdateProjectVariable", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *MemoryServiceClient) GetMemoryVariableMeta(ctx context.Context, req *project_memory.GetMemoryVariableMetaReq) (r *project_memory.GetMemoryVariableMetaResp, err error) {
	var _args MemoryServiceGetMemoryVariableMetaArgs
	_args.Req = req
	var _result MemoryServiceGetMemoryVariableMetaResult
	if err = p.Client_().Call(ctx, "GetMemoryVariableMeta", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *MemoryServiceClient) DelProfileMemory(ctx context.Context, req *kvmemory.DelProfileMemoryRequest) (r *kvmemory.DelProfileMemoryResponse, err error) {
	var _args MemoryServiceDelProfileMemoryArgs
	_args.Req = req
	var _result MemoryServiceDelProfileMemoryResult
	if err = p.Client_().Call(ctx, "DelProfileMemory", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *MemoryServiceClient) GetPlayGroundMemory(ctx context.Context, req *kvmemory.GetProfileMemoryRequest) (r *kvmemory.GetProfileMemoryResponse, err error) {
	var _args MemoryServiceGetPlayGroundMemoryArgs
	_args.Req = req
	var _result MemoryServiceGetPlayGroundMemoryResult
	if err = p.Client_().Call(ctx, "GetPlayGroundMemory", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *MemoryServiceClient) GetSysVariableConf(ctx context.Context, req *kvmemory.GetSysVariableConfRequest) (r *kvmemory.GetSysVariableConfResponse, err error) {
	var _args MemoryServiceGetSysVariableConfArgs
	_args.Req = req
	var _result MemoryServiceGetSysVariableConfResult
	if err = p.Client_().Call(ctx, "GetSysVariableConf", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *MemoryServiceClient) SetKvMemory(ctx context.Context, req *kvmemory.SetKvMemoryReq) (r *kvmemory.SetKvMemoryResp, err error) {
	var _args MemoryServiceSetKvMemoryArgs
	_args.Req = req
	var _result MemoryServiceSetKvMemoryResult
	if err = p.Client_().Call(ctx, "SetKvMemory", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *MemoryServiceClient) GetModeConfig(ctx context.Context, req *table.GetModeConfigRequest) (r *table.GetModeConfigResponse, err error) {
	var _args MemoryServiceGetModeConfigArgs
	_args.Req = req
	var _result MemoryServiceGetModeConfigResult
	if err = p.Client_().Call(ctx, "GetModeConfig", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *MemoryServiceClient) GetDocumentTableInfo(ctx context.Context, req *document.GetDocumentTableInfoRequest) (r *document.GetDocumentTableInfoResponse, err error) {
	var _args MemoryServiceGetDocumentTableInfoArgs
	_args.Req = req
	var _result MemoryServiceGetDocumentTableInfoResult
	if err = p.Client_().Call(ctx, "GetDocumentTableInfo", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

type MemoryServiceProcessor struct {
	processorMap map[string]thrift.TProcessorFunction
	handler      MemoryService
}

func (p *MemoryServiceProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
	p.processorMap[key] = processor
}

func (p *MemoryServiceProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
	processor, ok = p.processorMap[key]
	return processor, ok
}

func (p *MemoryServiceProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
	return p.processorMap
}

func NewMemoryServiceProcessor(handler MemoryService) *MemoryServiceProcessor {
	self := &MemoryServiceProcessor{handler: handler, processorMap: make(map[string]thrift.TProcessorFunction)}
	self.AddToProcessorMap("GetProjectVariableList", &memoryServiceProcessorGetProjectVariableList{handler: handler})
	self.AddToProcessorMap("UpdateProjectVariable", &memoryServiceProcessorUpdateProjectVariable{handler: handler})
	self.AddToProcessorMap("GetMemoryVariableMeta", &memoryServiceProcessorGetMemoryVariableMeta{handler: handler})
	self.AddToProcessorMap("DelProfileMemory", &memoryServiceProcessorDelProfileMemory{handler: handler})
	self.AddToProcessorMap("GetPlayGroundMemory", &memoryServiceProcessorGetPlayGroundMemory{handler: handler})
	self.AddToProcessorMap("GetSysVariableConf", &memoryServiceProcessorGetSysVariableConf{handler: handler})
	self.AddToProcessorMap("SetKvMemory", &memoryServiceProcessorSetKvMemory{handler: handler})
	self.AddToProcessorMap("GetModeConfig", &memoryServiceProcessorGetModeConfig{handler: handler})
	self.AddToProcessorMap("GetDocumentTableInfo", &memoryServiceProcessorGetDocumentTableInfo{handler: handler})
	return self
}
func (p *MemoryServiceProcessor) Process(ctx context.Context, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	name, _, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return false, err
	}
	if processor, ok := p.GetProcessorFunction(name); ok {
		return processor.Process(ctx, seqId, iprot, oprot)
	}
	iprot.Skip(thrift.STRUCT)
	iprot.ReadMessageEnd()
	x := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function "+name)
	oprot.WriteMessageBegin(name, thrift.EXCEPTION, seqId)
	x.Write(oprot)
	oprot.WriteMessageEnd()
	oprot.Flush(ctx)
	return false, x
}

type memoryServiceProcessorGetProjectVariableList struct {
	handler MemoryService
}

func (p *memoryServiceProcessorGetProjectVariableList) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := MemoryServiceGetProjectVariableListArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("GetProjectVariableList", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := MemoryServiceGetProjectVariableListResult{}
	var retval *project_memory.GetProjectVariableListResp
	if retval, err2 = p.handler.GetProjectVariableList(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing GetProjectVariableList: "+err2.Error())
		oprot.WriteMessageBegin("GetProjectVariableList", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("GetProjectVariableList", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type memoryServiceProcessorUpdateProjectVariable struct {
	handler MemoryService
}

func (p *memoryServiceProcessorUpdateProjectVariable) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := MemoryServiceUpdateProjectVariableArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("UpdateProjectVariable", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := MemoryServiceUpdateProjectVariableResult{}
	var retval *project_memory.UpdateProjectVariableResp
	if retval, err2 = p.handler.UpdateProjectVariable(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing UpdateProjectVariable: "+err2.Error())
		oprot.WriteMessageBegin("UpdateProjectVariable", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("UpdateProjectVariable", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type memoryServiceProcessorGetMemoryVariableMeta struct {
	handler MemoryService
}

func (p *memoryServiceProcessorGetMemoryVariableMeta) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := MemoryServiceGetMemoryVariableMetaArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("GetMemoryVariableMeta", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := MemoryServiceGetMemoryVariableMetaResult{}
	var retval *project_memory.GetMemoryVariableMetaResp
	if retval, err2 = p.handler.GetMemoryVariableMeta(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing GetMemoryVariableMeta: "+err2.Error())
		oprot.WriteMessageBegin("GetMemoryVariableMeta", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("GetMemoryVariableMeta", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type memoryServiceProcessorDelProfileMemory struct {
	handler MemoryService
}

func (p *memoryServiceProcessorDelProfileMemory) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := MemoryServiceDelProfileMemoryArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("DelProfileMemory", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := MemoryServiceDelProfileMemoryResult{}
	var retval *kvmemory.DelProfileMemoryResponse
	if retval, err2 = p.handler.DelProfileMemory(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing DelProfileMemory: "+err2.Error())
		oprot.WriteMessageBegin("DelProfileMemory", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("DelProfileMemory", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type memoryServiceProcessorGetPlayGroundMemory struct {
	handler MemoryService
}

func (p *memoryServiceProcessorGetPlayGroundMemory) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := MemoryServiceGetPlayGroundMemoryArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("GetPlayGroundMemory", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := MemoryServiceGetPlayGroundMemoryResult{}
	var retval *kvmemory.GetProfileMemoryResponse
	if retval, err2 = p.handler.GetPlayGroundMemory(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing GetPlayGroundMemory: "+err2.Error())
		oprot.WriteMessageBegin("GetPlayGroundMemory", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("GetPlayGroundMemory", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type memoryServiceProcessorGetSysVariableConf struct {
	handler MemoryService
}

func (p *memoryServiceProcessorGetSysVariableConf) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := MemoryServiceGetSysVariableConfArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("GetSysVariableConf", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := MemoryServiceGetSysVariableConfResult{}
	var retval *kvmemory.GetSysVariableConfResponse
	if retval, err2 = p.handler.GetSysVariableConf(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing GetSysVariableConf: "+err2.Error())
		oprot.WriteMessageBegin("GetSysVariableConf", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("GetSysVariableConf", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type memoryServiceProcessorSetKvMemory struct {
	handler MemoryService
}

func (p *memoryServiceProcessorSetKvMemory) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := MemoryServiceSetKvMemoryArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("SetKvMemory", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := MemoryServiceSetKvMemoryResult{}
	var retval *kvmemory.SetKvMemoryResp
	if retval, err2 = p.handler.SetKvMemory(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing SetKvMemory: "+err2.Error())
		oprot.WriteMessageBegin("SetKvMemory", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("SetKvMemory", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type memoryServiceProcessorGetModeConfig struct {
	handler MemoryService
}

func (p *memoryServiceProcessorGetModeConfig) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := MemoryServiceGetModeConfigArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("GetModeConfig", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := MemoryServiceGetModeConfigResult{}
	var retval *table.GetModeConfigResponse
	if retval, err2 = p.handler.GetModeConfig(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing GetModeConfig: "+err2.Error())
		oprot.WriteMessageBegin("GetModeConfig", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("GetModeConfig", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type memoryServiceProcessorGetDocumentTableInfo struct {
	handler MemoryService
}

func (p *memoryServiceProcessorGetDocumentTableInfo) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := MemoryServiceGetDocumentTableInfoArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("GetDocumentTableInfo", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := MemoryServiceGetDocumentTableInfoResult{}
	var retval *document.GetDocumentTableInfoResponse
	if retval, err2 = p.handler.GetDocumentTableInfo(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing GetDocumentTableInfo: "+err2.Error())
		oprot.WriteMessageBegin("GetDocumentTableInfo", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("GetDocumentTableInfo", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type MemoryServiceGetProjectVariableListArgs struct {
	Req *project_memory.GetProjectVariableListReq `thrift:"req,1"`
}

func NewMemoryServiceGetProjectVariableListArgs() *MemoryServiceGetProjectVariableListArgs {
	return &MemoryServiceGetProjectVariableListArgs{}
}

func (p *MemoryServiceGetProjectVariableListArgs) InitDefault() {
}

var MemoryServiceGetProjectVariableListArgs_Req_DEFAULT *project_memory.GetProjectVariableListReq

func (p *MemoryServiceGetProjectVariableListArgs) GetReq() (v *project_memory.GetProjectVariableListReq) {
	if !p.IsSetReq() {
		return MemoryServiceGetProjectVariableListArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_MemoryServiceGetProjectVariableListArgs = map[int16]string{
	1: "req",
}

func (p *MemoryServiceGetProjectVariableListArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *MemoryServiceGetProjectVariableListArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_MemoryServiceGetProjectVariableListArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *MemoryServiceGetProjectVariableListArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := project_memory.NewGetProjectVariableListReq()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *MemoryServiceGetProjectVariableListArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetProjectVariableList_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *MemoryServiceGetProjectVariableListArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *MemoryServiceGetProjectVariableListArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MemoryServiceGetProjectVariableListArgs(%+v)", *p)

}

type MemoryServiceGetProjectVariableListResult struct {
	Success *project_memory.GetProjectVariableListResp `thrift:"success,0,optional"`
}

func NewMemoryServiceGetProjectVariableListResult() *MemoryServiceGetProjectVariableListResult {
	return &MemoryServiceGetProjectVariableListResult{}
}

func (p *MemoryServiceGetProjectVariableListResult) InitDefault() {
}

var MemoryServiceGetProjectVariableListResult_Success_DEFAULT *project_memory.GetProjectVariableListResp

func (p *MemoryServiceGetProjectVariableListResult) GetSuccess() (v *project_memory.GetProjectVariableListResp) {
	if !p.IsSetSuccess() {
		return MemoryServiceGetProjectVariableListResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_MemoryServiceGetProjectVariableListResult = map[int16]string{
	0: "success",
}

func (p *MemoryServiceGetProjectVariableListResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *MemoryServiceGetProjectVariableListResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_MemoryServiceGetProjectVariableListResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *MemoryServiceGetProjectVariableListResult) ReadField0(iprot thrift.TProtocol) error {
	_field := project_memory.NewGetProjectVariableListResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *MemoryServiceGetProjectVariableListResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetProjectVariableList_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *MemoryServiceGetProjectVariableListResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *MemoryServiceGetProjectVariableListResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MemoryServiceGetProjectVariableListResult(%+v)", *p)

}

type MemoryServiceUpdateProjectVariableArgs struct {
	Req *project_memory.UpdateProjectVariableReq `thrift:"req,1"`
}

func NewMemoryServiceUpdateProjectVariableArgs() *MemoryServiceUpdateProjectVariableArgs {
	return &MemoryServiceUpdateProjectVariableArgs{}
}

func (p *MemoryServiceUpdateProjectVariableArgs) InitDefault() {
}

var MemoryServiceUpdateProjectVariableArgs_Req_DEFAULT *project_memory.UpdateProjectVariableReq

func (p *MemoryServiceUpdateProjectVariableArgs) GetReq() (v *project_memory.UpdateProjectVariableReq) {
	if !p.IsSetReq() {
		return MemoryServiceUpdateProjectVariableArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_MemoryServiceUpdateProjectVariableArgs = map[int16]string{
	1: "req",
}

func (p *MemoryServiceUpdateProjectVariableArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *MemoryServiceUpdateProjectVariableArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_MemoryServiceUpdateProjectVariableArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *MemoryServiceUpdateProjectVariableArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := project_memory.NewUpdateProjectVariableReq()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *MemoryServiceUpdateProjectVariableArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("UpdateProjectVariable_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *MemoryServiceUpdateProjectVariableArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *MemoryServiceUpdateProjectVariableArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MemoryServiceUpdateProjectVariableArgs(%+v)", *p)

}

type MemoryServiceUpdateProjectVariableResult struct {
	Success *project_memory.UpdateProjectVariableResp `thrift:"success,0,optional"`
}

func NewMemoryServiceUpdateProjectVariableResult() *MemoryServiceUpdateProjectVariableResult {
	return &MemoryServiceUpdateProjectVariableResult{}
}

func (p *MemoryServiceUpdateProjectVariableResult) InitDefault() {
}

var MemoryServiceUpdateProjectVariableResult_Success_DEFAULT *project_memory.UpdateProjectVariableResp

func (p *MemoryServiceUpdateProjectVariableResult) GetSuccess() (v *project_memory.UpdateProjectVariableResp) {
	if !p.IsSetSuccess() {
		return MemoryServiceUpdateProjectVariableResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_MemoryServiceUpdateProjectVariableResult = map[int16]string{
	0: "success",
}

func (p *MemoryServiceUpdateProjectVariableResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *MemoryServiceUpdateProjectVariableResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_MemoryServiceUpdateProjectVariableResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *MemoryServiceUpdateProjectVariableResult) ReadField0(iprot thrift.TProtocol) error {
	_field := project_memory.NewUpdateProjectVariableResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *MemoryServiceUpdateProjectVariableResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("UpdateProjectVariable_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *MemoryServiceUpdateProjectVariableResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *MemoryServiceUpdateProjectVariableResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MemoryServiceUpdateProjectVariableResult(%+v)", *p)

}

type MemoryServiceGetMemoryVariableMetaArgs struct {
	Req *project_memory.GetMemoryVariableMetaReq `thrift:"req,1"`
}

func NewMemoryServiceGetMemoryVariableMetaArgs() *MemoryServiceGetMemoryVariableMetaArgs {
	return &MemoryServiceGetMemoryVariableMetaArgs{}
}

func (p *MemoryServiceGetMemoryVariableMetaArgs) InitDefault() {
}

var MemoryServiceGetMemoryVariableMetaArgs_Req_DEFAULT *project_memory.GetMemoryVariableMetaReq

func (p *MemoryServiceGetMemoryVariableMetaArgs) GetReq() (v *project_memory.GetMemoryVariableMetaReq) {
	if !p.IsSetReq() {
		return MemoryServiceGetMemoryVariableMetaArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_MemoryServiceGetMemoryVariableMetaArgs = map[int16]string{
	1: "req",
}

func (p *MemoryServiceGetMemoryVariableMetaArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *MemoryServiceGetMemoryVariableMetaArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_MemoryServiceGetMemoryVariableMetaArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *MemoryServiceGetMemoryVariableMetaArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := project_memory.NewGetMemoryVariableMetaReq()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *MemoryServiceGetMemoryVariableMetaArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetMemoryVariableMeta_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *MemoryServiceGetMemoryVariableMetaArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *MemoryServiceGetMemoryVariableMetaArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MemoryServiceGetMemoryVariableMetaArgs(%+v)", *p)

}

type MemoryServiceGetMemoryVariableMetaResult struct {
	Success *project_memory.GetMemoryVariableMetaResp `thrift:"success,0,optional"`
}

func NewMemoryServiceGetMemoryVariableMetaResult() *MemoryServiceGetMemoryVariableMetaResult {
	return &MemoryServiceGetMemoryVariableMetaResult{}
}

func (p *MemoryServiceGetMemoryVariableMetaResult) InitDefault() {
}

var MemoryServiceGetMemoryVariableMetaResult_Success_DEFAULT *project_memory.GetMemoryVariableMetaResp

func (p *MemoryServiceGetMemoryVariableMetaResult) GetSuccess() (v *project_memory.GetMemoryVariableMetaResp) {
	if !p.IsSetSuccess() {
		return MemoryServiceGetMemoryVariableMetaResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_MemoryServiceGetMemoryVariableMetaResult = map[int16]string{
	0: "success",
}

func (p *MemoryServiceGetMemoryVariableMetaResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *MemoryServiceGetMemoryVariableMetaResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_MemoryServiceGetMemoryVariableMetaResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *MemoryServiceGetMemoryVariableMetaResult) ReadField0(iprot thrift.TProtocol) error {
	_field := project_memory.NewGetMemoryVariableMetaResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *MemoryServiceGetMemoryVariableMetaResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetMemoryVariableMeta_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *MemoryServiceGetMemoryVariableMetaResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *MemoryServiceGetMemoryVariableMetaResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MemoryServiceGetMemoryVariableMetaResult(%+v)", *p)

}

type MemoryServiceDelProfileMemoryArgs struct {
	Req *kvmemory.DelProfileMemoryRequest `thrift:"req,1"`
}

func NewMemoryServiceDelProfileMemoryArgs() *MemoryServiceDelProfileMemoryArgs {
	return &MemoryServiceDelProfileMemoryArgs{}
}

func (p *MemoryServiceDelProfileMemoryArgs) InitDefault() {
}

var MemoryServiceDelProfileMemoryArgs_Req_DEFAULT *kvmemory.DelProfileMemoryRequest

func (p *MemoryServiceDelProfileMemoryArgs) GetReq() (v *kvmemory.DelProfileMemoryRequest) {
	if !p.IsSetReq() {
		return MemoryServiceDelProfileMemoryArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_MemoryServiceDelProfileMemoryArgs = map[int16]string{
	1: "req",
}

func (p *MemoryServiceDelProfileMemoryArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *MemoryServiceDelProfileMemoryArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_MemoryServiceDelProfileMemoryArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *MemoryServiceDelProfileMemoryArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := kvmemory.NewDelProfileMemoryRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *MemoryServiceDelProfileMemoryArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("DelProfileMemory_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *MemoryServiceDelProfileMemoryArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *MemoryServiceDelProfileMemoryArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MemoryServiceDelProfileMemoryArgs(%+v)", *p)

}

type MemoryServiceDelProfileMemoryResult struct {
	Success *kvmemory.DelProfileMemoryResponse `thrift:"success,0,optional"`
}

func NewMemoryServiceDelProfileMemoryResult() *MemoryServiceDelProfileMemoryResult {
	return &MemoryServiceDelProfileMemoryResult{}
}

func (p *MemoryServiceDelProfileMemoryResult) InitDefault() {
}

var MemoryServiceDelProfileMemoryResult_Success_DEFAULT *kvmemory.DelProfileMemoryResponse

func (p *MemoryServiceDelProfileMemoryResult) GetSuccess() (v *kvmemory.DelProfileMemoryResponse) {
	if !p.IsSetSuccess() {
		return MemoryServiceDelProfileMemoryResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_MemoryServiceDelProfileMemoryResult = map[int16]string{
	0: "success",
}

func (p *MemoryServiceDelProfileMemoryResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *MemoryServiceDelProfileMemoryResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_MemoryServiceDelProfileMemoryResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *MemoryServiceDelProfileMemoryResult) ReadField0(iprot thrift.TProtocol) error {
	_field := kvmemory.NewDelProfileMemoryResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *MemoryServiceDelProfileMemoryResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("DelProfileMemory_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *MemoryServiceDelProfileMemoryResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *MemoryServiceDelProfileMemoryResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MemoryServiceDelProfileMemoryResult(%+v)", *p)

}

type MemoryServiceGetPlayGroundMemoryArgs struct {
	Req *kvmemory.GetProfileMemoryRequest `thrift:"req,1"`
}

func NewMemoryServiceGetPlayGroundMemoryArgs() *MemoryServiceGetPlayGroundMemoryArgs {
	return &MemoryServiceGetPlayGroundMemoryArgs{}
}

func (p *MemoryServiceGetPlayGroundMemoryArgs) InitDefault() {
}

var MemoryServiceGetPlayGroundMemoryArgs_Req_DEFAULT *kvmemory.GetProfileMemoryRequest

func (p *MemoryServiceGetPlayGroundMemoryArgs) GetReq() (v *kvmemory.GetProfileMemoryRequest) {
	if !p.IsSetReq() {
		return MemoryServiceGetPlayGroundMemoryArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_MemoryServiceGetPlayGroundMemoryArgs = map[int16]string{
	1: "req",
}

func (p *MemoryServiceGetPlayGroundMemoryArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *MemoryServiceGetPlayGroundMemoryArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_MemoryServiceGetPlayGroundMemoryArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *MemoryServiceGetPlayGroundMemoryArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := kvmemory.NewGetProfileMemoryRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *MemoryServiceGetPlayGroundMemoryArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetPlayGroundMemory_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *MemoryServiceGetPlayGroundMemoryArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *MemoryServiceGetPlayGroundMemoryArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MemoryServiceGetPlayGroundMemoryArgs(%+v)", *p)

}

type MemoryServiceGetPlayGroundMemoryResult struct {
	Success *kvmemory.GetProfileMemoryResponse `thrift:"success,0,optional"`
}

func NewMemoryServiceGetPlayGroundMemoryResult() *MemoryServiceGetPlayGroundMemoryResult {
	return &MemoryServiceGetPlayGroundMemoryResult{}
}

func (p *MemoryServiceGetPlayGroundMemoryResult) InitDefault() {
}

var MemoryServiceGetPlayGroundMemoryResult_Success_DEFAULT *kvmemory.GetProfileMemoryResponse

func (p *MemoryServiceGetPlayGroundMemoryResult) GetSuccess() (v *kvmemory.GetProfileMemoryResponse) {
	if !p.IsSetSuccess() {
		return MemoryServiceGetPlayGroundMemoryResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_MemoryServiceGetPlayGroundMemoryResult = map[int16]string{
	0: "success",
}

func (p *MemoryServiceGetPlayGroundMemoryResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *MemoryServiceGetPlayGroundMemoryResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_MemoryServiceGetPlayGroundMemoryResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *MemoryServiceGetPlayGroundMemoryResult) ReadField0(iprot thrift.TProtocol) error {
	_field := kvmemory.NewGetProfileMemoryResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *MemoryServiceGetPlayGroundMemoryResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetPlayGroundMemory_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *MemoryServiceGetPlayGroundMemoryResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *MemoryServiceGetPlayGroundMemoryResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MemoryServiceGetPlayGroundMemoryResult(%+v)", *p)

}

type MemoryServiceGetSysVariableConfArgs struct {
	Req *kvmemory.GetSysVariableConfRequest `thrift:"req,1"`
}

func NewMemoryServiceGetSysVariableConfArgs() *MemoryServiceGetSysVariableConfArgs {
	return &MemoryServiceGetSysVariableConfArgs{}
}

func (p *MemoryServiceGetSysVariableConfArgs) InitDefault() {
}

var MemoryServiceGetSysVariableConfArgs_Req_DEFAULT *kvmemory.GetSysVariableConfRequest

func (p *MemoryServiceGetSysVariableConfArgs) GetReq() (v *kvmemory.GetSysVariableConfRequest) {
	if !p.IsSetReq() {
		return MemoryServiceGetSysVariableConfArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_MemoryServiceGetSysVariableConfArgs = map[int16]string{
	1: "req",
}

func (p *MemoryServiceGetSysVariableConfArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *MemoryServiceGetSysVariableConfArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_MemoryServiceGetSysVariableConfArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *MemoryServiceGetSysVariableConfArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := kvmemory.NewGetSysVariableConfRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *MemoryServiceGetSysVariableConfArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetSysVariableConf_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *MemoryServiceGetSysVariableConfArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *MemoryServiceGetSysVariableConfArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MemoryServiceGetSysVariableConfArgs(%+v)", *p)

}

type MemoryServiceGetSysVariableConfResult struct {
	Success *kvmemory.GetSysVariableConfResponse `thrift:"success,0,optional"`
}

func NewMemoryServiceGetSysVariableConfResult() *MemoryServiceGetSysVariableConfResult {
	return &MemoryServiceGetSysVariableConfResult{}
}

func (p *MemoryServiceGetSysVariableConfResult) InitDefault() {
}

var MemoryServiceGetSysVariableConfResult_Success_DEFAULT *kvmemory.GetSysVariableConfResponse

func (p *MemoryServiceGetSysVariableConfResult) GetSuccess() (v *kvmemory.GetSysVariableConfResponse) {
	if !p.IsSetSuccess() {
		return MemoryServiceGetSysVariableConfResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_MemoryServiceGetSysVariableConfResult = map[int16]string{
	0: "success",
}

func (p *MemoryServiceGetSysVariableConfResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *MemoryServiceGetSysVariableConfResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_MemoryServiceGetSysVariableConfResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *MemoryServiceGetSysVariableConfResult) ReadField0(iprot thrift.TProtocol) error {
	_field := kvmemory.NewGetSysVariableConfResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *MemoryServiceGetSysVariableConfResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetSysVariableConf_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *MemoryServiceGetSysVariableConfResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *MemoryServiceGetSysVariableConfResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MemoryServiceGetSysVariableConfResult(%+v)", *p)

}

type MemoryServiceSetKvMemoryArgs struct {
	Req *kvmemory.SetKvMemoryReq `thrift:"req,1"`
}

func NewMemoryServiceSetKvMemoryArgs() *MemoryServiceSetKvMemoryArgs {
	return &MemoryServiceSetKvMemoryArgs{}
}

func (p *MemoryServiceSetKvMemoryArgs) InitDefault() {
}

var MemoryServiceSetKvMemoryArgs_Req_DEFAULT *kvmemory.SetKvMemoryReq

func (p *MemoryServiceSetKvMemoryArgs) GetReq() (v *kvmemory.SetKvMemoryReq) {
	if !p.IsSetReq() {
		return MemoryServiceSetKvMemoryArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_MemoryServiceSetKvMemoryArgs = map[int16]string{
	1: "req",
}

func (p *MemoryServiceSetKvMemoryArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *MemoryServiceSetKvMemoryArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_MemoryServiceSetKvMemoryArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *MemoryServiceSetKvMemoryArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := kvmemory.NewSetKvMemoryReq()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *MemoryServiceSetKvMemoryArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("SetKvMemory_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *MemoryServiceSetKvMemoryArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *MemoryServiceSetKvMemoryArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MemoryServiceSetKvMemoryArgs(%+v)", *p)

}

type MemoryServiceSetKvMemoryResult struct {
	Success *kvmemory.SetKvMemoryResp `thrift:"success,0,optional"`
}

func NewMemoryServiceSetKvMemoryResult() *MemoryServiceSetKvMemoryResult {
	return &MemoryServiceSetKvMemoryResult{}
}

func (p *MemoryServiceSetKvMemoryResult) InitDefault() {
}

var MemoryServiceSetKvMemoryResult_Success_DEFAULT *kvmemory.SetKvMemoryResp

func (p *MemoryServiceSetKvMemoryResult) GetSuccess() (v *kvmemory.SetKvMemoryResp) {
	if !p.IsSetSuccess() {
		return MemoryServiceSetKvMemoryResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_MemoryServiceSetKvMemoryResult = map[int16]string{
	0: "success",
}

func (p *MemoryServiceSetKvMemoryResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *MemoryServiceSetKvMemoryResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_MemoryServiceSetKvMemoryResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *MemoryServiceSetKvMemoryResult) ReadField0(iprot thrift.TProtocol) error {
	_field := kvmemory.NewSetKvMemoryResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *MemoryServiceSetKvMemoryResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("SetKvMemory_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *MemoryServiceSetKvMemoryResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *MemoryServiceSetKvMemoryResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MemoryServiceSetKvMemoryResult(%+v)", *p)

}

type MemoryServiceGetModeConfigArgs struct {
	Req *table.GetModeConfigRequest `thrift:"req,1"`
}

func NewMemoryServiceGetModeConfigArgs() *MemoryServiceGetModeConfigArgs {
	return &MemoryServiceGetModeConfigArgs{}
}

func (p *MemoryServiceGetModeConfigArgs) InitDefault() {
}

var MemoryServiceGetModeConfigArgs_Req_DEFAULT *table.GetModeConfigRequest

func (p *MemoryServiceGetModeConfigArgs) GetReq() (v *table.GetModeConfigRequest) {
	if !p.IsSetReq() {
		return MemoryServiceGetModeConfigArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_MemoryServiceGetModeConfigArgs = map[int16]string{
	1: "req",
}

func (p *MemoryServiceGetModeConfigArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *MemoryServiceGetModeConfigArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_MemoryServiceGetModeConfigArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *MemoryServiceGetModeConfigArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := table.NewGetModeConfigRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *MemoryServiceGetModeConfigArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetModeConfig_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *MemoryServiceGetModeConfigArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *MemoryServiceGetModeConfigArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MemoryServiceGetModeConfigArgs(%+v)", *p)

}

type MemoryServiceGetModeConfigResult struct {
	Success *table.GetModeConfigResponse `thrift:"success,0,optional"`
}

func NewMemoryServiceGetModeConfigResult() *MemoryServiceGetModeConfigResult {
	return &MemoryServiceGetModeConfigResult{}
}

func (p *MemoryServiceGetModeConfigResult) InitDefault() {
}

var MemoryServiceGetModeConfigResult_Success_DEFAULT *table.GetModeConfigResponse

func (p *MemoryServiceGetModeConfigResult) GetSuccess() (v *table.GetModeConfigResponse) {
	if !p.IsSetSuccess() {
		return MemoryServiceGetModeConfigResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_MemoryServiceGetModeConfigResult = map[int16]string{
	0: "success",
}

func (p *MemoryServiceGetModeConfigResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *MemoryServiceGetModeConfigResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_MemoryServiceGetModeConfigResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *MemoryServiceGetModeConfigResult) ReadField0(iprot thrift.TProtocol) error {
	_field := table.NewGetModeConfigResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *MemoryServiceGetModeConfigResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetModeConfig_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *MemoryServiceGetModeConfigResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *MemoryServiceGetModeConfigResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MemoryServiceGetModeConfigResult(%+v)", *p)

}

type MemoryServiceGetDocumentTableInfoArgs struct {
	Req *document.GetDocumentTableInfoRequest `thrift:"req,1"`
}

func NewMemoryServiceGetDocumentTableInfoArgs() *MemoryServiceGetDocumentTableInfoArgs {
	return &MemoryServiceGetDocumentTableInfoArgs{}
}

func (p *MemoryServiceGetDocumentTableInfoArgs) InitDefault() {
}

var MemoryServiceGetDocumentTableInfoArgs_Req_DEFAULT *document.GetDocumentTableInfoRequest

func (p *MemoryServiceGetDocumentTableInfoArgs) GetReq() (v *document.GetDocumentTableInfoRequest) {
	if !p.IsSetReq() {
		return MemoryServiceGetDocumentTableInfoArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_MemoryServiceGetDocumentTableInfoArgs = map[int16]string{
	1: "req",
}

func (p *MemoryServiceGetDocumentTableInfoArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *MemoryServiceGetDocumentTableInfoArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_MemoryServiceGetDocumentTableInfoArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *MemoryServiceGetDocumentTableInfoArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := document.NewGetDocumentTableInfoRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *MemoryServiceGetDocumentTableInfoArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetDocumentTableInfo_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *MemoryServiceGetDocumentTableInfoArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *MemoryServiceGetDocumentTableInfoArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MemoryServiceGetDocumentTableInfoArgs(%+v)", *p)

}

type MemoryServiceGetDocumentTableInfoResult struct {
	Success *document.GetDocumentTableInfoResponse `thrift:"success,0,optional"`
}

func NewMemoryServiceGetDocumentTableInfoResult() *MemoryServiceGetDocumentTableInfoResult {
	return &MemoryServiceGetDocumentTableInfoResult{}
}

func (p *MemoryServiceGetDocumentTableInfoResult) InitDefault() {
}

var MemoryServiceGetDocumentTableInfoResult_Success_DEFAULT *document.GetDocumentTableInfoResponse

func (p *MemoryServiceGetDocumentTableInfoResult) GetSuccess() (v *document.GetDocumentTableInfoResponse) {
	if !p.IsSetSuccess() {
		return MemoryServiceGetDocumentTableInfoResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_MemoryServiceGetDocumentTableInfoResult = map[int16]string{
	0: "success",
}

func (p *MemoryServiceGetDocumentTableInfoResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *MemoryServiceGetDocumentTableInfoResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_MemoryServiceGetDocumentTableInfoResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *MemoryServiceGetDocumentTableInfoResult) ReadField0(iprot thrift.TProtocol) error {
	_field := document.NewGetDocumentTableInfoResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *MemoryServiceGetDocumentTableInfoResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetDocumentTableInfo_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *MemoryServiceGetDocumentTableInfoResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *MemoryServiceGetDocumentTableInfoResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MemoryServiceGetDocumentTableInfoResult(%+v)", *p)

}
