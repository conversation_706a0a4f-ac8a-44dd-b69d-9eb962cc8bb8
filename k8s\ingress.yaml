# Ingress 配置 - 统一入口
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: paper-services-ingress
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/cors-allow-origin: "*"
    nginx.ingress.kubernetes.io/cors-allow-methods: "GET, POST, PUT, DELETE, OPTIONS"
    nginx.ingress.kubernetes.io/cors-allow-headers: "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization"
spec:
  rules:
  - host: localhost
    http:
      paths:
      # 前端 UI 访问
      - path: /
        pathType: Prefix
        backend:
          service:
            name: lunwen-generate-ui
            port:
              number: 3000
      # Editor API 访问
      - path: /editor-api
        pathType: Prefix
        backend:
          service:
            name: paper-editor-api
            port:
              number: 8890
      # Node 服务访问
      - path: /node-api
        pathType: Prefix
        backend:
          service:
            name: paper-node-service
            port:
              number: 9529
      # Python 服务访问
      - path: /py-api
        pathType: Prefix
        backend:
          service:
            name: paper-py-service
            port:
              number: 9528
