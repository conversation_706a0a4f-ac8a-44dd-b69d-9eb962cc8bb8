// Code generated by thriftgo (0.4.1). DO NOT EDIT.

package dataset

import (
	"database/sql"
	"database/sql/driver"
	"fmt"
	"github.com/apache/thrift/lib/go/thrift"
)

// type
type FormatType int64

const (
	// Text
	FormatType_Text FormatType = 0
	// table
	FormatType_Table FormatType = 1
	// image
	FormatType_Image FormatType = 2
	// database
	FormatType_Database FormatType = 3
)

func (p FormatType) String() string {
	switch p {
	case FormatType_Text:
		return "Text"
	case FormatType_Table:
		return "Table"
	case FormatType_Image:
		return "Image"
	case FormatType_Database:
		return "Database"
	}
	return "<UNSET>"
}

func FormatTypeFromString(s string) (FormatType, error) {
	switch s {
	case "Text":
		return FormatType_Text, nil
	case "Table":
		return FormatType_Table, nil
	case "Image":
		return FormatType_Image, nil
	case "Database":
		return FormatType_Database, nil
	}
	return FormatType(0), fmt.Errorf("not a valid FormatType string")
}

func FormatTypePtr(v FormatType) *FormatType { return &v }
func (p *FormatType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = FormatType(result.Int64)
	return
}

func (p *FormatType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type ChunkType int64

const (
	ChunkType_DefaultChunk ChunkType = 0
	ChunkType_CustomChunk  ChunkType = 1
	ChunkType_LevelChunk   ChunkType = 2
)

func (p ChunkType) String() string {
	switch p {
	case ChunkType_DefaultChunk:
		return "DefaultChunk"
	case ChunkType_CustomChunk:
		return "CustomChunk"
	case ChunkType_LevelChunk:
		return "LevelChunk"
	}
	return "<UNSET>"
}

func ChunkTypeFromString(s string) (ChunkType, error) {
	switch s {
	case "DefaultChunk":
		return ChunkType_DefaultChunk, nil
	case "CustomChunk":
		return ChunkType_CustomChunk, nil
	case "LevelChunk":
		return ChunkType_LevelChunk, nil
	}
	return ChunkType(0), fmt.Errorf("not a valid ChunkType string")
}

func ChunkTypePtr(v ChunkType) *ChunkType { return &v }
func (p *ChunkType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = ChunkType(result.Int64)
	return
}

func (p *ChunkType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type ContentSchema int64

const (
	ContentSchema_DefaultSchema    ContentSchema = 0
	ContentSchema_LinkReaderSchema ContentSchema = 1
)

func (p ContentSchema) String() string {
	switch p {
	case ContentSchema_DefaultSchema:
		return "DefaultSchema"
	case ContentSchema_LinkReaderSchema:
		return "LinkReaderSchema"
	}
	return "<UNSET>"
}

func ContentSchemaFromString(s string) (ContentSchema, error) {
	switch s {
	case "DefaultSchema":
		return ContentSchema_DefaultSchema, nil
	case "LinkReaderSchema":
		return ContentSchema_LinkReaderSchema, nil
	}
	return ContentSchema(0), fmt.Errorf("not a valid ContentSchema string")
}

func ContentSchemaPtr(v ContentSchema) *ContentSchema { return &v }
func (p *ContentSchema) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = ContentSchema(result.Int64)
	return
}

func (p *ContentSchema) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type CaptionType int64

const (
	// intelligent annotation
	CaptionType_Auto CaptionType = 0
	// manual annotation
	CaptionType_Manual CaptionType = 1
)

func (p CaptionType) String() string {
	switch p {
	case CaptionType_Auto:
		return "Auto"
	case CaptionType_Manual:
		return "Manual"
	}
	return "<UNSET>"
}

func CaptionTypeFromString(s string) (CaptionType, error) {
	switch s {
	case "Auto":
		return CaptionType_Auto, nil
	case "Manual":
		return CaptionType_Manual, nil
	}
	return CaptionType(0), fmt.Errorf("not a valid CaptionType string")
}

func CaptionTypePtr(v CaptionType) *CaptionType { return &v }
func (p *CaptionType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = CaptionType(result.Int64)
	return
}

func (p *CaptionType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type DocumentStatus int64

const (
	// Uploading
	DocumentStatus_Processing DocumentStatus = 0
	// take effect
	DocumentStatus_Enable DocumentStatus = 1
	// failure
	DocumentStatus_Disable DocumentStatus = 2
	// delete
	DocumentStatus_Deleted DocumentStatus = 3
	// In rescaling, the caller is not aware of the state
	DocumentStatus_Resegment DocumentStatus = 4
	// Refreshing (will be deleted after successful refresh)
	DocumentStatus_Refreshing DocumentStatus = 5
	// fail
	DocumentStatus_Failed DocumentStatus = 9
)

func (p DocumentStatus) String() string {
	switch p {
	case DocumentStatus_Processing:
		return "Processing"
	case DocumentStatus_Enable:
		return "Enable"
	case DocumentStatus_Disable:
		return "Disable"
	case DocumentStatus_Deleted:
		return "Deleted"
	case DocumentStatus_Resegment:
		return "Resegment"
	case DocumentStatus_Refreshing:
		return "Refreshing"
	case DocumentStatus_Failed:
		return "Failed"
	}
	return "<UNSET>"
}

func DocumentStatusFromString(s string) (DocumentStatus, error) {
	switch s {
	case "Processing":
		return DocumentStatus_Processing, nil
	case "Enable":
		return DocumentStatus_Enable, nil
	case "Disable":
		return DocumentStatus_Disable, nil
	case "Deleted":
		return DocumentStatus_Deleted, nil
	case "Resegment":
		return DocumentStatus_Resegment, nil
	case "Refreshing":
		return DocumentStatus_Refreshing, nil
	case "Failed":
		return DocumentStatus_Failed, nil
	}
	return DocumentStatus(0), fmt.Errorf("not a valid DocumentStatus string")
}

func DocumentStatusPtr(v DocumentStatus) *DocumentStatus { return &v }
func (p *DocumentStatus) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = DocumentStatus(result.Int64)
	return
}

func (p *DocumentStatus) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type DocumentSource int64

const (
	// local file upload
	DocumentSource_Document DocumentSource = 0
	// custom type
	DocumentSource_Custom DocumentSource = 2
)

func (p DocumentSource) String() string {
	switch p {
	case DocumentSource_Document:
		return "Document"
	case DocumentSource_Custom:
		return "Custom"
	}
	return "<UNSET>"
}

func DocumentSourceFromString(s string) (DocumentSource, error) {
	switch s {
	case "Document":
		return DocumentSource_Document, nil
	case "Custom":
		return DocumentSource_Custom, nil
	}
	return DocumentSource(0), fmt.Errorf("not a valid DocumentSource string")
}

func DocumentSourcePtr(v DocumentSource) *DocumentSource { return &v }
func (p *DocumentSource) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = DocumentSource(result.Int64)
	return
}

func (p *DocumentSource) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type ParsingType int64

const (
	//fast parse
	ParsingType_FastParsing ParsingType = 0
	//accurate analysis
	ParsingType_AccurateParsing ParsingType = 1
)

func (p ParsingType) String() string {
	switch p {
	case ParsingType_FastParsing:
		return "FastParsing"
	case ParsingType_AccurateParsing:
		return "AccurateParsing"
	}
	return "<UNSET>"
}

func ParsingTypeFromString(s string) (ParsingType, error) {
	switch s {
	case "FastParsing":
		return ParsingType_FastParsing, nil
	case "AccurateParsing":
		return ParsingType_AccurateParsing, nil
	}
	return ParsingType(0), fmt.Errorf("not a valid ParsingType string")
}

func ParsingTypePtr(v ParsingType) *ParsingType { return &v }
func (p *ParsingType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = ParsingType(result.Int64)
	return
}

func (p *ParsingType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

// sort field
type OrderField int64

const (
	OrderField_CreateTime OrderField = 1
	OrderField_UpdateTime OrderField = 2
)

func (p OrderField) String() string {
	switch p {
	case OrderField_CreateTime:
		return "CreateTime"
	case OrderField_UpdateTime:
		return "UpdateTime"
	}
	return "<UNSET>"
}

func OrderFieldFromString(s string) (OrderField, error) {
	switch s {
	case "CreateTime":
		return OrderField_CreateTime, nil
	case "UpdateTime":
		return OrderField_UpdateTime, nil
	}
	return OrderField(0), fmt.Errorf("not a valid OrderField string")
}

func OrderFieldPtr(v OrderField) *OrderField { return &v }
func (p *OrderField) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = OrderField(result.Int64)
	return
}

func (p *OrderField) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

// OrderType
type OrderType int64

const (
	OrderType_Desc OrderType = 1
	OrderType_Asc  OrderType = 2
)

func (p OrderType) String() string {
	switch p {
	case OrderType_Desc:
		return "Desc"
	case OrderType_Asc:
		return "Asc"
	}
	return "<UNSET>"
}

func OrderTypeFromString(s string) (OrderType, error) {
	switch s {
	case "Desc":
		return OrderType_Desc, nil
	case "Asc":
		return OrderType_Asc, nil
	}
	return OrderType(0), fmt.Errorf("not a valid OrderType string")
}

func OrderTypePtr(v OrderType) *OrderType { return &v }
func (p *OrderType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = OrderType(result.Int64)
	return
}

func (p *OrderType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type ReviewStatus int64

const (
	// Processing
	ReviewStatus_Processing ReviewStatus = 0
	// Completed.
	ReviewStatus_Enable ReviewStatus = 1
	// fail
	ReviewStatus_Failed ReviewStatus = 2
	// fail
	ReviewStatus_ForceStop ReviewStatus = 3
)

func (p ReviewStatus) String() string {
	switch p {
	case ReviewStatus_Processing:
		return "Processing"
	case ReviewStatus_Enable:
		return "Enable"
	case ReviewStatus_Failed:
		return "Failed"
	case ReviewStatus_ForceStop:
		return "ForceStop"
	}
	return "<UNSET>"
}

func ReviewStatusFromString(s string) (ReviewStatus, error) {
	switch s {
	case "Processing":
		return ReviewStatus_Processing, nil
	case "Enable":
		return ReviewStatus_Enable, nil
	case "Failed":
		return ReviewStatus_Failed, nil
	case "ForceStop":
		return ReviewStatus_ForceStop, nil
	}
	return ReviewStatus(0), fmt.Errorf("not a valid ReviewStatus string")
}

func ReviewStatusPtr(v ReviewStatus) *ReviewStatus { return &v }
func (p *ReviewStatus) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = ReviewStatus(result.Int64)
	return
}

func (p *ReviewStatus) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type ChunkStrategy struct {
	// A separator, such as a period
	Separator string `thrift:"separator,1" form:"separator" json:"separator" query:"separator"`
	// Maximum number of tokens for sharding
	MaxTokens int64 `thrift:"max_tokens,2" form:"max_tokens" json:"max_tokens" query:"max_tokens"`
	// Replace consecutive spaces, newlines, and tabs
	RemoveExtraSpaces bool `thrift:"remove_extra_spaces,3" form:"remove_extra_spaces" json:"remove_extra_spaces" query:"remove_extra_spaces"`
	// Remove URL and email
	RemoveUrlsEmails bool `thrift:"remove_urls_emails,4" form:"remove_urls_emails" json:"remove_urls_emails" query:"remove_urls_emails"`
	// If 0, the configuration of the above fields is not used
	ChunkType ChunkType `thrift:"chunk_type,5" form:"chunk_type" json:"chunk_type" query:"chunk_type"`
	// Image type, image description text annotation method
	CaptionType *CaptionType `thrift:"caption_type,7,optional" form:"caption_type" json:"caption_type,omitempty" query:"caption_type"`
	//segmented overlap
	Overlap *int64 `thrift:"overlap,8,optional" form:"overlap" json:"overlap,omitempty" query:"overlap"`
	//Maximum number of levels (effective when segmented by level)
	MaxLevel *int64 `thrift:"max_level,9,optional" form:"max_level" json:"max_level,omitempty" query:"max_level"`
	//Slice preserves level headers (effective when segmented by level)
	SaveTitle *bool `thrift:"save_title,10,optional" form:"save_title" json:"save_title,omitempty" query:"save_title"`
}

func NewChunkStrategy() *ChunkStrategy {
	return &ChunkStrategy{}
}

func (p *ChunkStrategy) InitDefault() {
}

func (p *ChunkStrategy) GetSeparator() (v string) {
	return p.Separator
}

func (p *ChunkStrategy) GetMaxTokens() (v int64) {
	return p.MaxTokens
}

func (p *ChunkStrategy) GetRemoveExtraSpaces() (v bool) {
	return p.RemoveExtraSpaces
}

func (p *ChunkStrategy) GetRemoveUrlsEmails() (v bool) {
	return p.RemoveUrlsEmails
}

func (p *ChunkStrategy) GetChunkType() (v ChunkType) {
	return p.ChunkType
}

var ChunkStrategy_CaptionType_DEFAULT CaptionType

func (p *ChunkStrategy) GetCaptionType() (v CaptionType) {
	if !p.IsSetCaptionType() {
		return ChunkStrategy_CaptionType_DEFAULT
	}
	return *p.CaptionType
}

var ChunkStrategy_Overlap_DEFAULT int64

func (p *ChunkStrategy) GetOverlap() (v int64) {
	if !p.IsSetOverlap() {
		return ChunkStrategy_Overlap_DEFAULT
	}
	return *p.Overlap
}

var ChunkStrategy_MaxLevel_DEFAULT int64

func (p *ChunkStrategy) GetMaxLevel() (v int64) {
	if !p.IsSetMaxLevel() {
		return ChunkStrategy_MaxLevel_DEFAULT
	}
	return *p.MaxLevel
}

var ChunkStrategy_SaveTitle_DEFAULT bool

func (p *ChunkStrategy) GetSaveTitle() (v bool) {
	if !p.IsSetSaveTitle() {
		return ChunkStrategy_SaveTitle_DEFAULT
	}
	return *p.SaveTitle
}

var fieldIDToName_ChunkStrategy = map[int16]string{
	1:  "separator",
	2:  "max_tokens",
	3:  "remove_extra_spaces",
	4:  "remove_urls_emails",
	5:  "chunk_type",
	7:  "caption_type",
	8:  "overlap",
	9:  "max_level",
	10: "save_title",
}

func (p *ChunkStrategy) IsSetCaptionType() bool {
	return p.CaptionType != nil
}

func (p *ChunkStrategy) IsSetOverlap() bool {
	return p.Overlap != nil
}

func (p *ChunkStrategy) IsSetMaxLevel() bool {
	return p.MaxLevel != nil
}

func (p *ChunkStrategy) IsSetSaveTitle() bool {
	return p.SaveTitle != nil
}

func (p *ChunkStrategy) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ChunkStrategy[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ChunkStrategy) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Separator = _field
	return nil
}
func (p *ChunkStrategy) ReadField2(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.MaxTokens = _field
	return nil
}
func (p *ChunkStrategy) ReadField3(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RemoveExtraSpaces = _field
	return nil
}
func (p *ChunkStrategy) ReadField4(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RemoveUrlsEmails = _field
	return nil
}
func (p *ChunkStrategy) ReadField5(iprot thrift.TProtocol) error {

	var _field ChunkType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = ChunkType(v)
	}
	p.ChunkType = _field
	return nil
}
func (p *ChunkStrategy) ReadField7(iprot thrift.TProtocol) error {

	var _field *CaptionType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := CaptionType(v)
		_field = &tmp
	}
	p.CaptionType = _field
	return nil
}
func (p *ChunkStrategy) ReadField8(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Overlap = _field
	return nil
}
func (p *ChunkStrategy) ReadField9(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.MaxLevel = _field
	return nil
}
func (p *ChunkStrategy) ReadField10(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.SaveTitle = _field
	return nil
}

func (p *ChunkStrategy) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ChunkStrategy"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ChunkStrategy) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("separator", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Separator); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ChunkStrategy) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("max_tokens", thrift.I64, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.MaxTokens); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ChunkStrategy) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("remove_extra_spaces", thrift.BOOL, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.RemoveExtraSpaces); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *ChunkStrategy) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("remove_urls_emails", thrift.BOOL, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.RemoveUrlsEmails); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *ChunkStrategy) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("chunk_type", thrift.I32, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.ChunkType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *ChunkStrategy) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetCaptionType() {
		if err = oprot.WriteFieldBegin("caption_type", thrift.I32, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.CaptionType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}
func (p *ChunkStrategy) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetOverlap() {
		if err = oprot.WriteFieldBegin("overlap", thrift.I64, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.Overlap); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}
func (p *ChunkStrategy) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetMaxLevel() {
		if err = oprot.WriteFieldBegin("max_level", thrift.I64, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.MaxLevel); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}
func (p *ChunkStrategy) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetSaveTitle() {
		if err = oprot.WriteFieldBegin("save_title", thrift.BOOL, 10); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.SaveTitle); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *ChunkStrategy) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ChunkStrategy(%+v)", *p)

}

type ParsingStrategy struct {
	//parse type
	ParsingType *ParsingType `thrift:"parsing_type,1,optional" form:"parsing_type" json:"parsing_type,omitempty" query:"parsing_type"`
	//Whether to enable image element extraction (effective when accurately parsing)
	ImageExtraction *bool `thrift:"image_extraction,2,optional" form:"image_extraction" json:"image_extraction,omitempty" query:"image_extraction"`
	//Whether to enable table element extraction (effective when accurately parsing)
	TableExtraction *bool `thrift:"table_extraction,3,optional" form:"table_extraction" json:"table_extraction,omitempty" query:"table_extraction"`
	//Whether to turn on picture OCR (effective when accurate analysis)
	ImageOcr *bool `thrift:"image_ocr,4,optional" form:"image_ocr" json:"image_ocr,omitempty" query:"image_ocr"`
}

func NewParsingStrategy() *ParsingStrategy {
	return &ParsingStrategy{}
}

func (p *ParsingStrategy) InitDefault() {
}

var ParsingStrategy_ParsingType_DEFAULT ParsingType

func (p *ParsingStrategy) GetParsingType() (v ParsingType) {
	if !p.IsSetParsingType() {
		return ParsingStrategy_ParsingType_DEFAULT
	}
	return *p.ParsingType
}

var ParsingStrategy_ImageExtraction_DEFAULT bool

func (p *ParsingStrategy) GetImageExtraction() (v bool) {
	if !p.IsSetImageExtraction() {
		return ParsingStrategy_ImageExtraction_DEFAULT
	}
	return *p.ImageExtraction
}

var ParsingStrategy_TableExtraction_DEFAULT bool

func (p *ParsingStrategy) GetTableExtraction() (v bool) {
	if !p.IsSetTableExtraction() {
		return ParsingStrategy_TableExtraction_DEFAULT
	}
	return *p.TableExtraction
}

var ParsingStrategy_ImageOcr_DEFAULT bool

func (p *ParsingStrategy) GetImageOcr() (v bool) {
	if !p.IsSetImageOcr() {
		return ParsingStrategy_ImageOcr_DEFAULT
	}
	return *p.ImageOcr
}

var fieldIDToName_ParsingStrategy = map[int16]string{
	1: "parsing_type",
	2: "image_extraction",
	3: "table_extraction",
	4: "image_ocr",
}

func (p *ParsingStrategy) IsSetParsingType() bool {
	return p.ParsingType != nil
}

func (p *ParsingStrategy) IsSetImageExtraction() bool {
	return p.ImageExtraction != nil
}

func (p *ParsingStrategy) IsSetTableExtraction() bool {
	return p.TableExtraction != nil
}

func (p *ParsingStrategy) IsSetImageOcr() bool {
	return p.ImageOcr != nil
}

func (p *ParsingStrategy) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ParsingStrategy[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ParsingStrategy) ReadField1(iprot thrift.TProtocol) error {

	var _field *ParsingType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := ParsingType(v)
		_field = &tmp
	}
	p.ParsingType = _field
	return nil
}
func (p *ParsingStrategy) ReadField2(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ImageExtraction = _field
	return nil
}
func (p *ParsingStrategy) ReadField3(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.TableExtraction = _field
	return nil
}
func (p *ParsingStrategy) ReadField4(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ImageOcr = _field
	return nil
}

func (p *ParsingStrategy) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ParsingStrategy"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ParsingStrategy) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetParsingType() {
		if err = oprot.WriteFieldBegin("parsing_type", thrift.I32, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.ParsingType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ParsingStrategy) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetImageExtraction() {
		if err = oprot.WriteFieldBegin("image_extraction", thrift.BOOL, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.ImageExtraction); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ParsingStrategy) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetTableExtraction() {
		if err = oprot.WriteFieldBegin("table_extraction", thrift.BOOL, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.TableExtraction); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *ParsingStrategy) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetImageOcr() {
		if err = oprot.WriteFieldBegin("image_ocr", thrift.BOOL, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.ImageOcr); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ParsingStrategy) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ParsingStrategy(%+v)", *p)

}

type IndexStrategy struct {
	//Whether to enable vector indexing (default is true)
	VectorIndexing *bool `thrift:"vector_indexing,1,optional" form:"vector_indexing" json:"vector_indexing,omitempty" query:"vector_indexing"`
	//Whether to enable keyword indexing (default is true)
	KeywordIndexing *bool `thrift:"keyword_indexing,2,optional" form:"keyword_indexing" json:"keyword_indexing,omitempty" query:"keyword_indexing"`
	//Whether to enable hierarchical indexing
	HierarchicalIndexing *bool `thrift:"hierarchical_indexing,3,optional" form:"hierarchical_indexing" json:"hierarchical_indexing,omitempty" query:"hierarchical_indexing"`
	//vector model
	Model *string `thrift:"model,4,optional" form:"model" json:"model,omitempty" query:"model"`
}

func NewIndexStrategy() *IndexStrategy {
	return &IndexStrategy{}
}

func (p *IndexStrategy) InitDefault() {
}

var IndexStrategy_VectorIndexing_DEFAULT bool

func (p *IndexStrategy) GetVectorIndexing() (v bool) {
	if !p.IsSetVectorIndexing() {
		return IndexStrategy_VectorIndexing_DEFAULT
	}
	return *p.VectorIndexing
}

var IndexStrategy_KeywordIndexing_DEFAULT bool

func (p *IndexStrategy) GetKeywordIndexing() (v bool) {
	if !p.IsSetKeywordIndexing() {
		return IndexStrategy_KeywordIndexing_DEFAULT
	}
	return *p.KeywordIndexing
}

var IndexStrategy_HierarchicalIndexing_DEFAULT bool

func (p *IndexStrategy) GetHierarchicalIndexing() (v bool) {
	if !p.IsSetHierarchicalIndexing() {
		return IndexStrategy_HierarchicalIndexing_DEFAULT
	}
	return *p.HierarchicalIndexing
}

var IndexStrategy_Model_DEFAULT string

func (p *IndexStrategy) GetModel() (v string) {
	if !p.IsSetModel() {
		return IndexStrategy_Model_DEFAULT
	}
	return *p.Model
}

var fieldIDToName_IndexStrategy = map[int16]string{
	1: "vector_indexing",
	2: "keyword_indexing",
	3: "hierarchical_indexing",
	4: "model",
}

func (p *IndexStrategy) IsSetVectorIndexing() bool {
	return p.VectorIndexing != nil
}

func (p *IndexStrategy) IsSetKeywordIndexing() bool {
	return p.KeywordIndexing != nil
}

func (p *IndexStrategy) IsSetHierarchicalIndexing() bool {
	return p.HierarchicalIndexing != nil
}

func (p *IndexStrategy) IsSetModel() bool {
	return p.Model != nil
}

func (p *IndexStrategy) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_IndexStrategy[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *IndexStrategy) ReadField1(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.VectorIndexing = _field
	return nil
}
func (p *IndexStrategy) ReadField2(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.KeywordIndexing = _field
	return nil
}
func (p *IndexStrategy) ReadField3(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.HierarchicalIndexing = _field
	return nil
}
func (p *IndexStrategy) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Model = _field
	return nil
}

func (p *IndexStrategy) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("IndexStrategy"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *IndexStrategy) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetVectorIndexing() {
		if err = oprot.WriteFieldBegin("vector_indexing", thrift.BOOL, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.VectorIndexing); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *IndexStrategy) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetKeywordIndexing() {
		if err = oprot.WriteFieldBegin("keyword_indexing", thrift.BOOL, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.KeywordIndexing); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *IndexStrategy) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetHierarchicalIndexing() {
		if err = oprot.WriteFieldBegin("hierarchical_indexing", thrift.BOOL, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.HierarchicalIndexing); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *IndexStrategy) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetModel() {
		if err = oprot.WriteFieldBegin("model", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Model); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *IndexStrategy) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IndexStrategy(%+v)", *p)

}

type FilterStrategy struct {
	//filter pages
	FilterPage []int32 `thrift:"filter_page,1,optional" form:"filter_page" json:"filter_page,omitempty" query:"filter_page"`
}

func NewFilterStrategy() *FilterStrategy {
	return &FilterStrategy{}
}

func (p *FilterStrategy) InitDefault() {
}

var FilterStrategy_FilterPage_DEFAULT []int32

func (p *FilterStrategy) GetFilterPage() (v []int32) {
	if !p.IsSetFilterPage() {
		return FilterStrategy_FilterPage_DEFAULT
	}
	return p.FilterPage
}

var fieldIDToName_FilterStrategy = map[int16]string{
	1: "filter_page",
}

func (p *FilterStrategy) IsSetFilterPage() bool {
	return p.FilterPage != nil
}

func (p *FilterStrategy) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FilterStrategy[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *FilterStrategy) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]int32, 0, size)
	for i := 0; i < size; i++ {

		var _elem int32
		if v, err := iprot.ReadI32(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.FilterPage = _field
	return nil
}

func (p *FilterStrategy) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("FilterStrategy"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *FilterStrategy) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetFilterPage() {
		if err = oprot.WriteFieldBegin("filter_page", thrift.LIST, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.FilterPage)); err != nil {
			return err
		}
		for _, v := range p.FilterPage {
			if err := oprot.WriteI32(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *FilterStrategy) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FilterStrategy(%+v)", *p)

}

type SinkStrategy struct {
	// Check whether the index was successful
	CheckIndex bool `thrift:"check_index,1" form:"check_index" json:"check_index" query:"check_index"`
}

func NewSinkStrategy() *SinkStrategy {
	return &SinkStrategy{}
}

func (p *SinkStrategy) InitDefault() {
}

func (p *SinkStrategy) GetCheckIndex() (v bool) {
	return p.CheckIndex
}

var fieldIDToName_SinkStrategy = map[int16]string{
	1: "check_index",
}

func (p *SinkStrategy) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SinkStrategy[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *SinkStrategy) ReadField1(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CheckIndex = _field
	return nil
}

func (p *SinkStrategy) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("SinkStrategy"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SinkStrategy) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("check_index", thrift.BOOL, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.CheckIndex); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *SinkStrategy) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SinkStrategy(%+v)", *p)

}
