# pdf 文献总结

from pathlib import Path
from openai import OpenAI
from .routes import analyze_app
from flask import request, jsonify
import logging
import requests
import tempfile
import os

logger = logging.getLogger(__name__)

client = OpenAI(
    api_key="sk-2uJkXJefLIepfEq5KjFIC0NM90bkWZE4oQYpYYZBL4NH8gDK",  # 在这里将 MOONSHOT_API_KEY 替换为你从 Kimi 开放平台申请的 API Key
    base_url="https://api.moonshot.cn/v1",
)


def download_file(url: str) -> str:
    """
    从URL下载文件到临时目录
    
    Args:
        url: 文件的URL地址
        
    Returns:
        str: 临时文件的路径
    """
    try:
        # 发送GET请求下载文件
        response = requests.get(url)
        response.raise_for_status()  # 确保请求成功
        
        # 创建临时文件并写入内容
        tmp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.pdf')
        tmp_file.write(response.content)
        tmp_file.close()
        
        return tmp_file.name
    except Exception as e:
        logger.error(f"下载文件失败: {str(e)}")
        raise


@analyze_app.route("/literature_analyze", methods=["POST"])
def handle_literature_analyze():
    """处理文献总结请求"""
    try:
        logger.info("收到文献总结请求")

        # 获取请求数据
        data = request.get_json()
        if not data or 'file_url' not in data:
            return jsonify({"error": "缺少必要的file_url参数"}), 400
            
        file_url = data.get("file_url")
        
        # 下载文件
        logger.info(f"开始下载文件: {file_url}")
        local_file_path = download_file(file_url)
        
        try:
            # 上传文件到OpenAI
            logger.info("开始上传文件到OpenAI")
            file_object = client.files.create(
                file=Path(local_file_path),
                purpose="file-extract"
            )
            
            # 获取文件内容
            file_content = client.files.content(file_id=file_object.id).text
            
            # 把文件内容通过系统提示词 system prompt 放进请求中
            messages = [
                {"role": "user", "content": "请简单介绍 moonshot.pdf 的具体内容"},
                {
                    "role": "system",
                    "content": file_content,  # <-- 这里，我们将抽取后的文件内容（注意是文件内容，而不是文件 ID）放置在请求中
                },
            ]

            # 然后调用 chat-completion, 获取 Kimi 的回答
            completion = client.chat.completions.create(
                model="moonshot-v1-32k",
                messages=messages,
                temperature=0.3,
            )

            print(completion.choices[0].message)
            
            return jsonify({
                "success": True,
                "content": file_content
            })
            
        finally:
            # 清理临时文件
            try:
                os.unlink(local_file_path)
                logger.info("临时文件已清理")
            except Exception as e:
                logger.error(f"清理临时文件失败: {str(e)}")
                
    except Exception as e:
        logger.error(f"处理文献总结请求失败: {str(e)}")
        return jsonify({"error": str(e)}), 500
