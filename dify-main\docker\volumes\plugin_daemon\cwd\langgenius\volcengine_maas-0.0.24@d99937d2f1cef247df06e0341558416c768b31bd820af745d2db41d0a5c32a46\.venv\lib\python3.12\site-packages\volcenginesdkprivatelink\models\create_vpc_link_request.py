# coding: utf-8

"""
    privatelink

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateVpcLinkRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'chain_role_trn': 'list[str]',
        'client_token': 'str',
        'description': 'str',
        'private_ip_address_count': 'int',
        'private_link_gateway_id': 'str',
        'resource_id': 'str',
        'security_group_ids': 'list[str]',
        'service_code': 'str',
        'subnet_id': 'str',
        'vpc_id': 'str',
        'vpc_link_name': 'str'
    }

    attribute_map = {
        'chain_role_trn': 'ChainRoleTrn',
        'client_token': 'ClientToken',
        'description': 'Description',
        'private_ip_address_count': 'PrivateIpAddressCount',
        'private_link_gateway_id': 'PrivateLinkGatewayId',
        'resource_id': 'ResourceId',
        'security_group_ids': 'SecurityGroupIds',
        'service_code': 'ServiceCode',
        'subnet_id': 'SubnetId',
        'vpc_id': 'VpcId',
        'vpc_link_name': 'VpcLinkName'
    }

    def __init__(self, chain_role_trn=None, client_token=None, description=None, private_ip_address_count=None, private_link_gateway_id=None, resource_id=None, security_group_ids=None, service_code=None, subnet_id=None, vpc_id=None, vpc_link_name=None, _configuration=None):  # noqa: E501
        """CreateVpcLinkRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._chain_role_trn = None
        self._client_token = None
        self._description = None
        self._private_ip_address_count = None
        self._private_link_gateway_id = None
        self._resource_id = None
        self._security_group_ids = None
        self._service_code = None
        self._subnet_id = None
        self._vpc_id = None
        self._vpc_link_name = None
        self.discriminator = None

        if chain_role_trn is not None:
            self.chain_role_trn = chain_role_trn
        if client_token is not None:
            self.client_token = client_token
        if description is not None:
            self.description = description
        if private_ip_address_count is not None:
            self.private_ip_address_count = private_ip_address_count
        self.private_link_gateway_id = private_link_gateway_id
        if resource_id is not None:
            self.resource_id = resource_id
        if security_group_ids is not None:
            self.security_group_ids = security_group_ids
        if service_code is not None:
            self.service_code = service_code
        self.subnet_id = subnet_id
        self.vpc_id = vpc_id
        if vpc_link_name is not None:
            self.vpc_link_name = vpc_link_name

    @property
    def chain_role_trn(self):
        """Gets the chain_role_trn of this CreateVpcLinkRequest.  # noqa: E501


        :return: The chain_role_trn of this CreateVpcLinkRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._chain_role_trn

    @chain_role_trn.setter
    def chain_role_trn(self, chain_role_trn):
        """Sets the chain_role_trn of this CreateVpcLinkRequest.


        :param chain_role_trn: The chain_role_trn of this CreateVpcLinkRequest.  # noqa: E501
        :type: list[str]
        """

        self._chain_role_trn = chain_role_trn

    @property
    def client_token(self):
        """Gets the client_token of this CreateVpcLinkRequest.  # noqa: E501


        :return: The client_token of this CreateVpcLinkRequest.  # noqa: E501
        :rtype: str
        """
        return self._client_token

    @client_token.setter
    def client_token(self, client_token):
        """Sets the client_token of this CreateVpcLinkRequest.


        :param client_token: The client_token of this CreateVpcLinkRequest.  # noqa: E501
        :type: str
        """

        self._client_token = client_token

    @property
    def description(self):
        """Gets the description of this CreateVpcLinkRequest.  # noqa: E501


        :return: The description of this CreateVpcLinkRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CreateVpcLinkRequest.


        :param description: The description of this CreateVpcLinkRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def private_ip_address_count(self):
        """Gets the private_ip_address_count of this CreateVpcLinkRequest.  # noqa: E501


        :return: The private_ip_address_count of this CreateVpcLinkRequest.  # noqa: E501
        :rtype: int
        """
        return self._private_ip_address_count

    @private_ip_address_count.setter
    def private_ip_address_count(self, private_ip_address_count):
        """Sets the private_ip_address_count of this CreateVpcLinkRequest.


        :param private_ip_address_count: The private_ip_address_count of this CreateVpcLinkRequest.  # noqa: E501
        :type: int
        """

        self._private_ip_address_count = private_ip_address_count

    @property
    def private_link_gateway_id(self):
        """Gets the private_link_gateway_id of this CreateVpcLinkRequest.  # noqa: E501


        :return: The private_link_gateway_id of this CreateVpcLinkRequest.  # noqa: E501
        :rtype: str
        """
        return self._private_link_gateway_id

    @private_link_gateway_id.setter
    def private_link_gateway_id(self, private_link_gateway_id):
        """Sets the private_link_gateway_id of this CreateVpcLinkRequest.


        :param private_link_gateway_id: The private_link_gateway_id of this CreateVpcLinkRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and private_link_gateway_id is None:
            raise ValueError("Invalid value for `private_link_gateway_id`, must not be `None`")  # noqa: E501

        self._private_link_gateway_id = private_link_gateway_id

    @property
    def resource_id(self):
        """Gets the resource_id of this CreateVpcLinkRequest.  # noqa: E501


        :return: The resource_id of this CreateVpcLinkRequest.  # noqa: E501
        :rtype: str
        """
        return self._resource_id

    @resource_id.setter
    def resource_id(self, resource_id):
        """Sets the resource_id of this CreateVpcLinkRequest.


        :param resource_id: The resource_id of this CreateVpcLinkRequest.  # noqa: E501
        :type: str
        """

        self._resource_id = resource_id

    @property
    def security_group_ids(self):
        """Gets the security_group_ids of this CreateVpcLinkRequest.  # noqa: E501


        :return: The security_group_ids of this CreateVpcLinkRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._security_group_ids

    @security_group_ids.setter
    def security_group_ids(self, security_group_ids):
        """Sets the security_group_ids of this CreateVpcLinkRequest.


        :param security_group_ids: The security_group_ids of this CreateVpcLinkRequest.  # noqa: E501
        :type: list[str]
        """

        self._security_group_ids = security_group_ids

    @property
    def service_code(self):
        """Gets the service_code of this CreateVpcLinkRequest.  # noqa: E501


        :return: The service_code of this CreateVpcLinkRequest.  # noqa: E501
        :rtype: str
        """
        return self._service_code

    @service_code.setter
    def service_code(self, service_code):
        """Sets the service_code of this CreateVpcLinkRequest.


        :param service_code: The service_code of this CreateVpcLinkRequest.  # noqa: E501
        :type: str
        """

        self._service_code = service_code

    @property
    def subnet_id(self):
        """Gets the subnet_id of this CreateVpcLinkRequest.  # noqa: E501


        :return: The subnet_id of this CreateVpcLinkRequest.  # noqa: E501
        :rtype: str
        """
        return self._subnet_id

    @subnet_id.setter
    def subnet_id(self, subnet_id):
        """Sets the subnet_id of this CreateVpcLinkRequest.


        :param subnet_id: The subnet_id of this CreateVpcLinkRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and subnet_id is None:
            raise ValueError("Invalid value for `subnet_id`, must not be `None`")  # noqa: E501

        self._subnet_id = subnet_id

    @property
    def vpc_id(self):
        """Gets the vpc_id of this CreateVpcLinkRequest.  # noqa: E501


        :return: The vpc_id of this CreateVpcLinkRequest.  # noqa: E501
        :rtype: str
        """
        return self._vpc_id

    @vpc_id.setter
    def vpc_id(self, vpc_id):
        """Sets the vpc_id of this CreateVpcLinkRequest.


        :param vpc_id: The vpc_id of this CreateVpcLinkRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and vpc_id is None:
            raise ValueError("Invalid value for `vpc_id`, must not be `None`")  # noqa: E501

        self._vpc_id = vpc_id

    @property
    def vpc_link_name(self):
        """Gets the vpc_link_name of this CreateVpcLinkRequest.  # noqa: E501


        :return: The vpc_link_name of this CreateVpcLinkRequest.  # noqa: E501
        :rtype: str
        """
        return self._vpc_link_name

    @vpc_link_name.setter
    def vpc_link_name(self, vpc_link_name):
        """Sets the vpc_link_name of this CreateVpcLinkRequest.


        :param vpc_link_name: The vpc_link_name of this CreateVpcLinkRequest.  # noqa: E501
        :type: str
        """

        self._vpc_link_name = vpc_link_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateVpcLinkRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateVpcLinkRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateVpcLinkRequest):
            return True

        return self.to_dict() != other.to_dict()
