2025/08/04-13:59:45.696672 44 RocksDB version: 6.29.5
2025/08/04-13:59:45.697463 44 Git sha 0
2025/08/04-13:59:45.697472 44 Compile date 2024-11-15 11:22:58
2025/08/04-13:59:45.697483 44 DB SUMMARY
2025/08/04-13:59:45.697485 44 DB Session ID:  IIU5CSDATBFFQMWH79LH
2025/08/04-13:59:45.700428 44 CURRENT file:  CURRENT
2025/08/04-13:59:45.700458 44 IDENTITY file:  IDENTITY
2025/08/04-13:59:45.701408 44 MANIFEST file:  MANIFEST-000047 size: 423 Bytes
2025/08/04-13:59:45.701425 44 SST files in /var/lib/milvus/rdb_data_meta_kv dir, Total Num: 3, files: 000009.sst 000040.sst 000046.sst 
2025/08/04-13:59:45.701433 44 Write Ahead Log file in /var/lib/milvus/rdb_data_meta_kv: 000048.log size: 139636 ; 
2025/08/04-13:59:45.701439 44                         Options.error_if_exists: 0
2025/08/04-13:59:45.701441 44                       Options.create_if_missing: 1
2025/08/04-13:59:45.701442 44                         Options.paranoid_checks: 1
2025/08/04-13:59:45.701443 44             Options.flush_verify_memtable_count: 1
2025/08/04-13:59:45.701444 44                               Options.track_and_verify_wals_in_manifest: 0
2025/08/04-13:59:45.701445 44                                     Options.env: 0x7f0d0b088d00
2025/08/04-13:59:45.701447 44                                      Options.fs: PosixFileSystem
2025/08/04-13:59:45.701448 44                                Options.info_log: 0x7f0c2ee90050
2025/08/04-13:59:45.701449 44                Options.max_file_opening_threads: 16
2025/08/04-13:59:45.701450 44                              Options.statistics: (nil)
2025/08/04-13:59:45.701451 44                               Options.use_fsync: 0
2025/08/04-13:59:45.701452 44                       Options.max_log_file_size: 0
2025/08/04-13:59:45.701453 44                  Options.max_manifest_file_size: 1073741824
2025/08/04-13:59:45.701455 44                   Options.log_file_time_to_roll: 0
2025/08/04-13:59:45.701456 44                       Options.keep_log_file_num: 1000
2025/08/04-13:59:45.701457 44                    Options.recycle_log_file_num: 0
2025/08/04-13:59:45.701458 44                         Options.allow_fallocate: 1
2025/08/04-13:59:45.701459 44                        Options.allow_mmap_reads: 0
2025/08/04-13:59:45.701460 44                       Options.allow_mmap_writes: 0
2025/08/04-13:59:45.701461 44                        Options.use_direct_reads: 0
2025/08/04-13:59:45.701461 44                        Options.use_direct_io_for_flush_and_compaction: 0
2025/08/04-13:59:45.701463 44          Options.create_missing_column_families: 0
2025/08/04-13:59:45.701463 44                              Options.db_log_dir: 
2025/08/04-13:59:45.701465 44                                 Options.wal_dir: 
2025/08/04-13:59:45.701466 44                Options.table_cache_numshardbits: 6
2025/08/04-13:59:45.701467 44                         Options.WAL_ttl_seconds: 0
2025/08/04-13:59:45.701468 44                       Options.WAL_size_limit_MB: 0
2025/08/04-13:59:45.701469 44                        Options.max_write_batch_group_size_bytes: 1048576
2025/08/04-13:59:45.701470 44             Options.manifest_preallocation_size: 4194304
2025/08/04-13:59:45.701471 44                     Options.is_fd_close_on_exec: 1
2025/08/04-13:59:45.701472 44                   Options.advise_random_on_open: 1
2025/08/04-13:59:45.701473 44                   Options.experimental_mempurge_threshold: 0.000000
2025/08/04-13:59:45.701490 44                    Options.db_write_buffer_size: 0
2025/08/04-13:59:45.701492 44                    Options.write_buffer_manager: 0x7f0c312400a0
2025/08/04-13:59:45.701493 44         Options.access_hint_on_compaction_start: 1
2025/08/04-13:59:45.701494 44  Options.new_table_reader_for_compaction_inputs: 0
2025/08/04-13:59:45.701495 44           Options.random_access_max_buffer_size: 1048576
2025/08/04-13:59:45.701496 44                      Options.use_adaptive_mutex: 0
2025/08/04-13:59:45.701497 44                            Options.rate_limiter: (nil)
2025/08/04-13:59:45.701548 44     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/08/04-13:59:45.701555 44                       Options.wal_recovery_mode: 2
2025/08/04-13:59:45.702551 44                  Options.enable_thread_tracking: 0
2025/08/04-13:59:45.702562 44                  Options.enable_pipelined_write: 0
2025/08/04-13:59:45.702564 44                  Options.unordered_write: 0
2025/08/04-13:59:45.702565 44         Options.allow_concurrent_memtable_write: 1
2025/08/04-13:59:45.702566 44      Options.enable_write_thread_adaptive_yield: 1
2025/08/04-13:59:45.702566 44             Options.write_thread_max_yield_usec: 100
2025/08/04-13:59:45.702567 44            Options.write_thread_slow_yield_usec: 3
2025/08/04-13:59:45.702568 44                               Options.row_cache: None
2025/08/04-13:59:45.702569 44                              Options.wal_filter: None
2025/08/04-13:59:45.702571 44             Options.avoid_flush_during_recovery: 0
2025/08/04-13:59:45.702572 44             Options.allow_ingest_behind: 0
2025/08/04-13:59:45.702572 44             Options.preserve_deletes: 0
2025/08/04-13:59:45.702573 44             Options.two_write_queues: 0
2025/08/04-13:59:45.702574 44             Options.manual_wal_flush: 0
2025/08/04-13:59:45.702575 44             Options.atomic_flush: 0
2025/08/04-13:59:45.702576 44             Options.avoid_unnecessary_blocking_io: 0
2025/08/04-13:59:45.702576 44                 Options.persist_stats_to_disk: 0
2025/08/04-13:59:45.702577 44                 Options.write_dbid_to_manifest: 0
2025/08/04-13:59:45.702578 44                 Options.log_readahead_size: 0
2025/08/04-13:59:45.702579 44                 Options.file_checksum_gen_factory: Unknown
2025/08/04-13:59:45.702580 44                 Options.best_efforts_recovery: 0
2025/08/04-13:59:45.702580 44                Options.max_bgerror_resume_count: 2147483647
2025/08/04-13:59:45.702581 44            Options.bgerror_resume_retry_interval: 1000000
2025/08/04-13:59:45.702582 44             Options.allow_data_in_errors: 0
2025/08/04-13:59:45.702583 44             Options.db_host_id: __hostname__
2025/08/04-13:59:45.702589 44             Options.max_background_jobs: 2
2025/08/04-13:59:45.702590 44             Options.max_background_compactions: -1
2025/08/04-13:59:45.702591 44             Options.max_subcompactions: 1
2025/08/04-13:59:45.702592 44             Options.avoid_flush_during_shutdown: 0
2025/08/04-13:59:45.702593 44           Options.writable_file_max_buffer_size: 1048576
2025/08/04-13:59:45.702594 44             Options.delayed_write_rate : 16777216
2025/08/04-13:59:45.702595 44             Options.max_total_wal_size: 0
2025/08/04-13:59:45.702596 44             Options.delete_obsolete_files_period_micros: 21600000000
2025/08/04-13:59:45.702597 44                   Options.stats_dump_period_sec: 600
2025/08/04-13:59:45.702598 44                 Options.stats_persist_period_sec: 600
2025/08/04-13:59:45.702599 44                 Options.stats_history_buffer_size: 1048576
2025/08/04-13:59:45.702600 44                          Options.max_open_files: -1
2025/08/04-13:59:45.702601 44                          Options.bytes_per_sync: 0
2025/08/04-13:59:45.702602 44                      Options.wal_bytes_per_sync: 0
2025/08/04-13:59:45.702602 44                   Options.strict_bytes_per_sync: 0
2025/08/04-13:59:45.702603 44       Options.compaction_readahead_size: 0
2025/08/04-13:59:45.702604 44                  Options.max_background_flushes: 1
2025/08/04-13:59:45.702605 44 Compression algorithms supported:
2025/08/04-13:59:45.702609 44 	kZSTD supported: 1
2025/08/04-13:59:45.702610 44 	kXpressCompression supported: 0
2025/08/04-13:59:45.702611 44 	kBZip2Compression supported: 0
2025/08/04-13:59:45.702612 44 	kZSTDNotFinalCompression supported: 1
2025/08/04-13:59:45.702614 44 	kLZ4Compression supported: 0
2025/08/04-13:59:45.702614 44 	kZlibCompression supported: 0
2025/08/04-13:59:45.702615 44 	kLZ4HCCompression supported: 0
2025/08/04-13:59:45.702616 44 	kSnappyCompression supported: 0
2025/08/04-13:59:45.702624 44 Fast CRC32 supported: Not supported on x86
2025/08/04-13:59:45.709751 44 [db/version_set.cc:4888] Recovering from manifest file: /var/lib/milvus/rdb_data_meta_kv/MANIFEST-000047
2025/08/04-13:59:45.711779 44 [db/column_family.cc:605] --------------- Options for column family [default]:
2025/08/04-13:59:45.711789 44               Options.comparator: leveldb.BytewiseComparator
2025/08/04-13:59:45.711790 44           Options.merge_operator: None
2025/08/04-13:59:45.711791 44        Options.compaction_filter: None
2025/08/04-13:59:45.711792 44        Options.compaction_filter_factory: None
2025/08/04-13:59:45.711792 44  Options.sst_partitioner_factory: None
2025/08/04-13:59:45.711793 44         Options.memtable_factory: SkipListFactory
2025/08/04-13:59:45.711794 44            Options.table_factory: BlockBasedTable
2025/08/04-13:59:45.711820 44            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f0c313000c0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0x7f0c31240010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 1001872834
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: (nil)
  persistent_cache: (nil)
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2025/08/04-13:59:45.711821 44        Options.write_buffer_size: 67108864
2025/08/04-13:59:45.711821 44  Options.max_write_buffer_number: 2
2025/08/04-13:59:45.711824 44        Options.compression[0]: NoCompression
2025/08/04-13:59:45.711824 44        Options.compression[1]: NoCompression
2025/08/04-13:59:45.711825 44        Options.compression[2]: ZSTD
2025/08/04-13:59:45.711826 44        Options.compression[3]: ZSTD
2025/08/04-13:59:45.711826 44        Options.compression[4]: ZSTD
2025/08/04-13:59:45.711827 44                  Options.bottommost_compression: Disabled
2025/08/04-13:59:45.711828 44       Options.prefix_extractor: nullptr
2025/08/04-13:59:45.711828 44   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/04-13:59:45.711829 44             Options.num_levels: 5
2025/08/04-13:59:45.711829 44        Options.min_write_buffer_number_to_merge: 1
2025/08/04-13:59:45.711830 44     Options.max_write_buffer_number_to_maintain: 0
2025/08/04-13:59:45.711831 44     Options.max_write_buffer_size_to_maintain: 0
2025/08/04-13:59:45.711831 44            Options.bottommost_compression_opts.window_bits: -14
2025/08/04-13:59:45.711832 44                  Options.bottommost_compression_opts.level: 32767
2025/08/04-13:59:45.711832 44               Options.bottommost_compression_opts.strategy: 0
2025/08/04-13:59:45.711833 44         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/04-13:59:45.711834 44         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/04-13:59:45.711834 44         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/04-13:59:45.711835 44                  Options.bottommost_compression_opts.enabled: false
2025/08/04-13:59:45.711835 44         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/04-13:59:45.711836 44            Options.compression_opts.window_bits: -14
2025/08/04-13:59:45.711837 44                  Options.compression_opts.level: 32767
2025/08/04-13:59:45.711837 44               Options.compression_opts.strategy: 0
2025/08/04-13:59:45.711838 44         Options.compression_opts.max_dict_bytes: 0
2025/08/04-13:59:45.711839 44         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/04-13:59:45.711998 44         Options.compression_opts.parallel_threads: 1
2025/08/04-13:59:45.712003 44                  Options.compression_opts.enabled: false
2025/08/04-13:59:45.712004 44         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/04-13:59:45.712005 44      Options.level0_file_num_compaction_trigger: 4
2025/08/04-13:59:45.712005 44          Options.level0_slowdown_writes_trigger: 20
2025/08/04-13:59:45.712006 44              Options.level0_stop_writes_trigger: 36
2025/08/04-13:59:45.712007 44                   Options.target_file_size_base: 67108864
2025/08/04-13:59:45.712007 44             Options.target_file_size_multiplier: 2
2025/08/04-13:59:45.712008 44                Options.max_bytes_for_level_base: 268435456
2025/08/04-13:59:45.712009 44 Options.level_compaction_dynamic_level_bytes: 0
2025/08/04-13:59:45.712009 44          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/04-13:59:45.712012 44 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/04-13:59:45.712013 44 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/04-13:59:45.712014 44 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/04-13:59:45.712014 44 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/04-13:59:45.712015 44 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/04-13:59:45.712015 44 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/04-13:59:45.712016 44 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/04-13:59:45.712017 44       Options.max_sequential_skip_in_iterations: 8
2025/08/04-13:59:45.712017 44                    Options.max_compaction_bytes: 1677721600
2025/08/04-13:59:45.712018 44                        Options.arena_block_size: 1048576
2025/08/04-13:59:45.712018 44   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/04-13:59:45.712019 44   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/04-13:59:45.712020 44       Options.rate_limit_delay_max_milliseconds: 100
2025/08/04-13:59:45.712020 44                Options.disable_auto_compactions: 0
2025/08/04-13:59:45.712023 44                        Options.compaction_style: kCompactionStyleLevel
2025/08/04-13:59:45.712024 44                          Options.compaction_pri: kMinOverlappingRatio
2025/08/04-13:59:45.712025 44 Options.compaction_options_universal.size_ratio: 1
2025/08/04-13:59:45.712025 44 Options.compaction_options_universal.min_merge_width: 2
2025/08/04-13:59:45.712026 44 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/04-13:59:45.712027 44 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/04-13:59:45.712027 44 Options.compaction_options_universal.compression_size_percent: -1
2025/08/04-13:59:45.712029 44 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/04-13:59:45.712029 44 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/04-13:59:45.712030 44 Options.compaction_options_fifo.allow_compaction: 0
2025/08/04-13:59:45.712036 44                   Options.table_properties_collectors: 
2025/08/04-13:59:45.712036 44                   Options.inplace_update_support: 0
2025/08/04-13:59:45.712037 44                 Options.inplace_update_num_locks: 10000
2025/08/04-13:59:45.712038 44               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/04-13:59:45.712039 44               Options.memtable_whole_key_filtering: 0
2025/08/04-13:59:45.712039 44   Options.memtable_huge_page_size: 0
2025/08/04-13:59:45.712040 44                           Options.bloom_locality: 0
2025/08/04-13:59:45.712040 44                    Options.max_successive_merges: 0
2025/08/04-13:59:45.712041 44                Options.optimize_filters_for_hits: 0
2025/08/04-13:59:45.712042 44                Options.paranoid_file_checks: 0
2025/08/04-13:59:45.712042 44                Options.force_consistency_checks: 1
2025/08/04-13:59:45.712043 44                Options.report_bg_io_stats: 0
2025/08/04-13:59:45.712043 44                               Options.ttl: 2592000
2025/08/04-13:59:45.712290 44          Options.periodic_compaction_seconds: 0
2025/08/04-13:59:45.712293 44                       Options.enable_blob_files: false
2025/08/04-13:59:45.712294 44                           Options.min_blob_size: 0
2025/08/04-13:59:45.712295 44                          Options.blob_file_size: 268435456
2025/08/04-13:59:45.712296 44                   Options.blob_compression_type: NoCompression
2025/08/04-13:59:45.712297 44          Options.enable_blob_garbage_collection: false
2025/08/04-13:59:45.712298 44      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/04-13:59:45.712300 44 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/04-13:59:45.712301 44          Options.blob_compaction_readahead_size: 0
2025/08/04-13:59:45.720324 44 [db/version_set.cc:4928] Recovered from manifest file:/var/lib/milvus/rdb_data_meta_kv/MANIFEST-000047 succeeded,manifest_file_number is 47, next_file_number is 49, last_sequence is 6944, log_number is 43,prev_log_number is 0,max_column_family is 0,min_log_number_to_keep is 0
2025/08/04-13:59:45.720338 44 [db/version_set.cc:4943] Column family [default] (ID 0), log number is 43
2025/08/04-13:59:45.724644 44 [db/version_set.cc:4409] Creating manifest 51
2025/08/04-13:59:45.763207 44 EVENT_LOG_v1 {"time_micros": 1754315985763188, "job": 1, "event": "recovery_started", "wal_files": [48]}
2025/08/04-13:59:45.763215 44 [db/db_impl/db_impl_open.cc:888] Recovering log #48 mode 2
2025/08/04-13:59:45.776726 44 EVENT_LOG_v1 {"time_micros": 1754315985776689, "cf_name": "default", "job": 1, "event": "table_file_creation", "file_number": 52, "file_size": 1031, "file_checksum": "", "file_checksum_func_name": "Unknown", "table_properties": {"data_size": 65, "index_size": 52, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 43, "raw_average_key_size": 43, "raw_value_size": 6, "raw_average_value_size": 6, "num_data_blocks": 1, "num_entries": 1, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; ", "creation_time": 1754315985, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "5d84d4a4-9f27-46dd-b1e4-572f86de492f", "db_session_id": "IIU5CSDATBFFQMWH79LH", "orig_file_number": 52}}
2025/08/04-13:59:45.776905 44 [db/version_set.cc:4409] Creating manifest 53
2025/08/04-13:59:45.797835 44 EVENT_LOG_v1 {"time_micros": 1754315985797828, "job": 1, "event": "recovery_finished"}
2025/08/04-13:59:45.824165 44 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data_meta_kv/000048.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/08/04-13:59:45.824509 44 [db/db_impl/db_impl_open.cc:1820] SstFileManager instance 0x7f0c2e300000
2025/08/04-13:59:45.826346 44 DB pointer 0x7f0c2ee20000
2025/08/04-13:59:45.827711 46 [db/compaction/compaction_job.cc:2331] [default] [JOB 3] Compacting 4@0 files to L1, score 1.00
2025/08/04-13:59:45.827731 46 [db/compaction/compaction_job.cc:2337] [default] Compaction start summary: Base version 3 Base level 0, inputs: [52(1031B) 46(1031B) 40(1057B) 9(1733B)]
2025/08/04-13:59:45.827785 46 EVENT_LOG_v1 {"time_micros": 1754315985827746, "job": 3, "event": "compaction_started", "compaction_reason": "LevelL0FilesNum", "files_L0": [52, 46, 40, 9], "score": 1, "input_data_size": 4852}
2025/08/04-13:59:45.827907 64 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/04-13:59:45.827929 64 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 0.1 total, 0.1 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      4/4    4.74 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.1      0.01              0.00         1    0.007       0      0       0.0       0.0
 Sum      4/4    4.74 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.1      0.01              0.00         1    0.007       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.1      0.01              0.00         1    0.007       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.1      0.01              0.00         1    0.007       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 0.1 total, 0.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.01 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.01 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f0c31240010#8 capacity: 955.46 MB collections: 1 last_copies: 0 last_secs: 7.2e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/08/04-13:59:45.836402 46 [db/compaction/compaction_job.cc:1937] [default] [JOB 3] Generated table #57: 34 keys, 1763 bytes
2025/08/04-13:59:45.836451 46 EVENT_LOG_v1 {"time_micros": 1754315985836425, "cf_name": "default", "job": 3, "event": "table_file_creation", "file_number": 57, "file_size": 1763, "file_checksum": "", "file_checksum_func_name": "Unknown", "table_properties": {"data_size": 773, "index_size": 49, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 1402, "raw_average_key_size": 41, "raw_value_size": 194, "raw_average_value_size": 5, "num_data_blocks": 1, "num_entries": 34, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; ", "creation_time": 1754269138, "oldest_key_time": 0, "file_creation_time": 1754315985, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "5d84d4a4-9f27-46dd-b1e4-572f86de492f", "db_session_id": "IIU5CSDATBFFQMWH79LH", "orig_file_number": 57}}
2025/08/04-13:59:45.841843 46 [db/compaction/compaction_job.cc:1998] [default] [JOB 3] Compacted 4@0 files to L1 => 1763 bytes
2025/08/04-13:59:45.845850 46 (Original Log Time 2025/08/04-13:59:45.845286) [db/compaction/compaction_job.cc:944] [default] compacted to: files[0 1 0 0 0] max score 0.00, MB/sec: 0.5 rd, 0.2 wr, level 1, files in(4, 0) out(1 +0 blob) MB in(0.0, 0.0 +0.0 blob) out(0.0 +0.0 blob), read-write-amplify(1.4) write-amplify(0.4) OK, records in: 38, records dropped: 4 output_compression: NoCompression
2025/08/04-13:59:45.845855 46 (Original Log Time 2025/08/04-13:59:45.845784) EVENT_LOG_v1 {"time_micros": 1754315985845766, "job": 3, "event": "compaction_finished", "compaction_time_micros": 9402, "compaction_time_cpu_micros": 1659, "output_level": 1, "num_output_files": 1, "total_output_size": 1763, "num_input_records": 38, "num_output_records": 34, "num_subcompactions": 1, "output_compression": "NoCompression", "num_single_delete_mismatches": 0, "num_single_delete_fallthrough": 0, "lsm_state": [0, 1, 0, 0, 0]}
2025/08/04-13:59:45.847202 46 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data_meta_kv/000052.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/08/04-13:59:45.847607 46 EVENT_LOG_v1 {"time_micros": 1754315985847595, "job": 3, "event": "table_file_deletion", "file_number": 52}
2025/08/04-13:59:45.849261 46 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data_meta_kv/000046.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/08/04-13:59:45.849295 46 EVENT_LOG_v1 {"time_micros": 1754315985849289, "job": 3, "event": "table_file_deletion", "file_number": 46}
2025/08/04-13:59:45.850670 46 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data_meta_kv/000040.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/08/04-13:59:45.850693 46 EVENT_LOG_v1 {"time_micros": 1754315985850689, "job": 3, "event": "table_file_deletion", "file_number": 40}
2025/08/04-13:59:45.852013 46 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data_meta_kv/000009.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/08/04-13:59:45.852025 46 EVENT_LOG_v1 {"time_micros": 1754315985852023, "job": 3, "event": "table_file_deletion", "file_number": 9}
2025/08/04-14:09:45.828573 64 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/04-14:09:45.829215 64 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 600.1 total, 600.0 interval
Cumulative writes: 5543 writes, 5543 keys, 5425 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 5543 writes, 0 syncs, 5543.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 5543 writes, 5543 keys, 5425 commit groups, 1.0 writes per commit group, ingest: 0.29 MB, 0.00 MB/s
Interval WAL: 5543 writes, 0 syncs, 5543.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.1      0.01              0.00         1    0.007       0      0       0.0       0.0
  L1      1/0    1.72 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.4      0.5      0.2      0.01              0.00         1    0.009      38      4       0.0       0.0
 Sum      1/0    1.72 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.7      0.3      0.2      0.02              0.00         2    0.008      38      4       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0 1763.0      0.5      0.2      0.01              0.00         1    0.009      38      4       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.5      0.2      0.01              0.00         1    0.009      38      4       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.1      0.01              0.00         1    0.007       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 600.1 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f0c31240010#8 capacity: 955.46 MB collections: 2 last_copies: 0 last_secs: 0.00014 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(7,295.42 KB,0.0301947%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/08/04-14:19:45.829525 64 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/04-14:19:45.830012 64 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 1200.1 total, 600.0 interval
Cumulative writes: 11K writes, 11K keys, 11K commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 11K writes, 0 syncs, 11541.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 5998 writes, 5998 keys, 5853 commit groups, 1.0 writes per commit group, ingest: 0.32 MB, 0.00 MB/s
Interval WAL: 5998 writes, 0 syncs, 5998.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.1      0.01              0.00         1    0.007       0      0       0.0       0.0
  L1      1/0    1.72 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.4      0.5      0.2      0.01              0.00         1    0.009      38      4       0.0       0.0
 Sum      1/0    1.72 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.7      0.3      0.2      0.02              0.00         2    0.008      38      4       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.5      0.2      0.01              0.00         1    0.009      38      4       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.1      0.01              0.00         1    0.007       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 1200.1 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f0c31240010#8 capacity: 955.46 MB collections: 3 last_copies: 0 last_secs: 5e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(7,295.42 KB,0.0301947%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/08/04-14:29:45.830534 64 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/04-14:29:45.830845 64 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 1800.1 total, 600.0 interval
Cumulative writes: 17K writes, 17K keys, 17K commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 17K writes, 0 syncs, 17525.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 5984 writes, 5984 keys, 5843 commit groups, 1.0 writes per commit group, ingest: 0.32 MB, 0.00 MB/s
Interval WAL: 5984 writes, 0 syncs, 5984.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.1      0.01              0.00         1    0.007       0      0       0.0       0.0
  L1      1/0    1.72 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.4      0.5      0.2      0.01              0.00         1    0.009      38      4       0.0       0.0
 Sum      1/0    1.72 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.7      0.3      0.2      0.02              0.00         2    0.008      38      4       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.5      0.2      0.01              0.00         1    0.009      38      4       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.1      0.01              0.00         1    0.007       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 1800.1 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f0c31240010#8 capacity: 955.46 MB collections: 4 last_copies: 0 last_secs: 8.6e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(7,295.42 KB,0.0301947%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/08/04-14:39:45.831108 64 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/04-14:39:45.831555 64 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 2400.1 total, 600.0 interval
Cumulative writes: 23K writes, 23K keys, 23K commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 23K writes, 0 syncs, 23523.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 5998 writes, 5998 keys, 5895 commit groups, 1.0 writes per commit group, ingest: 0.32 MB, 0.00 MB/s
Interval WAL: 5998 writes, 0 syncs, 5998.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.1      0.01              0.00         1    0.007       0      0       0.0       0.0
  L1      1/0    1.72 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.4      0.5      0.2      0.01              0.00         1    0.009      38      4       0.0       0.0
 Sum      1/0    1.72 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.7      0.3      0.2      0.02              0.00         2    0.008      38      4       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.5      0.2      0.01              0.00         1    0.009      38      4       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.1      0.01              0.00         1    0.007       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 2400.1 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f0c31240010#8 capacity: 955.46 MB collections: 5 last_copies: 0 last_secs: 7.3e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(7,295.42 KB,0.0301947%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/08/04-14:49:45.831849 64 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/04-14:49:45.832368 64 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 3000.1 total, 600.0 interval
Cumulative writes: 29K writes, 29K keys, 28K commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 29K writes, 0 syncs, 29515.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 5992 writes, 5992 keys, 5907 commit groups, 1.0 writes per commit group, ingest: 0.32 MB, 0.00 MB/s
Interval WAL: 5992 writes, 0 syncs, 5992.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.1      0.01              0.00         1    0.007       0      0       0.0       0.0
  L1      1/0    1.72 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.4      0.5      0.2      0.01              0.00         1    0.009      38      4       0.0       0.0
 Sum      1/0    1.72 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.7      0.3      0.2      0.02              0.00         2    0.008      38      4       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.5      0.2      0.01              0.00         1    0.009      38      4       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.1      0.01              0.00         1    0.007       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3000.1 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f0c31240010#8 capacity: 955.46 MB collections: 6 last_copies: 0 last_secs: 9.3e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(7,295.42 KB,0.0301947%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/08/04-14:59:45.832767 64 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/04-14:59:45.833073 64 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 3600.1 total, 600.0 interval
Cumulative writes: 35K writes, 35K keys, 34K commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 35K writes, 0 syncs, 35507.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 5992 writes, 5992 keys, 5858 commit groups, 1.0 writes per commit group, ingest: 0.32 MB, 0.00 MB/s
Interval WAL: 5992 writes, 0 syncs, 5992.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.1      0.01              0.00         1    0.007       0      0       0.0       0.0
  L1      1/0    1.72 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.4      0.5      0.2      0.01              0.00         1    0.009      38      4       0.0       0.0
 Sum      1/0    1.72 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.7      0.3      0.2      0.02              0.00         2    0.008      38      4       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.5      0.2      0.01              0.00         1    0.009      38      4       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.1      0.01              0.00         1    0.007       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3600.1 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f0c31240010#8 capacity: 955.46 MB collections: 7 last_copies: 0 last_secs: 0.000104 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(7,295.42 KB,0.0301947%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/08/04-15:09:45.833537 64 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/04-15:09:45.835022 64 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 4200.1 total, 600.0 interval
Cumulative writes: 41K writes, 41K keys, 40K commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 41K writes, 0 syncs, 41503.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 5996 writes, 5996 keys, 5766 commit groups, 1.0 writes per commit group, ingest: 0.32 MB, 0.00 MB/s
Interval WAL: 5996 writes, 0 syncs, 5996.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.1      0.01              0.00         1    0.007       0      0       0.0       0.0
  L1      1/0    1.72 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.4      0.5      0.2      0.01              0.00         1    0.009      38      4       0.0       0.0
 Sum      1/0    1.72 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.7      0.3      0.2      0.02              0.00         2    0.008      38      4       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.5      0.2      0.01              0.00         1    0.009      38      4       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.1      0.01              0.00         1    0.007       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 4200.1 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f0c31240010#8 capacity: 955.46 MB collections: 8 last_copies: 0 last_secs: 0.000145 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(7,295.42 KB,0.0301947%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/08/04-15:19:45.835971 64 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/04-15:19:45.836780 64 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 4800.1 total, 600.0 interval
Cumulative writes: 47K writes, 47K keys, 46K commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 47K writes, 0 syncs, 47503.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 6000 writes, 6000 keys, 5777 commit groups, 1.0 writes per commit group, ingest: 0.32 MB, 0.00 MB/s
Interval WAL: 6000 writes, 0 syncs, 6000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.1      0.01              0.00         1    0.007       0      0       0.0       0.0
  L1      1/0    1.72 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.4      0.5      0.2      0.01              0.00         1    0.009      38      4       0.0       0.0
 Sum      1/0    1.72 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.7      0.3      0.2      0.02              0.00         2    0.008      38      4       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.5      0.2      0.01              0.00         1    0.009      38      4       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.1      0.01              0.00         1    0.007       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 4800.1 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f0c31240010#8 capacity: 955.46 MB collections: 9 last_copies: 0 last_secs: 0.000258 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(7,295.42 KB,0.0301947%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/08/04-15:29:45.837449 64 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/04-15:29:45.837865 64 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 5400.1 total, 600.0 interval
Cumulative writes: 53K writes, 53K keys, 52K commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 53K writes, 0 syncs, 53503.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 6000 writes, 6000 keys, 5843 commit groups, 1.0 writes per commit group, ingest: 0.32 MB, 0.00 MB/s
Interval WAL: 6000 writes, 0 syncs, 6000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.1      0.01              0.00         1    0.007       0      0       0.0       0.0
  L1      1/0    1.72 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.4      0.5      0.2      0.01              0.00         1    0.009      38      4       0.0       0.0
 Sum      1/0    1.72 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.7      0.3      0.2      0.02              0.00         2    0.008      38      4       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.5      0.2      0.01              0.00         1    0.009      38      4       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.1      0.01              0.00         1    0.007       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 5400.1 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f0c31240010#8 capacity: 955.46 MB collections: 10 last_copies: 0 last_secs: 9.9e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(7,295.42 KB,0.0301947%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/08/04-15:39:45.838096 64 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/04-15:39:45.838468 64 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 6000.1 total, 600.0 interval
Cumulative writes: 59K writes, 59K keys, 57K commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 59K writes, 0 syncs, 59475.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 5972 writes, 5972 keys, 5811 commit groups, 1.0 writes per commit group, ingest: 0.32 MB, 0.00 MB/s
Interval WAL: 5972 writes, 0 syncs, 5972.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.1      0.01              0.00         1    0.007       0      0       0.0       0.0
  L1      1/0    1.72 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.4      0.5      0.2      0.01              0.00         1    0.009      38      4       0.0       0.0
 Sum      1/0    1.72 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.7      0.3      0.2      0.02              0.00         2    0.008      38      4       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.5      0.2      0.01              0.00         1    0.009      38      4       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.1      0.01              0.00         1    0.007       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 6000.1 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f0c31240010#8 capacity: 955.46 MB collections: 11 last_copies: 0 last_secs: 5.1e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(7,295.42 KB,0.0301947%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/08/04-15:49:45.838887 64 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/04-15:49:45.839233 64 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 6600.1 total, 600.0 interval
Cumulative writes: 65K writes, 65K keys, 63K commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 65K writes, 0 syncs, 65461.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 5986 writes, 5986 keys, 5826 commit groups, 1.0 writes per commit group, ingest: 0.32 MB, 0.00 MB/s
Interval WAL: 5986 writes, 0 syncs, 5986.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.1      0.01              0.00         1    0.007       0      0       0.0       0.0
  L1      1/0    1.72 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.4      0.5      0.2      0.01              0.00         1    0.009      38      4       0.0       0.0
 Sum      1/0    1.72 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.7      0.3      0.2      0.02              0.00         2    0.008      38      4       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.5      0.2      0.01              0.00         1    0.009      38      4       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.1      0.01              0.00         1    0.007       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 6600.1 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f0c31240010#8 capacity: 955.46 MB collections: 12 last_copies: 0 last_secs: 9.7e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(7,295.42 KB,0.0301947%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
