// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"gorm.io/gorm"
)

const TableNameWorkflowVersion = "workflow_version"

// WorkflowVersion Workflow Canvas Version Information Table, used to record canvas information for different versions
type WorkflowVersion struct {
	ID                 int64          `gorm:"column:id;primaryKey;autoIncrement:true;comment:ID" json:"id"`                                          // ID
	WorkflowID         int64          `gorm:"column:workflow_id;not null;comment:workflow id" json:"workflow_id"`                                    // workflow id
	Version            string         `gorm:"column:version;not null;comment:Published version" json:"version"`                                      // Published version
	VersionDescription string         `gorm:"column:version_description;not null;comment:Version Description" json:"version_description"`            // Version Description
	Canvas             string         `gorm:"column:canvas;not null;comment:Front end schema" json:"canvas"`                                         // Front end schema
	InputParams        string         `gorm:"column:input_params;comment:input params" json:"input_params"`                                          // input params
	OutputParams       string         `gorm:"column:output_params;comment:output params" json:"output_params"`                                       // output params
	CreatorID          int64          `gorm:"column:creator_id;not null;comment:creator id" json:"creator_id"`                                       // creator id
	CreatedAt          int64          `gorm:"column:created_at;not null;autoCreateTime:milli;comment:Create Time in Milliseconds" json:"created_at"` // Create Time in Milliseconds
	DeletedAt          gorm.DeletedAt `gorm:"column:deleted_at;comment:Delete Time" json:"deleted_at"`                                               // Delete Time
	CommitID           string         `gorm:"column:commit_id;not null;comment:the commit id corresponding to this version" json:"commit_id"`        // the commit id corresponding to this version
}

// TableName WorkflowVersion's table name
func (*WorkflowVersion) TableName() string {
	return TableNameWorkflowVersion
}
