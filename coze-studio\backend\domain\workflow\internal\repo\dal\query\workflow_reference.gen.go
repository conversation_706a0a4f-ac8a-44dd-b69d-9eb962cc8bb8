// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/coze-dev/coze-studio/backend/domain/workflow/internal/repo/dal/model"
)

func newWorkflowReference(db *gorm.DB, opts ...gen.DOOption) workflowReference {
	_workflowReference := workflowReference{}

	_workflowReference.workflowReferenceDo.UseDB(db, opts...)
	_workflowReference.workflowReferenceDo.UseModel(&model.WorkflowReference{})

	tableName := _workflowReference.workflowReferenceDo.TableName()
	_workflowReference.ALL = field.NewAsterisk(tableName)
	_workflowReference.ID = field.NewInt64(tableName, "id")
	_workflowReference.ReferredID = field.NewInt64(tableName, "referred_id")
	_workflowReference.ReferringID = field.NewInt64(tableName, "referring_id")
	_workflowReference.ReferType = field.NewInt32(tableName, "refer_type")
	_workflowReference.ReferringBizType = field.NewInt32(tableName, "referring_biz_type")
	_workflowReference.CreatedAt = field.NewInt64(tableName, "created_at")
	_workflowReference.Status = field.NewInt32(tableName, "status")
	_workflowReference.DeletedAt = field.NewField(tableName, "deleted_at")

	_workflowReference.fillFieldMap()

	return _workflowReference
}

// workflowReference The workflow association table,used to record the direct mutual reference relationship between workflows
type workflowReference struct {
	workflowReferenceDo

	ALL              field.Asterisk
	ID               field.Int64 // workflow id
	ReferredID       field.Int64 // the id of the workflow that is referred by other entities
	ReferringID      field.Int64 // the entity id that refers this workflow
	ReferType        field.Int32 // 1 subworkflow 2 tool
	ReferringBizType field.Int32 // the biz type the referring entity belongs to: 1. workflow 2. agent
	CreatedAt        field.Int64 // create time in millisecond
	Status           field.Int32 // whether this reference currently takes effect. 0: disabled 1: enabled
	DeletedAt        field.Field // Delete Time

	fieldMap map[string]field.Expr
}

func (w workflowReference) Table(newTableName string) *workflowReference {
	w.workflowReferenceDo.UseTable(newTableName)
	return w.updateTableName(newTableName)
}

func (w workflowReference) As(alias string) *workflowReference {
	w.workflowReferenceDo.DO = *(w.workflowReferenceDo.As(alias).(*gen.DO))
	return w.updateTableName(alias)
}

func (w *workflowReference) updateTableName(table string) *workflowReference {
	w.ALL = field.NewAsterisk(table)
	w.ID = field.NewInt64(table, "id")
	w.ReferredID = field.NewInt64(table, "referred_id")
	w.ReferringID = field.NewInt64(table, "referring_id")
	w.ReferType = field.NewInt32(table, "refer_type")
	w.ReferringBizType = field.NewInt32(table, "referring_biz_type")
	w.CreatedAt = field.NewInt64(table, "created_at")
	w.Status = field.NewInt32(table, "status")
	w.DeletedAt = field.NewField(table, "deleted_at")

	w.fillFieldMap()

	return w
}

func (w *workflowReference) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := w.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (w *workflowReference) fillFieldMap() {
	w.fieldMap = make(map[string]field.Expr, 8)
	w.fieldMap["id"] = w.ID
	w.fieldMap["referred_id"] = w.ReferredID
	w.fieldMap["referring_id"] = w.ReferringID
	w.fieldMap["refer_type"] = w.ReferType
	w.fieldMap["referring_biz_type"] = w.ReferringBizType
	w.fieldMap["created_at"] = w.CreatedAt
	w.fieldMap["status"] = w.Status
	w.fieldMap["deleted_at"] = w.DeletedAt
}

func (w workflowReference) clone(db *gorm.DB) workflowReference {
	w.workflowReferenceDo.ReplaceConnPool(db.Statement.ConnPool)
	return w
}

func (w workflowReference) replaceDB(db *gorm.DB) workflowReference {
	w.workflowReferenceDo.ReplaceDB(db)
	return w
}

type workflowReferenceDo struct{ gen.DO }

type IWorkflowReferenceDo interface {
	gen.SubQuery
	Debug() IWorkflowReferenceDo
	WithContext(ctx context.Context) IWorkflowReferenceDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IWorkflowReferenceDo
	WriteDB() IWorkflowReferenceDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IWorkflowReferenceDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IWorkflowReferenceDo
	Not(conds ...gen.Condition) IWorkflowReferenceDo
	Or(conds ...gen.Condition) IWorkflowReferenceDo
	Select(conds ...field.Expr) IWorkflowReferenceDo
	Where(conds ...gen.Condition) IWorkflowReferenceDo
	Order(conds ...field.Expr) IWorkflowReferenceDo
	Distinct(cols ...field.Expr) IWorkflowReferenceDo
	Omit(cols ...field.Expr) IWorkflowReferenceDo
	Join(table schema.Tabler, on ...field.Expr) IWorkflowReferenceDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IWorkflowReferenceDo
	RightJoin(table schema.Tabler, on ...field.Expr) IWorkflowReferenceDo
	Group(cols ...field.Expr) IWorkflowReferenceDo
	Having(conds ...gen.Condition) IWorkflowReferenceDo
	Limit(limit int) IWorkflowReferenceDo
	Offset(offset int) IWorkflowReferenceDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IWorkflowReferenceDo
	Unscoped() IWorkflowReferenceDo
	Create(values ...*model.WorkflowReference) error
	CreateInBatches(values []*model.WorkflowReference, batchSize int) error
	Save(values ...*model.WorkflowReference) error
	First() (*model.WorkflowReference, error)
	Take() (*model.WorkflowReference, error)
	Last() (*model.WorkflowReference, error)
	Find() ([]*model.WorkflowReference, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.WorkflowReference, err error)
	FindInBatches(result *[]*model.WorkflowReference, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.WorkflowReference) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IWorkflowReferenceDo
	Assign(attrs ...field.AssignExpr) IWorkflowReferenceDo
	Joins(fields ...field.RelationField) IWorkflowReferenceDo
	Preload(fields ...field.RelationField) IWorkflowReferenceDo
	FirstOrInit() (*model.WorkflowReference, error)
	FirstOrCreate() (*model.WorkflowReference, error)
	FindByPage(offset int, limit int) (result []*model.WorkflowReference, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IWorkflowReferenceDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (w workflowReferenceDo) Debug() IWorkflowReferenceDo {
	return w.withDO(w.DO.Debug())
}

func (w workflowReferenceDo) WithContext(ctx context.Context) IWorkflowReferenceDo {
	return w.withDO(w.DO.WithContext(ctx))
}

func (w workflowReferenceDo) ReadDB() IWorkflowReferenceDo {
	return w.Clauses(dbresolver.Read)
}

func (w workflowReferenceDo) WriteDB() IWorkflowReferenceDo {
	return w.Clauses(dbresolver.Write)
}

func (w workflowReferenceDo) Session(config *gorm.Session) IWorkflowReferenceDo {
	return w.withDO(w.DO.Session(config))
}

func (w workflowReferenceDo) Clauses(conds ...clause.Expression) IWorkflowReferenceDo {
	return w.withDO(w.DO.Clauses(conds...))
}

func (w workflowReferenceDo) Returning(value interface{}, columns ...string) IWorkflowReferenceDo {
	return w.withDO(w.DO.Returning(value, columns...))
}

func (w workflowReferenceDo) Not(conds ...gen.Condition) IWorkflowReferenceDo {
	return w.withDO(w.DO.Not(conds...))
}

func (w workflowReferenceDo) Or(conds ...gen.Condition) IWorkflowReferenceDo {
	return w.withDO(w.DO.Or(conds...))
}

func (w workflowReferenceDo) Select(conds ...field.Expr) IWorkflowReferenceDo {
	return w.withDO(w.DO.Select(conds...))
}

func (w workflowReferenceDo) Where(conds ...gen.Condition) IWorkflowReferenceDo {
	return w.withDO(w.DO.Where(conds...))
}

func (w workflowReferenceDo) Order(conds ...field.Expr) IWorkflowReferenceDo {
	return w.withDO(w.DO.Order(conds...))
}

func (w workflowReferenceDo) Distinct(cols ...field.Expr) IWorkflowReferenceDo {
	return w.withDO(w.DO.Distinct(cols...))
}

func (w workflowReferenceDo) Omit(cols ...field.Expr) IWorkflowReferenceDo {
	return w.withDO(w.DO.Omit(cols...))
}

func (w workflowReferenceDo) Join(table schema.Tabler, on ...field.Expr) IWorkflowReferenceDo {
	return w.withDO(w.DO.Join(table, on...))
}

func (w workflowReferenceDo) LeftJoin(table schema.Tabler, on ...field.Expr) IWorkflowReferenceDo {
	return w.withDO(w.DO.LeftJoin(table, on...))
}

func (w workflowReferenceDo) RightJoin(table schema.Tabler, on ...field.Expr) IWorkflowReferenceDo {
	return w.withDO(w.DO.RightJoin(table, on...))
}

func (w workflowReferenceDo) Group(cols ...field.Expr) IWorkflowReferenceDo {
	return w.withDO(w.DO.Group(cols...))
}

func (w workflowReferenceDo) Having(conds ...gen.Condition) IWorkflowReferenceDo {
	return w.withDO(w.DO.Having(conds...))
}

func (w workflowReferenceDo) Limit(limit int) IWorkflowReferenceDo {
	return w.withDO(w.DO.Limit(limit))
}

func (w workflowReferenceDo) Offset(offset int) IWorkflowReferenceDo {
	return w.withDO(w.DO.Offset(offset))
}

func (w workflowReferenceDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IWorkflowReferenceDo {
	return w.withDO(w.DO.Scopes(funcs...))
}

func (w workflowReferenceDo) Unscoped() IWorkflowReferenceDo {
	return w.withDO(w.DO.Unscoped())
}

func (w workflowReferenceDo) Create(values ...*model.WorkflowReference) error {
	if len(values) == 0 {
		return nil
	}
	return w.DO.Create(values)
}

func (w workflowReferenceDo) CreateInBatches(values []*model.WorkflowReference, batchSize int) error {
	return w.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (w workflowReferenceDo) Save(values ...*model.WorkflowReference) error {
	if len(values) == 0 {
		return nil
	}
	return w.DO.Save(values)
}

func (w workflowReferenceDo) First() (*model.WorkflowReference, error) {
	if result, err := w.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.WorkflowReference), nil
	}
}

func (w workflowReferenceDo) Take() (*model.WorkflowReference, error) {
	if result, err := w.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.WorkflowReference), nil
	}
}

func (w workflowReferenceDo) Last() (*model.WorkflowReference, error) {
	if result, err := w.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.WorkflowReference), nil
	}
}

func (w workflowReferenceDo) Find() ([]*model.WorkflowReference, error) {
	result, err := w.DO.Find()
	return result.([]*model.WorkflowReference), err
}

func (w workflowReferenceDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.WorkflowReference, err error) {
	buf := make([]*model.WorkflowReference, 0, batchSize)
	err = w.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (w workflowReferenceDo) FindInBatches(result *[]*model.WorkflowReference, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return w.DO.FindInBatches(result, batchSize, fc)
}

func (w workflowReferenceDo) Attrs(attrs ...field.AssignExpr) IWorkflowReferenceDo {
	return w.withDO(w.DO.Attrs(attrs...))
}

func (w workflowReferenceDo) Assign(attrs ...field.AssignExpr) IWorkflowReferenceDo {
	return w.withDO(w.DO.Assign(attrs...))
}

func (w workflowReferenceDo) Joins(fields ...field.RelationField) IWorkflowReferenceDo {
	for _, _f := range fields {
		w = *w.withDO(w.DO.Joins(_f))
	}
	return &w
}

func (w workflowReferenceDo) Preload(fields ...field.RelationField) IWorkflowReferenceDo {
	for _, _f := range fields {
		w = *w.withDO(w.DO.Preload(_f))
	}
	return &w
}

func (w workflowReferenceDo) FirstOrInit() (*model.WorkflowReference, error) {
	if result, err := w.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.WorkflowReference), nil
	}
}

func (w workflowReferenceDo) FirstOrCreate() (*model.WorkflowReference, error) {
	if result, err := w.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.WorkflowReference), nil
	}
}

func (w workflowReferenceDo) FindByPage(offset int, limit int) (result []*model.WorkflowReference, count int64, err error) {
	result, err = w.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = w.Offset(-1).Limit(-1).Count()
	return
}

func (w workflowReferenceDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = w.Count()
	if err != nil {
		return
	}

	err = w.Offset(offset).Limit(limit).Scan(result)
	return
}

func (w workflowReferenceDo) Scan(result interface{}) (err error) {
	return w.DO.Scan(result)
}

func (w workflowReferenceDo) Delete(models ...*model.WorkflowReference) (result gen.ResultInfo, err error) {
	return w.DO.Delete(models)
}

func (w *workflowReferenceDo) withDO(do gen.Dao) *workflowReferenceDo {
	w.DO = *do.(*gen.DO)
	return w
}
