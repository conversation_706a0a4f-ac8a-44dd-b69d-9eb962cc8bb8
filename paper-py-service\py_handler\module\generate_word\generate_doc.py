from docx import Document
from docx.shared import Pt, Cm, Inches
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.section import WD_ORIENTATION
from docx.enum.text import WD_BREAK
from docx.shared import Length, RGBColor
from io import BytesIO
from flask import send_file, request, jsonify
from .generate_word import insert_content_paragraphs, insert_toc
from .routes import generate_app

def generate_simple_doc(abstract, keywords, content, reference):
    """
    生成带有中文论文格式要求的Word文档
    
    参数:
        abstract (dict): 摘要文本，结构为 {id, content, target, level}
        keywords (dict): 关键词文本，结构为 {id, content, target, level}
        content (list): 内容部分列表，每项结构为 {id, content, target, level}
        reference (dict): 参考文献文本，结构为 {id, content, target, level}
    
    返回:
        flask.Response: 可下载的Word文档
    """
    # 创建文档
    doc = Document()
    
    # 设置页面布局
    section = doc.sections[0]
    section.page_height = Cm(29.7)  # A4纸高度
    section.page_width = Cm(21.0)   # A4纸宽度
    section.left_margin = Cm(3.0)   # 左边距
    section.right_margin = Cm(2.5)  # 右边距
    section.top_margin = Cm(2.5)    # 上边距
    section.bottom_margin = Cm(2.5) # 下边距
    
    # 设置默认样式
    style = doc.styles['Normal']
    style.font.name = '宋体'
    style.font.size = Pt(12)  # 小四号字体
    style.font.color.rgb = RGBColor(0, 0, 0)  # 黑色
    style.paragraph_format.line_spacing = 1.5  # 1.5倍行距

    # 设置标题样式
    heading1 = doc.styles['Heading 1']
    heading1.font.name = '宋体'
    heading1.font.size = Pt(14)  # 四号字体
    heading1.font.bold = True
    heading1.font.color.rgb = RGBColor(0, 0, 0)  # 黑色
    heading1.paragraph_format.line_spacing = 1.5
    heading1.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.CENTER

    heading2 = doc.styles['Heading 2']
    heading2.font.name = '宋体'
    heading2.font.size = Pt(12)  # 小四号字体
    heading2.font.color.rgb = RGBColor(0, 0, 0)  # 黑色
    heading2.paragraph_format.line_spacing = 1.5

    heading3 = doc.styles['Heading 3']
    heading3.font.name = '宋体'
    heading3.font.size = Pt(12)  # 小四号字体
    heading3.font.color.rgb = RGBColor(0, 0, 0)  # 黑色
    heading3.paragraph_format.line_spacing = 1.5
    
    # 添加目录标题
    toc_heading = doc.add_paragraph("目录", style='Heading 1')
    
    # 插入目录
    toc_paragraph = doc.add_paragraph()
    insert_toc(toc_paragraph)
    
    # 添加分页符
    page_break = doc.add_paragraph()
    page_break.add_run().add_break(WD_BREAK.PAGE)
    
    # 添加摘要
    abstract_heading = doc.add_paragraph("摘要", style='Heading 1')
    
    # 使用insert_content_paragraphs添加摘要内容
    abstract_para = doc.add_paragraph(style='Normal')
    insert_content_paragraphs(doc, abstract_para, abstract['content'], "abstract")
    
    # 添加关键词
    keywords_para = doc.add_paragraph(style='Normal')
    keywords_run = keywords_para.add_run("关键词：")
    keywords_run.bold = True
    
    # 使用insert_content_paragraphs添加关键词内容
    keywords_content_para = doc.add_paragraph(style='Normal')
    insert_content_paragraphs(doc, keywords_content_para, keywords['content'], "keywords")
    
    # 添加分页符
    page_break = doc.add_paragraph()
    page_break.add_run().add_break(WD_BREAK.PAGE)
    
    # 添加内容部分
    for section_content in content:
        # 根据level添加标题
        if section_content['level'] == 1:
            # 一级标题
            heading = doc.add_paragraph(section_content['target'], style='Heading 1')
        elif section_content['level'] == 2:
            # 二级标题
            heading = doc.add_paragraph(section_content['target'], style='Heading 2')
        elif section_content['level'] == 3:
            # 三级标题
            heading = doc.add_paragraph(section_content['target'], style='Heading 3')
        
        # 使用insert_content_paragraphs添加段落内容
        if section_content['content']:
            content_para = doc.add_paragraph(style='Normal')
            insert_content_paragraphs(doc, content_para, section_content['content'], "content")
    
    # 添加参考文献
    ref_heading = doc.add_paragraph("参考文献", style='Heading 1')
    
    # 使用insert_content_paragraphs添加参考文献内容
    if reference['content']:
        ref_para = doc.add_paragraph(style='Normal')
        insert_content_paragraphs(doc, ref_para, reference['content'], "reference")
    
    # 保存文档到内存
    doc_stream = BytesIO()
    try:
        doc.save(doc_stream)
        doc_stream.seek(0)
        
        response = send_file(
            doc_stream,
            mimetype='application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            as_attachment=True,
            download_name='document.docx'
        )
        
        # 设置文件流在响应完成后关闭
        def cleanup():
            doc_stream.close()
        response.call_on_close(cleanup)
        
        return response
    except Exception as e:
        doc_stream.close()
        raise e

@generate_app.route('/simple-doc', methods=['POST'])
def handle_generate_simple_doc():
    """
    生成文档的API端点
    
    预期的JSON数据格式:
    {
        "abstract": {"id": "", "content": "", "target": "", "level": 1},
        "keywords": {"id": "", "content": "", "target": "", "level": 1},
        "content": [{"id": "", "content": "", "target": "", "level": 1}, ...],
        "reference": {"id": "", "content": "", "target": "", "level": 1}
    }
    
    返回:
        Word文档作为可下载文件
    """
    try:
        data = request.get_json()
        
        # 验证必需字段
        required_fields = ['abstract', 'keywords', 'content', 'reference']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'缺少必需字段: {field}'}), 400
        
        # 验证字段结构
        for field in ['abstract', 'keywords', 'reference']:
            if not isinstance(data[field], dict) or not all(key in data[field] for key in ['id', 'content', 'target', 'level']):
                return jsonify({'error': f'字段结构无效: {field}'}), 400
        
        # 验证content是否为列表
        if not isinstance(data['content'], list):
            return jsonify({'error': 'content必须是列表类型'}), 400
        
        # 验证content列表中的每个项目
        for item in data['content']:
            if not isinstance(item, dict) or not all(key in item for key in ['id', 'content', 'target', 'level']):
                return jsonify({'error': 'content列表中存在结构无效的项目'}), 400
        
        # 生成文档
        return generate_simple_doc(
            abstract=data['abstract'],
            keywords=data['keywords'],
            content=data['content'],
            reference=data['reference']
        )
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500