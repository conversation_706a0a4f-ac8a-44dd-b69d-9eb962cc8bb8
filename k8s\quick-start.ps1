# 快速启动脚本 - 一键体验 K8s 微服务
Write-Host "=== Paper Services K8s 快速启动 ===" -ForegroundColor Green

# 检查 Docker 是否运行
try {
    docker version | Out-Null
    Write-Host "✓ Docker 运行正常" -ForegroundColor Green
} catch {
    Write-Error "Docker 未运行，请启动 Docker Desktop"
    exit 1
}

# 检查 Kubernetes 是否启用
try {
    kubectl cluster-info | Out-Null
    Write-Host "✓ Kubernetes 集群可用" -ForegroundColor Green
} catch {
    Write-Error "Kubernetes 不可用，请在 Docker Desktop 中启用 Kubernetes"
    exit 1
}

Write-Host "`n🚀 开始一键部署..." -ForegroundColor Yellow
& "$PSScriptRoot\deploy.ps1" -Build

Write-Host "`n=== 启动完成! ===" -ForegroundColor Green
Write-Host "🎉 所有服务已启动，请访问:" -ForegroundColor Cyan
Write-Host ""
Write-Host "📝 论文生成系统: http://localhost:30000" -ForegroundColor White
Write-Host "🤖 Dify AI 平台: http://localhost:30001" -ForegroundColor White
Write-Host ""
Write-Host "💡 提示:" -ForegroundColor Yellow
Write-Host "- 首次启动可能需要几分钟时间下载和启动所有服务" -ForegroundColor White
Write-Host "- 使用 'kubectl get pods -n paper-services' 查看服务状态" -ForegroundColor White
Write-Host "- 使用 '.\cleanup.ps1 -All' 清理所有资源" -ForegroundColor White
