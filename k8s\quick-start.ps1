# Quick Start Script - One-click K8s Microservices Experience
Write-Host "=== Paper Services K8s Quick Start ===" -ForegroundColor Green

# Check if Docker is running
try {
    docker version | Out-Null
    Write-Host "✓ Docker is running" -ForegroundColor Green
} catch {
    Write-Error "Docker is not running, please start Docker Desktop"
    exit 1
}

# Check if Kubernetes is enabled
try {
    kubectl cluster-info | Out-Null
    Write-Host "✓ Kubernetes cluster is available" -ForegroundColor Green
} catch {
    Write-Error "Kubernetes is not available, please enable Kubernetes in Docker Desktop"
    exit 1
}

Write-Host "`n🚀 Starting one-click deployment..." -ForegroundColor Yellow
& "$PSScriptRoot\deploy.ps1" -Build

Write-Host "`n=== Startup Complete! ===" -ForegroundColor Green
Write-Host "🎉 All services are started, please visit:" -ForegroundColor Cyan
Write-Host ""
Write-Host "📝 Paper Generation System: http://localhost:30000" -ForegroundColor White
Write-Host "🤖 Dify AI Platform: http://localhost:30001" -ForegroundColor White
Write-Host ""
Write-Host "💡 Tips:" -ForegroundColor Yellow
Write-Host "- First startup may take a few minutes to download and start all services" -ForegroundColor White
Write-Host "- Use 'kubectl get pods -n paper-services' to check service status" -ForegroundColor White
Write-Host "- Use '.\cleanup.ps1 -All' to clean up all resources" -ForegroundColor White
