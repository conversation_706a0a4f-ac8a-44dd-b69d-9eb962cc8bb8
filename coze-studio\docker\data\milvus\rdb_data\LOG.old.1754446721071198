2025/08/05-03:29:47.922303 35 RocksDB version: 6.29.5
2025/08/05-03:29:47.923017 35 Git sha 0
2025/08/05-03:29:47.923028 35 Compile date 2024-11-15 11:22:58
2025/08/05-03:29:47.923031 35 DB SUMMARY
2025/08/05-03:29:47.923032 35 DB Session ID:  SFMW2AWSCE1QC1S44Z6J
2025/08/05-03:29:47.926562 35 CURRENT file:  CURRENT
2025/08/05-03:29:47.926584 35 IDENTITY file:  IDENTITY
2025/08/05-03:29:47.927747 35 MANIFEST file:  MANIFEST-000054 size: 558 Bytes
2025/08/05-03:29:47.927761 35 SST files in /var/lib/milvus/rdb_data dir, Total Num: 3, files: 000041.sst 000047.sst 000053.sst 
2025/08/05-03:29:47.927764 35 Write Ahead Log file in /var/lib/milvus/rdb_data: 000055.log size: 5227790 ; 
2025/08/05-03:29:47.927768 35                         Options.error_if_exists: 0
2025/08/05-03:29:47.927769 35                       Options.create_if_missing: 1
2025/08/05-03:29:47.927770 35                         Options.paranoid_checks: 1
2025/08/05-03:29:47.927771 35             Options.flush_verify_memtable_count: 1
2025/08/05-03:29:47.927772 35                               Options.track_and_verify_wals_in_manifest: 0
2025/08/05-03:29:47.927773 35                                     Options.env: 0x7f09c6f9ed00
2025/08/05-03:29:47.927775 35                                      Options.fs: PosixFileSystem
2025/08/05-03:29:47.927776 35                                Options.info_log: 0x7f08eb290140
2025/08/05-03:29:47.927778 35                Options.max_file_opening_threads: 16
2025/08/05-03:29:47.927778 35                              Options.statistics: (nil)
2025/08/05-03:29:47.927780 35                               Options.use_fsync: 0
2025/08/05-03:29:47.927781 35                       Options.max_log_file_size: 0
2025/08/05-03:29:47.927782 35                  Options.max_manifest_file_size: 1073741824
2025/08/05-03:29:47.927783 35                   Options.log_file_time_to_roll: 0
2025/08/05-03:29:47.927784 35                       Options.keep_log_file_num: 1000
2025/08/05-03:29:47.927785 35                    Options.recycle_log_file_num: 0
2025/08/05-03:29:47.927786 35                         Options.allow_fallocate: 1
2025/08/05-03:29:47.927787 35                        Options.allow_mmap_reads: 0
2025/08/05-03:29:47.927788 35                       Options.allow_mmap_writes: 0
2025/08/05-03:29:47.927789 35                        Options.use_direct_reads: 0
2025/08/05-03:29:47.927790 35                        Options.use_direct_io_for_flush_and_compaction: 0
2025/08/05-03:29:47.927791 35          Options.create_missing_column_families: 1
2025/08/05-03:29:47.927792 35                              Options.db_log_dir: 
2025/08/05-03:29:47.927793 35                                 Options.wal_dir: 
2025/08/05-03:29:47.927794 35                Options.table_cache_numshardbits: 6
2025/08/05-03:29:47.927795 35                         Options.WAL_ttl_seconds: 0
2025/08/05-03:29:47.927796 35                       Options.WAL_size_limit_MB: 0
2025/08/05-03:29:47.927797 35                        Options.max_write_batch_group_size_bytes: 1048576
2025/08/05-03:29:47.927798 35             Options.manifest_preallocation_size: 4194304
2025/08/05-03:29:47.927799 35                     Options.is_fd_close_on_exec: 1
2025/08/05-03:29:47.927800 35                   Options.advise_random_on_open: 1
2025/08/05-03:29:47.927801 35                   Options.experimental_mempurge_threshold: 0.000000
2025/08/05-03:29:47.927807 35                    Options.db_write_buffer_size: 0
2025/08/05-03:29:47.927808 35                    Options.write_buffer_manager: 0x7f08f3660280
2025/08/05-03:29:47.927809 35         Options.access_hint_on_compaction_start: 1
2025/08/05-03:29:47.927810 35  Options.new_table_reader_for_compaction_inputs: 0
2025/08/05-03:29:47.927811 35           Options.random_access_max_buffer_size: 1048576
2025/08/05-03:29:47.927812 35                      Options.use_adaptive_mutex: 0
2025/08/05-03:29:47.927813 35                            Options.rate_limiter: (nil)
2025/08/05-03:29:47.927833 35     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/08/05-03:29:47.927834 35                       Options.wal_recovery_mode: 2
2025/08/05-03:29:47.928270 35                  Options.enable_thread_tracking: 0
2025/08/05-03:29:47.928275 35                  Options.enable_pipelined_write: 0
2025/08/05-03:29:47.928276 35                  Options.unordered_write: 0
2025/08/05-03:29:47.928277 35         Options.allow_concurrent_memtable_write: 1
2025/08/05-03:29:47.928278 35      Options.enable_write_thread_adaptive_yield: 1
2025/08/05-03:29:47.928279 35             Options.write_thread_max_yield_usec: 100
2025/08/05-03:29:47.928280 35            Options.write_thread_slow_yield_usec: 3
2025/08/05-03:29:47.928281 35                               Options.row_cache: None
2025/08/05-03:29:47.928282 35                              Options.wal_filter: None
2025/08/05-03:29:47.928287 35             Options.avoid_flush_during_recovery: 0
2025/08/05-03:29:47.928289 35             Options.allow_ingest_behind: 0
2025/08/05-03:29:47.928290 35             Options.preserve_deletes: 0
2025/08/05-03:29:47.928290 35             Options.two_write_queues: 0
2025/08/05-03:29:47.928291 35             Options.manual_wal_flush: 0
2025/08/05-03:29:47.928292 35             Options.atomic_flush: 0
2025/08/05-03:29:47.928293 35             Options.avoid_unnecessary_blocking_io: 0
2025/08/05-03:29:47.928294 35                 Options.persist_stats_to_disk: 0
2025/08/05-03:29:47.928295 35                 Options.write_dbid_to_manifest: 0
2025/08/05-03:29:47.928296 35                 Options.log_readahead_size: 0
2025/08/05-03:29:47.928297 35                 Options.file_checksum_gen_factory: Unknown
2025/08/05-03:29:47.928298 35                 Options.best_efforts_recovery: 0
2025/08/05-03:29:47.928299 35                Options.max_bgerror_resume_count: 2147483647
2025/08/05-03:29:47.928300 35            Options.bgerror_resume_retry_interval: 1000000
2025/08/05-03:29:47.928313 35             Options.allow_data_in_errors: 0
2025/08/05-03:29:47.928314 35             Options.db_host_id: __hostname__
2025/08/05-03:29:47.928316 35             Options.max_background_jobs: 2
2025/08/05-03:29:47.928317 35             Options.max_background_compactions: -1
2025/08/05-03:29:47.928318 35             Options.max_subcompactions: 1
2025/08/05-03:29:47.928319 35             Options.avoid_flush_during_shutdown: 0
2025/08/05-03:29:47.928320 35           Options.writable_file_max_buffer_size: 1048576
2025/08/05-03:29:47.928321 35             Options.delayed_write_rate : 16777216
2025/08/05-03:29:47.928322 35             Options.max_total_wal_size: 0
2025/08/05-03:29:47.928323 35             Options.delete_obsolete_files_period_micros: 21600000000
2025/08/05-03:29:47.928324 35                   Options.stats_dump_period_sec: 600
2025/08/05-03:29:47.928325 35                 Options.stats_persist_period_sec: 600
2025/08/05-03:29:47.928326 35                 Options.stats_history_buffer_size: 1048576
2025/08/05-03:29:47.928327 35                          Options.max_open_files: -1
2025/08/05-03:29:47.928328 35                          Options.bytes_per_sync: 0
2025/08/05-03:29:47.928329 35                      Options.wal_bytes_per_sync: 0
2025/08/05-03:29:47.928330 35                   Options.strict_bytes_per_sync: 0
2025/08/05-03:29:47.928331 35       Options.compaction_readahead_size: 0
2025/08/05-03:29:47.928332 35                  Options.max_background_flushes: 1
2025/08/05-03:29:47.928332 35 Compression algorithms supported:
2025/08/05-03:29:47.928335 35 	kZSTD supported: 1
2025/08/05-03:29:47.928336 35 	kXpressCompression supported: 0
2025/08/05-03:29:47.928338 35 	kBZip2Compression supported: 0
2025/08/05-03:29:47.928339 35 	kZSTDNotFinalCompression supported: 1
2025/08/05-03:29:47.928340 35 	kLZ4Compression supported: 0
2025/08/05-03:29:47.928341 35 	kZlibCompression supported: 0
2025/08/05-03:29:47.928342 35 	kLZ4HCCompression supported: 0
2025/08/05-03:29:47.928343 35 	kSnappyCompression supported: 0
2025/08/05-03:29:47.928348 35 Fast CRC32 supported: Not supported on x86
2025/08/05-03:29:47.935671 35 [db/version_set.cc:4888] Recovering from manifest file: /var/lib/milvus/rdb_data/MANIFEST-000054
2025/08/05-03:29:47.938304 35 [db/column_family.cc:605] --------------- Options for column family [default]:
2025/08/05-03:29:47.938327 35               Options.comparator: leveldb.BytewiseComparator
2025/08/05-03:29:47.938329 35           Options.merge_operator: None
2025/08/05-03:29:47.938330 35        Options.compaction_filter: None
2025/08/05-03:29:47.938331 35        Options.compaction_filter_factory: None
2025/08/05-03:29:47.938333 35  Options.sst_partitioner_factory: None
2025/08/05-03:29:47.938334 35         Options.memtable_factory: SkipListFactory
2025/08/05-03:29:47.938336 35            Options.table_factory: BlockBasedTable
2025/08/05-03:29:47.938365 35            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f08f3601000)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0x7f08f3660010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 1001872834
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: (nil)
  persistent_cache: (nil)
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2025/08/05-03:29:47.938377 35        Options.write_buffer_size: 67108864
2025/08/05-03:29:47.938378 35  Options.max_write_buffer_number: 2
2025/08/05-03:29:47.938380 35        Options.compression[0]: NoCompression
2025/08/05-03:29:47.938381 35        Options.compression[1]: NoCompression
2025/08/05-03:29:47.938383 35        Options.compression[2]: ZSTD
2025/08/05-03:29:47.938384 35        Options.compression[3]: ZSTD
2025/08/05-03:29:47.938385 35        Options.compression[4]: ZSTD
2025/08/05-03:29:47.938386 35                  Options.bottommost_compression: Disabled
2025/08/05-03:29:47.938387 35       Options.prefix_extractor: nullptr
2025/08/05-03:29:47.938388 35   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/05-03:29:47.938390 35             Options.num_levels: 5
2025/08/05-03:29:47.938391 35        Options.min_write_buffer_number_to_merge: 1
2025/08/05-03:29:47.938392 35     Options.max_write_buffer_number_to_maintain: 0
2025/08/05-03:29:47.938393 35     Options.max_write_buffer_size_to_maintain: 0
2025/08/05-03:29:47.938394 35            Options.bottommost_compression_opts.window_bits: -14
2025/08/05-03:29:47.938395 35                  Options.bottommost_compression_opts.level: 32767
2025/08/05-03:29:47.938396 35               Options.bottommost_compression_opts.strategy: 0
2025/08/05-03:29:47.938397 35         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/05-03:29:47.938398 35         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/05-03:29:47.938399 35         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/05-03:29:47.938400 35                  Options.bottommost_compression_opts.enabled: false
2025/08/05-03:29:47.938402 35         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/05-03:29:47.938403 35            Options.compression_opts.window_bits: -14
2025/08/05-03:29:47.938404 35                  Options.compression_opts.level: 32767
2025/08/05-03:29:47.938405 35               Options.compression_opts.strategy: 0
2025/08/05-03:29:47.938406 35         Options.compression_opts.max_dict_bytes: 0
2025/08/05-03:29:47.938407 35         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/05-03:29:47.938408 35         Options.compression_opts.parallel_threads: 1
2025/08/05-03:29:47.938951 35                  Options.compression_opts.enabled: false
2025/08/05-03:29:47.938994 35         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/05-03:29:47.939070 35      Options.level0_file_num_compaction_trigger: 4
2025/08/05-03:29:47.939073 35          Options.level0_slowdown_writes_trigger: 20
2025/08/05-03:29:47.939074 35              Options.level0_stop_writes_trigger: 36
2025/08/05-03:29:47.939075 35                   Options.target_file_size_base: 67108864
2025/08/05-03:29:47.939076 35             Options.target_file_size_multiplier: 2
2025/08/05-03:29:47.939077 35                Options.max_bytes_for_level_base: 268435456
2025/08/05-03:29:47.939078 35 Options.level_compaction_dynamic_level_bytes: 0
2025/08/05-03:29:47.939079 35          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/05-03:29:47.939086 35 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/05-03:29:47.939087 35 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/05-03:29:47.939088 35 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/05-03:29:47.939089 35 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/05-03:29:47.939090 35 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/05-03:29:47.939091 35 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/05-03:29:47.939092 35 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/05-03:29:47.939093 35       Options.max_sequential_skip_in_iterations: 8
2025/08/05-03:29:47.939094 35                    Options.max_compaction_bytes: 1677721600
2025/08/05-03:29:47.939096 35                        Options.arena_block_size: 1048576
2025/08/05-03:29:47.939097 35   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/05-03:29:47.939098 35   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/05-03:29:47.939099 35       Options.rate_limit_delay_max_milliseconds: 100
2025/08/05-03:29:47.939100 35                Options.disable_auto_compactions: 0
2025/08/05-03:29:47.939106 35                        Options.compaction_style: kCompactionStyleLevel
2025/08/05-03:29:47.939108 35                          Options.compaction_pri: kMinOverlappingRatio
2025/08/05-03:29:47.939109 35 Options.compaction_options_universal.size_ratio: 1
2025/08/05-03:29:47.939110 35 Options.compaction_options_universal.min_merge_width: 2
2025/08/05-03:29:47.939111 35 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/05-03:29:47.939177 35 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/05-03:29:47.939179 35 Options.compaction_options_universal.compression_size_percent: -1
2025/08/05-03:29:47.939182 35 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/05-03:29:47.939183 35 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/05-03:29:47.939184 35 Options.compaction_options_fifo.allow_compaction: 0
2025/08/05-03:29:47.939200 35                   Options.table_properties_collectors: 
2025/08/05-03:29:47.939201 35                   Options.inplace_update_support: 0
2025/08/05-03:29:47.939202 35                 Options.inplace_update_num_locks: 10000
2025/08/05-03:29:47.939204 35               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/05-03:29:47.939206 35               Options.memtable_whole_key_filtering: 0
2025/08/05-03:29:47.939207 35   Options.memtable_huge_page_size: 0
2025/08/05-03:29:47.939208 35                           Options.bloom_locality: 0
2025/08/05-03:29:47.939209 35                    Options.max_successive_merges: 0
2025/08/05-03:29:47.939211 35                Options.optimize_filters_for_hits: 0
2025/08/05-03:29:47.939211 35                Options.paranoid_file_checks: 0
2025/08/05-03:29:47.939212 35                Options.force_consistency_checks: 1
2025/08/05-03:29:47.939213 35                Options.report_bg_io_stats: 0
2025/08/05-03:29:47.939214 35                               Options.ttl: 2592000
2025/08/05-03:29:47.939215 35          Options.periodic_compaction_seconds: 0
2025/08/05-03:29:47.939622 35                       Options.enable_blob_files: false
2025/08/05-03:29:47.939626 35                           Options.min_blob_size: 0
2025/08/05-03:29:47.939627 35                          Options.blob_file_size: 268435456
2025/08/05-03:29:47.939628 35                   Options.blob_compression_type: NoCompression
2025/08/05-03:29:47.939629 35          Options.enable_blob_garbage_collection: false
2025/08/05-03:29:47.939630 35      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/05-03:29:47.939632 35 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/05-03:29:47.939633 35          Options.blob_compaction_readahead_size: 0
2025/08/05-03:29:47.940723 35 [db/column_family.cc:605] --------------- Options for column family [properties]:
2025/08/05-03:29:47.940730 35               Options.comparator: leveldb.BytewiseComparator
2025/08/05-03:29:47.940731 35           Options.merge_operator: None
2025/08/05-03:29:47.940732 35        Options.compaction_filter: None
2025/08/05-03:29:47.940733 35        Options.compaction_filter_factory: None
2025/08/05-03:29:47.940734 35  Options.sst_partitioner_factory: None
2025/08/05-03:29:47.940735 35         Options.memtable_factory: SkipListFactory
2025/08/05-03:29:47.940736 35            Options.table_factory: BlockBasedTable
2025/08/05-03:29:47.940782 35            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f08f3601000)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0x7f08f3660010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 1001872834
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: (nil)
  persistent_cache: (nil)
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2025/08/05-03:29:47.940783 35        Options.write_buffer_size: 67108864
2025/08/05-03:29:47.940785 35  Options.max_write_buffer_number: 2
2025/08/05-03:29:47.940786 35        Options.compression[0]: NoCompression
2025/08/05-03:29:47.940788 35        Options.compression[1]: NoCompression
2025/08/05-03:29:47.940789 35        Options.compression[2]: ZSTD
2025/08/05-03:29:47.940790 35        Options.compression[3]: ZSTD
2025/08/05-03:29:47.940791 35        Options.compression[4]: ZSTD
2025/08/05-03:29:47.940792 35                  Options.bottommost_compression: Disabled
2025/08/05-03:29:47.940793 35       Options.prefix_extractor: nullptr
2025/08/05-03:29:47.940794 35   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/05-03:29:47.940795 35             Options.num_levels: 5
2025/08/05-03:29:47.940796 35        Options.min_write_buffer_number_to_merge: 1
2025/08/05-03:29:47.940797 35     Options.max_write_buffer_number_to_maintain: 0
2025/08/05-03:29:47.940798 35     Options.max_write_buffer_size_to_maintain: 0
2025/08/05-03:29:47.940799 35            Options.bottommost_compression_opts.window_bits: -14
2025/08/05-03:29:47.940800 35                  Options.bottommost_compression_opts.level: 32767
2025/08/05-03:29:47.940802 35               Options.bottommost_compression_opts.strategy: 0
2025/08/05-03:29:47.940803 35         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/05-03:29:47.940804 35         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/05-03:29:47.941058 35         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/05-03:29:47.941064 35                  Options.bottommost_compression_opts.enabled: false
2025/08/05-03:29:47.941066 35         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/05-03:29:47.941068 35            Options.compression_opts.window_bits: -14
2025/08/05-03:29:47.941069 35                  Options.compression_opts.level: 32767
2025/08/05-03:29:47.941070 35               Options.compression_opts.strategy: 0
2025/08/05-03:29:47.941071 35         Options.compression_opts.max_dict_bytes: 0
2025/08/05-03:29:47.941072 35         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/05-03:29:47.941073 35         Options.compression_opts.parallel_threads: 1
2025/08/05-03:29:47.941074 35                  Options.compression_opts.enabled: false
2025/08/05-03:29:47.941075 35         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/05-03:29:47.941076 35      Options.level0_file_num_compaction_trigger: 4
2025/08/05-03:29:47.941077 35          Options.level0_slowdown_writes_trigger: 20
2025/08/05-03:29:47.941078 35              Options.level0_stop_writes_trigger: 36
2025/08/05-03:29:47.941079 35                   Options.target_file_size_base: 67108864
2025/08/05-03:29:47.941080 35             Options.target_file_size_multiplier: 2
2025/08/05-03:29:47.941081 35                Options.max_bytes_for_level_base: 268435456
2025/08/05-03:29:47.941082 35 Options.level_compaction_dynamic_level_bytes: 0
2025/08/05-03:29:47.941083 35          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/05-03:29:47.941087 35 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/05-03:29:47.941088 35 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/05-03:29:47.941090 35 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/05-03:29:47.941091 35 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/05-03:29:47.941092 35 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/05-03:29:47.941093 35 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/05-03:29:47.941094 35 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/05-03:29:47.941095 35       Options.max_sequential_skip_in_iterations: 8
2025/08/05-03:29:47.941096 35                    Options.max_compaction_bytes: 1677721600
2025/08/05-03:29:47.941097 35                        Options.arena_block_size: 1048576
2025/08/05-03:29:47.941098 35   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/05-03:29:47.941099 35   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/05-03:29:47.941100 35       Options.rate_limit_delay_max_milliseconds: 100
2025/08/05-03:29:47.941101 35                Options.disable_auto_compactions: 0
2025/08/05-03:29:47.941106 35                        Options.compaction_style: kCompactionStyleLevel
2025/08/05-03:29:47.941108 35                          Options.compaction_pri: kMinOverlappingRatio
2025/08/05-03:29:47.941109 35 Options.compaction_options_universal.size_ratio: 1
2025/08/05-03:29:47.941110 35 Options.compaction_options_universal.min_merge_width: 2
2025/08/05-03:29:47.941111 35 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/05-03:29:47.941112 35 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/05-03:29:47.941113 35 Options.compaction_options_universal.compression_size_percent: -1
2025/08/05-03:29:47.941114 35 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/05-03:29:47.941115 35 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/05-03:29:47.941116 35 Options.compaction_options_fifo.allow_compaction: 0
2025/08/05-03:29:47.941123 35                   Options.table_properties_collectors: 
2025/08/05-03:29:47.941168 35                   Options.inplace_update_support: 0
2025/08/05-03:29:47.941171 35                 Options.inplace_update_num_locks: 10000
2025/08/05-03:29:47.941173 35               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/05-03:29:47.941372 35               Options.memtable_whole_key_filtering: 0
2025/08/05-03:29:47.941378 35   Options.memtable_huge_page_size: 0
2025/08/05-03:29:47.941379 35                           Options.bloom_locality: 0
2025/08/05-03:29:47.941380 35                    Options.max_successive_merges: 0
2025/08/05-03:29:47.941381 35                Options.optimize_filters_for_hits: 0
2025/08/05-03:29:47.941382 35                Options.paranoid_file_checks: 0
2025/08/05-03:29:47.941383 35                Options.force_consistency_checks: 1
2025/08/05-03:29:47.941384 35                Options.report_bg_io_stats: 0
2025/08/05-03:29:47.941385 35                               Options.ttl: 2592000
2025/08/05-03:29:47.941386 35          Options.periodic_compaction_seconds: 0
2025/08/05-03:29:47.941387 35                       Options.enable_blob_files: false
2025/08/05-03:29:47.941388 35                           Options.min_blob_size: 0
2025/08/05-03:29:47.941389 35                          Options.blob_file_size: 268435456
2025/08/05-03:29:47.941391 35                   Options.blob_compression_type: NoCompression
2025/08/05-03:29:47.941392 35          Options.enable_blob_garbage_collection: false
2025/08/05-03:29:47.941393 35      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/05-03:29:47.941396 35 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/05-03:29:47.941397 35          Options.blob_compaction_readahead_size: 0
2025/08/05-03:29:47.977827 35 [db/version_set.cc:4928] Recovered from manifest file:/var/lib/milvus/rdb_data/MANIFEST-000054 succeeded,manifest_file_number is 54, next_file_number is 56, last_sequence is 9126, log_number is 50,prev_log_number is 0,max_column_family is 1,min_log_number_to_keep is 0
2025/08/05-03:29:47.977842 35 [db/version_set.cc:4943] Column family [default] (ID 0), log number is 50
2025/08/05-03:29:47.977844 35 [db/version_set.cc:4943] Column family [properties] (ID 1), log number is 50
2025/08/05-03:29:47.994993 35 [db/version_set.cc:4409] Creating manifest 58
2025/08/05-03:29:48.027700 35 EVENT_LOG_v1 {"time_micros": 1754364588027687, "job": 1, "event": "recovery_started", "wal_files": [55]}
2025/08/05-03:29:48.027712 35 [db/db_impl/db_impl_open.cc:888] Recovering log #55 mode 2
2025/08/05-03:29:48.257944 35 EVENT_LOG_v1 {"time_micros": 1754364588257905, "cf_name": "default", "job": 1, "event": "table_file_creation", "file_number": 59, "file_size": 2235369, "file_checksum": "", "file_checksum_func_name": "Unknown", "table_properties": {"data_size": 2232578, "index_size": 1863, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 3430143, "raw_average_key_size": 48, "raw_value_size": 1190961, "raw_average_value_size": 17, "num_data_blocks": 35, "num_entries": 70003, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; ", "creation_time": 1754364588, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "7a97ab61-73d0-446e-8623-a3ceb9aa3e8e", "db_session_id": "SFMW2AWSCE1QC1S44Z6J", "orig_file_number": 59}}
2025/08/05-03:29:48.258306 35 [db/version_set.cc:4409] Creating manifest 60
2025/08/05-03:29:48.280823 35 EVENT_LOG_v1 {"time_micros": 1754364588280813, "job": 1, "event": "recovery_finished"}
2025/08/05-03:29:48.339879 35 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data/000055.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/08/05-03:29:48.341566 35 [db/db_impl/db_impl_open.cc:1820] SstFileManager instance 0x7f08eae80700
2025/08/05-03:29:48.342278 54 [db/compaction/compaction_job.cc:2331] [default] [JOB 3] Compacting 4@0 files to L1, score 1.00
2025/08/05-03:29:48.342321 54 [db/compaction/compaction_job.cc:2337] [default] Compaction start summary: Base version 5 Base level 0, inputs: [59(2182KB) 53(70KB) 47(93KB) 41(124KB)]
2025/08/05-03:29:48.342489 54 EVENT_LOG_v1 {"time_micros": 1754364588342369, "job": 3, "event": "compaction_started", "compaction_reason": "LevelL0FilesNum", "files_L0": [59, 53, 47, 41], "score": 1, "input_data_size": 2530439}
2025/08/05-03:29:48.345236 35 DB pointer 0x7f08eb221c00
2025/08/05-03:29:48.476506 54 [db/compaction/compaction_job.cc:1937] [default] [JOB 3] Generated table #64: 79129 keys, 2527540 bytes
2025/08/05-03:29:48.476581 54 EVENT_LOG_v1 {"time_micros": 1754364588476548, "cf_name": "default", "job": 3, "event": "table_file_creation", "file_number": 64, "file_size": 2527540, "file_checksum": "", "file_checksum_func_name": "Unknown", "table_properties": {"data_size": 2524509, "index_size": 2080, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 3877313, "raw_average_key_size": 48, "raw_value_size": 1347124, "raw_average_value_size": 17, "num_data_blocks": 39, "num_entries": 79129, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; ", "creation_time": 1754314774, "oldest_key_time": 0, "file_creation_time": 1754364588, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "7a97ab61-73d0-446e-8623-a3ceb9aa3e8e", "db_session_id": "SFMW2AWSCE1QC1S44Z6J", "orig_file_number": 64}}
2025/08/05-03:29:48.482592 54 [db/compaction/compaction_job.cc:1998] [default] [JOB 3] Compacted 4@0 files to L1 => 2527540 bytes
2025/08/05-03:29:48.485783 54 (Original Log Time 2025/08/05-03:29:48.485574) [db/compaction/compaction_job.cc:944] [default] compacted to: files[0 1 0 0 0] max score 0.01, MB/sec: 18.7 rd, 18.7 wr, level 1, files in(4, 0) out(1 +0 blob) MB in(2.4, 0.0 +0.0 blob) out(2.4 +0.0 blob), read-write-amplify(2.0) write-amplify(1.0) OK, records in: 79129, records dropped: 0 output_compression: NoCompression
2025/08/05-03:29:48.485791 54 (Original Log Time 2025/08/05-03:29:48.485676) EVENT_LOG_v1 {"time_micros": 1754364588485627, "job": 3, "event": "compaction_finished", "compaction_time_micros": 135033, "compaction_time_cpu_micros": 40454, "output_level": 1, "num_output_files": 1, "total_output_size": 2527540, "num_input_records": 79129, "num_output_records": 79129, "num_subcompactions": 1, "output_compression": "NoCompression", "num_single_delete_mismatches": 0, "num_single_delete_fallthrough": 0, "lsm_state": [0, 1, 0, 0, 0]}
2025/08/05-03:29:48.487663 54 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data/000059.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/08/05-03:29:48.487725 54 EVENT_LOG_v1 {"time_micros": 1754364588487717, "job": 3, "event": "table_file_deletion", "file_number": 59}
2025/08/05-03:29:48.489603 54 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data/000053.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/08/05-03:29:48.489648 54 EVENT_LOG_v1 {"time_micros": 1754364588489640, "job": 3, "event": "table_file_deletion", "file_number": 53}
2025/08/05-03:29:48.491391 54 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data/000047.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/08/05-03:29:48.491427 54 EVENT_LOG_v1 {"time_micros": 1754364588491421, "job": 3, "event": "table_file_deletion", "file_number": 47}
2025/08/05-03:29:48.492871 54 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data/000041.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/08/05-03:29:48.492958 54 EVENT_LOG_v1 {"time_micros": 1754364588492949, "job": 3, "event": "table_file_deletion", "file_number": 41}
2025/08/05-03:29:51.346071 72 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/05-03:29:51.346093 72 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 3.4 total, 3.4 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0
  L1      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
 Sum      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.1     13.0     24.4      0.19              0.04         2    0.093     79K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.1     13.0     24.4      0.19              0.04         2    0.093     79K      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3.4 total, 3.4 interval
Flush(GB): cumulative 0.002, interval 0.002
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 1.33 MB/s write, 0.00 GB read, 0.71 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 1.33 MB/s write, 0.00 GB read, 0.71 MB/s read, 0.2 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 1 last_copies: 2 last_secs: 0.000115 secs_since: 3
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3.4 total, 3.4 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 1 last_copies: 2 last_secs: 0.000115 secs_since: 3
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/08/05-03:39:51.346737 72 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/05-03:39:51.346967 72 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 603.4 total, 600.0 interval
Cumulative writes: 2846 writes, 2846 keys, 2846 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 2846 writes, 0 syncs, 2846.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 2846 writes, 2846 keys, 2846 commit groups, 1.0 writes per commit group, ingest: 0.20 MB, 0.00 MB/s
Interval WAL: 2846 writes, 0 syncs, 2846.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0
  L1      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
 Sum      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.1     13.0     24.4      0.19              0.04         2    0.093     79K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 603.4 total, 600.0 interval
Flush(GB): cumulative 0.002, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.01 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 2 last_copies: 2 last_secs: 9.2e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(3,41.22 KB,0.00421291%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 603.4 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 2 last_copies: 2 last_secs: 9.2e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(3,41.22 KB,0.00421291%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/08/05-03:49:51.347353 72 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/05-03:49:51.347711 72 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 1203.4 total, 600.0 interval
Cumulative writes: 5846 writes, 5846 keys, 5846 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 5846 writes, 0 syncs, 5846.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 3000 writes, 3000 keys, 3000 commit groups, 1.0 writes per commit group, ingest: 0.21 MB, 0.00 MB/s
Interval WAL: 3000 writes, 0 syncs, 3000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0
  L1      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
 Sum      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.1     13.0     24.4      0.19              0.04         2    0.093     79K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 1203.4 total, 600.0 interval
Flush(GB): cumulative 0.002, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 3 last_copies: 2 last_secs: 5e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(3,41.22 KB,0.00421291%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 1203.4 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 3 last_copies: 2 last_secs: 5e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(3,41.22 KB,0.00421291%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/08/05-03:59:51.348314 72 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/05-03:59:51.348730 72 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 1803.4 total, 600.0 interval
Cumulative writes: 8833 writes, 8833 keys, 8833 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 8833 writes, 0 syncs, 8833.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 2987 writes, 2987 keys, 2987 commit groups, 1.0 writes per commit group, ingest: 0.21 MB, 0.00 MB/s
Interval WAL: 2987 writes, 0 syncs, 2987.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0
  L1      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
 Sum      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.1     13.0     24.4      0.19              0.04         2    0.093     79K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 1803.4 total, 600.0 interval
Flush(GB): cumulative 0.002, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 4 last_copies: 2 last_secs: 5.1e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(3,41.22 KB,0.00421291%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 1803.4 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 4 last_copies: 2 last_secs: 5.1e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(3,41.22 KB,0.00421291%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/08/05-04:09:51.349225 72 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/05-04:09:51.349614 72 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 2403.4 total, 600.0 interval
Cumulative writes: 11K writes, 11K keys, 11K commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 11K writes, 0 syncs, 11834.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 3001 writes, 3001 keys, 3001 commit groups, 1.0 writes per commit group, ingest: 0.21 MB, 0.00 MB/s
Interval WAL: 3001 writes, 0 syncs, 3001.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0
  L1      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
 Sum      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.1     13.0     24.4      0.19              0.04         2    0.093     79K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 2403.4 total, 600.0 interval
Flush(GB): cumulative 0.002, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 5 last_copies: 2 last_secs: 7.5e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(3,41.22 KB,0.00421291%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 2403.4 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 5 last_copies: 2 last_secs: 7.5e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(3,41.22 KB,0.00421291%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/08/05-04:19:51.350120 72 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/05-04:19:51.350422 72 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 3003.4 total, 600.0 interval
Cumulative writes: 14K writes, 14K keys, 14K commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 14K writes, 0 syncs, 14834.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 3000 writes, 3000 keys, 3000 commit groups, 1.0 writes per commit group, ingest: 0.21 MB, 0.00 MB/s
Interval WAL: 3000 writes, 0 syncs, 3000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0
  L1      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
 Sum      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.1     13.0     24.4      0.19              0.04         2    0.093     79K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3003.4 total, 600.0 interval
Flush(GB): cumulative 0.002, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 6 last_copies: 2 last_secs: 4.1e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(3,41.22 KB,0.00421291%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3003.4 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 6 last_copies: 2 last_secs: 4.1e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(3,41.22 KB,0.00421291%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/08/05-04:29:51.351030 72 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/05-04:29:51.351881 72 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 3603.4 total, 600.0 interval
Cumulative writes: 17K writes, 17K keys, 17K commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 17K writes, 0 syncs, 17834.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 3000 writes, 3000 keys, 3000 commit groups, 1.0 writes per commit group, ingest: 0.21 MB, 0.00 MB/s
Interval WAL: 3000 writes, 0 syncs, 3000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0
  L1      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
 Sum      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.1     13.0     24.4      0.19              0.04         2    0.093     79K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3603.4 total, 600.0 interval
Flush(GB): cumulative 0.002, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 7 last_copies: 2 last_secs: 4.5e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(3,41.22 KB,0.00421291%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3603.4 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 7 last_copies: 2 last_secs: 4.5e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(3,41.22 KB,0.00421291%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/08/05-04:39:51.353069 72 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/05-04:39:51.354131 72 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 4203.4 total, 600.0 interval
Cumulative writes: 20K writes, 20K keys, 20K commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 20K writes, 0 syncs, 20834.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 3000 writes, 3000 keys, 3000 commit groups, 1.0 writes per commit group, ingest: 0.21 MB, 0.00 MB/s
Interval WAL: 3000 writes, 0 syncs, 3000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0
  L1      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
 Sum      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.1     13.0     24.4      0.19              0.04         2    0.093     79K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 4203.4 total, 600.0 interval
Flush(GB): cumulative 0.002, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 8 last_copies: 2 last_secs: 0.000437 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(3,41.22 KB,0.00421291%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 4203.4 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 8 last_copies: 2 last_secs: 0.000437 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(3,41.22 KB,0.00421291%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/08/05-04:49:51.354930 72 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/05-04:49:51.355307 72 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 4803.4 total, 600.0 interval
Cumulative writes: 23K writes, 23K keys, 23K commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 23K writes, 0 syncs, 23835.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 3001 writes, 3001 keys, 3001 commit groups, 1.0 writes per commit group, ingest: 0.21 MB, 0.00 MB/s
Interval WAL: 3001 writes, 0 syncs, 3001.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0
  L1      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
 Sum      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.1     13.0     24.4      0.19              0.04         2    0.093     79K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 4803.4 total, 600.0 interval
Flush(GB): cumulative 0.002, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 9 last_copies: 2 last_secs: 5.9e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(3,41.22 KB,0.00421291%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 4803.4 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 9 last_copies: 2 last_secs: 5.9e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(3,41.22 KB,0.00421291%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/08/05-04:59:51.355756 72 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/05-04:59:51.356092 72 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 5403.4 total, 600.0 interval
Cumulative writes: 26K writes, 26K keys, 26K commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 26K writes, 0 syncs, 26835.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 3000 writes, 3000 keys, 3000 commit groups, 1.0 writes per commit group, ingest: 0.21 MB, 0.00 MB/s
Interval WAL: 3000 writes, 0 syncs, 3000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0
  L1      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
 Sum      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.1     13.0     24.4      0.19              0.04         2    0.093     79K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 5403.4 total, 600.0 interval
Flush(GB): cumulative 0.002, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 10 last_copies: 2 last_secs: 4.1e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(3,41.22 KB,0.00421291%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 5403.4 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 10 last_copies: 2 last_secs: 4.1e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(3,41.22 KB,0.00421291%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/08/05-05:09:51.356457 72 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/05-05:09:51.356858 72 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 6003.4 total, 600.0 interval
Cumulative writes: 29K writes, 29K keys, 29K commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 29K writes, 0 syncs, 29835.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 3000 writes, 3000 keys, 3000 commit groups, 1.0 writes per commit group, ingest: 0.21 MB, 0.00 MB/s
Interval WAL: 3000 writes, 0 syncs, 3000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0
  L1      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
 Sum      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.1     13.0     24.4      0.19              0.04         2    0.093     79K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 6003.4 total, 600.0 interval
Flush(GB): cumulative 0.002, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 11 last_copies: 2 last_secs: 0.000203 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(3,41.22 KB,0.00421291%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 6003.4 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 11 last_copies: 2 last_secs: 0.000203 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(3,41.22 KB,0.00421291%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/08/05-05:19:51.357313 72 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/05-05:19:51.357730 72 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 6603.4 total, 600.0 interval
Cumulative writes: 32K writes, 32K keys, 32K commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 32K writes, 0 syncs, 32835.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 3000 writes, 3000 keys, 3000 commit groups, 1.0 writes per commit group, ingest: 0.21 MB, 0.00 MB/s
Interval WAL: 3000 writes, 0 syncs, 3000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0
  L1      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
 Sum      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.1     13.0     24.4      0.19              0.04         2    0.093     79K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 6603.4 total, 600.0 interval
Flush(GB): cumulative 0.002, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 12 last_copies: 2 last_secs: 4.1e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(3,41.22 KB,0.00421291%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 6603.4 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 12 last_copies: 2 last_secs: 4.1e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(3,41.22 KB,0.00421291%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/08/05-05:29:51.358273 72 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/05-05:29:51.358616 72 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 7203.4 total, 600.0 interval
Cumulative writes: 35K writes, 35K keys, 35K commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 35K writes, 0 syncs, 35835.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 3000 writes, 3000 keys, 3000 commit groups, 1.0 writes per commit group, ingest: 0.21 MB, 0.00 MB/s
Interval WAL: 3000 writes, 0 syncs, 3000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0
  L1      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
 Sum      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.1     13.0     24.4      0.19              0.04         2    0.093     79K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 7203.4 total, 600.0 interval
Flush(GB): cumulative 0.002, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 13 last_copies: 2 last_secs: 4.6e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(3,41.22 KB,0.00421291%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 7203.4 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 13 last_copies: 2 last_secs: 4.6e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(3,41.22 KB,0.00421291%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/08/05-05:39:51.359140 72 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/05-05:39:51.359589 72 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 7803.4 total, 600.0 interval
Cumulative writes: 38K writes, 38K keys, 38K commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 38K writes, 0 syncs, 38835.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 3000 writes, 3000 keys, 3000 commit groups, 1.0 writes per commit group, ingest: 0.21 MB, 0.00 MB/s
Interval WAL: 3000 writes, 0 syncs, 3000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0
  L1      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
 Sum      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.1     13.0     24.4      0.19              0.04         2    0.093     79K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 7803.4 total, 600.0 interval
Flush(GB): cumulative 0.002, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 14 last_copies: 2 last_secs: 4.1e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(3,41.22 KB,0.00421291%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 7803.4 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 14 last_copies: 2 last_secs: 4.1e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(3,41.22 KB,0.00421291%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/08/05-05:49:51.360036 72 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/05-05:49:51.360299 72 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 8403.4 total, 600.0 interval
Cumulative writes: 41K writes, 41K keys, 41K commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 41K writes, 0 syncs, 41823.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 2988 writes, 2988 keys, 2988 commit groups, 1.0 writes per commit group, ingest: 0.21 MB, 0.00 MB/s
Interval WAL: 2988 writes, 0 syncs, 2988.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0
  L1      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
 Sum      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.1     13.0     24.4      0.19              0.04         2    0.093     79K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 8403.4 total, 600.0 interval
Flush(GB): cumulative 0.002, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 15 last_copies: 2 last_secs: 4e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(3,41.22 KB,0.00421291%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 8403.4 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 15 last_copies: 2 last_secs: 4e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(3,41.22 KB,0.00421291%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/08/05-05:59:51.360804 72 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/05-05:59:51.361639 72 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 9003.4 total, 600.0 interval
Cumulative writes: 44K writes, 44K keys, 44K commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 44K writes, 0 syncs, 44824.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 3001 writes, 3001 keys, 3001 commit groups, 1.0 writes per commit group, ingest: 0.21 MB, 0.00 MB/s
Interval WAL: 3001 writes, 0 syncs, 3001.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0
  L1      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
 Sum      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.1     13.0     24.4      0.19              0.04         2    0.093     79K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 9003.4 total, 600.0 interval
Flush(GB): cumulative 0.002, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 16 last_copies: 2 last_secs: 3.9e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(3,41.22 KB,0.00421291%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 9003.4 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 16 last_copies: 2 last_secs: 3.9e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(3,41.22 KB,0.00421291%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/08/05-06:09:51.362543 72 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/05-06:09:51.362925 72 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 9603.4 total, 600.0 interval
Cumulative writes: 47K writes, 47K keys, 47K commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 47K writes, 0 syncs, 47824.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 3000 writes, 3000 keys, 3000 commit groups, 1.0 writes per commit group, ingest: 0.21 MB, 0.00 MB/s
Interval WAL: 3000 writes, 0 syncs, 3000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0
  L1      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
 Sum      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.1     13.0     24.4      0.19              0.04         2    0.093     79K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 9603.4 total, 600.0 interval
Flush(GB): cumulative 0.002, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 17 last_copies: 2 last_secs: 4.3e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(3,41.22 KB,0.00421291%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 9603.4 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 17 last_copies: 2 last_secs: 4.3e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(3,41.22 KB,0.00421291%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/08/05-06:19:51.363516 72 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/05-06:19:51.363837 72 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 10203.4 total, 600.0 interval
Cumulative writes: 50K writes, 50K keys, 50K commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 50K writes, 0 syncs, 50824.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 3000 writes, 3000 keys, 3000 commit groups, 1.0 writes per commit group, ingest: 0.21 MB, 0.00 MB/s
Interval WAL: 3000 writes, 0 syncs, 3000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0
  L1      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
 Sum      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.1     13.0     24.4      0.19              0.04         2    0.093     79K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 10203.4 total, 600.0 interval
Flush(GB): cumulative 0.002, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 18 last_copies: 2 last_secs: 3.9e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(3,41.22 KB,0.00421291%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 10203.4 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 18 last_copies: 2 last_secs: 3.9e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(3,41.22 KB,0.00421291%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/08/05-06:29:51.364400 72 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/05-06:29:51.364804 72 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 10803.4 total, 600.0 interval
Cumulative writes: 53K writes, 53K keys, 53K commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 53K writes, 0 syncs, 53824.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 3000 writes, 3000 keys, 3000 commit groups, 1.0 writes per commit group, ingest: 0.21 MB, 0.00 MB/s
Interval WAL: 3000 writes, 0 syncs, 3000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0
  L1      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
 Sum      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.1     13.0     24.4      0.19              0.04         2    0.093     79K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 10803.4 total, 600.0 interval
Flush(GB): cumulative 0.002, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 19 last_copies: 2 last_secs: 7e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(3,41.22 KB,0.00421291%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 10803.4 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 19 last_copies: 2 last_secs: 7e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(3,41.22 KB,0.00421291%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/08/05-06:39:51.365634 72 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/05-06:39:51.366142 72 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 11403.4 total, 600.0 interval
Cumulative writes: 56K writes, 56K keys, 56K commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 56K writes, 0 syncs, 56824.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 3000 writes, 3000 keys, 3000 commit groups, 1.0 writes per commit group, ingest: 0.21 MB, 0.00 MB/s
Interval WAL: 3000 writes, 0 syncs, 3000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0
  L1      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
 Sum      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.1     13.0     24.4      0.19              0.04         2    0.093     79K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 11403.4 total, 600.0 interval
Flush(GB): cumulative 0.002, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 20 last_copies: 2 last_secs: 4.7e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(3,41.22 KB,0.00421291%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 11403.4 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 20 last_copies: 2 last_secs: 4.7e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(3,41.22 KB,0.00421291%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/08/05-06:49:51.366831 72 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/05-06:49:51.367072 72 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 12003.4 total, 600.0 interval
Cumulative writes: 59K writes, 59K keys, 59K commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 59K writes, 0 syncs, 59824.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 3000 writes, 3000 keys, 3000 commit groups, 1.0 writes per commit group, ingest: 0.21 MB, 0.00 MB/s
Interval WAL: 3000 writes, 0 syncs, 3000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0
  L1      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
 Sum      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.1     13.0     24.4      0.19              0.04         2    0.093     79K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 12003.4 total, 600.0 interval
Flush(GB): cumulative 0.002, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 21 last_copies: 2 last_secs: 3.9e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(3,41.22 KB,0.00421291%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 12003.4 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 21 last_copies: 2 last_secs: 3.9e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(3,41.22 KB,0.00421291%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/08/05-06:59:51.367430 72 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/05-06:59:51.367798 72 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 12603.4 total, 600.0 interval
Cumulative writes: 62K writes, 62K keys, 62K commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 62K writes, 0 syncs, 62825.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 3001 writes, 3001 keys, 3001 commit groups, 1.0 writes per commit group, ingest: 0.21 MB, 0.00 MB/s
Interval WAL: 3001 writes, 0 syncs, 3001.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0
  L1      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
 Sum      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.1     13.0     24.4      0.19              0.04         2    0.093     79K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 12603.4 total, 600.0 interval
Flush(GB): cumulative 0.002, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 22 last_copies: 2 last_secs: 4.5e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(3,41.22 KB,0.00421291%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 12603.4 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 22 last_copies: 2 last_secs: 4.5e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(3,41.22 KB,0.00421291%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/08/05-07:09:51.368205 72 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/05-07:09:51.368531 72 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 13203.4 total, 600.0 interval
Cumulative writes: 65K writes, 65K keys, 65K commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 65K writes, 0 syncs, 65825.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 3000 writes, 3000 keys, 3000 commit groups, 1.0 writes per commit group, ingest: 0.21 MB, 0.00 MB/s
Interval WAL: 3000 writes, 0 syncs, 3000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0
  L1      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
 Sum      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.1     13.0     24.4      0.19              0.04         2    0.093     79K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 13203.4 total, 600.0 interval
Flush(GB): cumulative 0.002, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 23 last_copies: 2 last_secs: 7.9e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(3,41.22 KB,0.00421291%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 13203.4 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 23 last_copies: 2 last_secs: 7.9e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(3,41.22 KB,0.00421291%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/08/05-07:19:51.368980 72 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/05-07:19:51.369306 72 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 13803.4 total, 600.0 interval
Cumulative writes: 68K writes, 68K keys, 68K commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 68K writes, 0 syncs, 68825.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 3000 writes, 3000 keys, 3000 commit groups, 1.0 writes per commit group, ingest: 0.21 MB, 0.00 MB/s
Interval WAL: 3000 writes, 0 syncs, 3000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0
  L1      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
 Sum      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.1     13.0     24.4      0.19              0.04         2    0.093     79K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 13803.4 total, 600.0 interval
Flush(GB): cumulative 0.002, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 24 last_copies: 2 last_secs: 0.00011 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(3,41.22 KB,0.00421291%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 13803.4 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 24 last_copies: 2 last_secs: 0.00011 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(3,41.22 KB,0.00421291%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/08/05-07:29:51.369765 72 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/05-07:29:51.370212 72 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 14403.4 total, 600.0 interval
Cumulative writes: 71K writes, 71K keys, 71K commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 71K writes, 0 syncs, 71825.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 3000 writes, 3000 keys, 3000 commit groups, 1.0 writes per commit group, ingest: 0.21 MB, 0.00 MB/s
Interval WAL: 3000 writes, 0 syncs, 3000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0
  L1      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
 Sum      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.1     13.0     24.4      0.19              0.04         2    0.093     79K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 14403.4 total, 600.0 interval
Flush(GB): cumulative 0.002, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 25 last_copies: 2 last_secs: 9.8e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(3,41.22 KB,0.00421291%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 14403.4 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 25 last_copies: 2 last_secs: 9.8e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(3,41.22 KB,0.00421291%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/08/05-07:39:51.370931 72 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/05-07:39:51.371505 72 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 15003.4 total, 600.0 interval
Cumulative writes: 74K writes, 74K keys, 74K commit groups, 1.0 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 74K writes, 0 syncs, 74818.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 2993 writes, 2993 keys, 2993 commit groups, 1.0 writes per commit group, ingest: 0.21 MB, 0.00 MB/s
Interval WAL: 2993 writes, 0 syncs, 2993.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0
  L1      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
 Sum      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.1     13.0     24.4      0.19              0.04         2    0.093     79K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 15003.4 total, 600.0 interval
Flush(GB): cumulative 0.002, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 26 last_copies: 2 last_secs: 7.4e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(3,41.22 KB,0.00421291%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 15003.4 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 26 last_copies: 2 last_secs: 7.4e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(3,41.22 KB,0.00421291%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/08/05-07:49:51.372402 72 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/05-07:49:51.374611 72 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 15603.4 total, 600.0 interval
Cumulative writes: 77K writes, 77K keys, 77K commit groups, 1.0 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 77K writes, 0 syncs, 77818.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 3000 writes, 3000 keys, 3000 commit groups, 1.0 writes per commit group, ingest: 0.21 MB, 0.00 MB/s
Interval WAL: 3000 writes, 0 syncs, 3000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0
  L1      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
 Sum      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.1     13.0     24.4      0.19              0.04         2    0.093     79K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 15603.4 total, 600.0 interval
Flush(GB): cumulative 0.002, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 27 last_copies: 2 last_secs: 3.9e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(3,41.22 KB,0.00421291%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 15603.4 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 27 last_copies: 2 last_secs: 3.9e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(3,41.22 KB,0.00421291%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/08/05-07:59:51.376708 72 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/05-07:59:51.377678 72 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 16203.4 total, 600.0 interval
Cumulative writes: 80K writes, 80K keys, 80K commit groups, 1.0 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 80K writes, 0 syncs, 80819.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 3001 writes, 3001 keys, 3001 commit groups, 1.0 writes per commit group, ingest: 0.21 MB, 0.00 MB/s
Interval WAL: 3001 writes, 0 syncs, 3001.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0
  L1      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
 Sum      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.1     13.0     24.4      0.19              0.04         2    0.093     79K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 16203.4 total, 600.0 interval
Flush(GB): cumulative 0.002, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 28 last_copies: 2 last_secs: 3.7e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(3,41.22 KB,0.00421291%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 16203.4 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 28 last_copies: 2 last_secs: 3.7e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(3,41.22 KB,0.00421291%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/08/05-08:09:51.378219 72 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/05-08:09:51.378546 72 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 16803.4 total, 600.0 interval
Cumulative writes: 83K writes, 83K keys, 83K commit groups, 1.0 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 83K writes, 0 syncs, 83819.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 3000 writes, 3000 keys, 3000 commit groups, 1.0 writes per commit group, ingest: 0.21 MB, 0.00 MB/s
Interval WAL: 3000 writes, 0 syncs, 3000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0
  L1      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
 Sum      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.1     13.0     24.4      0.19              0.04         2    0.093     79K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 16803.4 total, 600.0 interval
Flush(GB): cumulative 0.002, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 29 last_copies: 2 last_secs: 4.4e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(3,41.22 KB,0.00421291%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 16803.4 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 29 last_copies: 2 last_secs: 4.4e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(3,41.22 KB,0.00421291%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/08/05-08:19:51.379246 72 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/05-08:19:51.379573 72 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 17403.4 total, 600.0 interval
Cumulative writes: 86K writes, 86K keys, 86K commit groups, 1.0 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 86K writes, 0 syncs, 86819.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 3000 writes, 3000 keys, 3000 commit groups, 1.0 writes per commit group, ingest: 0.21 MB, 0.00 MB/s
Interval WAL: 3000 writes, 0 syncs, 3000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0
  L1      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
 Sum      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.1     13.0     24.4      0.19              0.04         2    0.093     79K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 17403.4 total, 600.0 interval
Flush(GB): cumulative 0.002, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 30 last_copies: 2 last_secs: 4.3e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(3,41.22 KB,0.00421291%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 17403.4 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 30 last_copies: 2 last_secs: 4.3e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(3,41.22 KB,0.00421291%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/08/05-08:29:51.380006 72 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/05-08:29:51.380399 72 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 18003.4 total, 600.0 interval
Cumulative writes: 89K writes, 89K keys, 89K commit groups, 1.0 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 89K writes, 0 syncs, 89819.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 3000 writes, 3000 keys, 3000 commit groups, 1.0 writes per commit group, ingest: 0.21 MB, 0.00 MB/s
Interval WAL: 3000 writes, 0 syncs, 3000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0
  L1      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
 Sum      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.1     13.0     24.4      0.19              0.04         2    0.093     79K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 18003.4 total, 600.0 interval
Flush(GB): cumulative 0.002, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 31 last_copies: 2 last_secs: 8.1e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(3,41.22 KB,0.00421291%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 18003.4 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 31 last_copies: 2 last_secs: 8.1e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(3,41.22 KB,0.00421291%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/08/05-08:39:51.380825 72 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/05-08:39:51.381085 72 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 18603.4 total, 600.0 interval
Cumulative writes: 92K writes, 92K keys, 92K commit groups, 1.0 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 92K writes, 0 syncs, 92820.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 3001 writes, 3001 keys, 3001 commit groups, 1.0 writes per commit group, ingest: 0.21 MB, 0.00 MB/s
Interval WAL: 3001 writes, 0 syncs, 3001.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0
  L1      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
 Sum      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.1     13.0     24.4      0.19              0.04         2    0.093     79K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 18603.4 total, 600.0 interval
Flush(GB): cumulative 0.002, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 32 last_copies: 2 last_secs: 4e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(3,41.22 KB,0.00421291%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 18603.4 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 32 last_copies: 2 last_secs: 4e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(3,41.22 KB,0.00421291%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/08/05-08:49:51.381495 72 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/05-08:49:51.381781 72 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 19203.4 total, 600.0 interval
Cumulative writes: 95K writes, 95K keys, 95K commit groups, 1.0 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 95K writes, 0 syncs, 95807.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 2987 writes, 2987 keys, 2987 commit groups, 1.0 writes per commit group, ingest: 0.21 MB, 0.00 MB/s
Interval WAL: 2987 writes, 0 syncs, 2987.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0
  L1      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
 Sum      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.1     13.0     24.4      0.19              0.04         2    0.093     79K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 19203.4 total, 600.0 interval
Flush(GB): cumulative 0.002, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 33 last_copies: 2 last_secs: 4.1e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(3,41.22 KB,0.00421291%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 19203.4 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 33 last_copies: 2 last_secs: 4.1e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(3,41.22 KB,0.00421291%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/08/05-08:59:51.382757 72 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/05-08:59:51.383583 72 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 19803.4 total, 600.0 interval
Cumulative writes: 98K writes, 98K keys, 98K commit groups, 1.0 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 98K writes, 0 syncs, 98807.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 3000 writes, 3000 keys, 3000 commit groups, 1.0 writes per commit group, ingest: 0.21 MB, 0.00 MB/s
Interval WAL: 3000 writes, 0 syncs, 3000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0
  L1      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
 Sum      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.1     13.0     24.4      0.19              0.04         2    0.093     79K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 19803.4 total, 600.0 interval
Flush(GB): cumulative 0.002, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 34 last_copies: 2 last_secs: 3.9e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(3,41.22 KB,0.00421291%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 19803.4 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 34 last_copies: 2 last_secs: 3.9e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(3,41.22 KB,0.00421291%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/08/05-09:09:51.384395 72 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/05-09:09:51.384709 72 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 20403.4 total, 600.0 interval
Cumulative writes: 101K writes, 101K keys, 101K commit groups, 1.0 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 101K writes, 0 syncs, 101806.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 2999 writes, 2999 keys, 2999 commit groups, 1.0 writes per commit group, ingest: 0.21 MB, 0.00 MB/s
Interval WAL: 2999 writes, 0 syncs, 2999.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0
  L1      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
 Sum      1/0    2.41 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.1     13.0     24.4      0.19              0.04         2    0.093     79K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0     17.9     17.9      0.14              0.04         1    0.135     79K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.6      0.05              0.00         1    0.051       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 20403.4 total, 600.0 interval
Flush(GB): cumulative 0.002, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 35 last_copies: 2 last_secs: 3.9e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(3,41.22 KB,0.00421291%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 20403.4 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f08f3660010#9 capacity: 955.46 MB collections: 35 last_copies: 2 last_secs: 3.9e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(3,41.22 KB,0.00421291%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
