# coding: utf-8

"""
    privatelink

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribePrivateLinkGatewayAttributesResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'business_status': 'str',
        'creation_time': 'str',
        'deleted_time': 'str',
        'description': 'str',
        'network_interface_id': 'str',
        'network_interface_ip': 'str',
        'private_link_gateway_id': 'str',
        'private_link_gateway_name': 'str',
        'request_id': 'str',
        'status': 'str',
        'subnet_id': 'str',
        'update_time': 'str',
        'vpc_id': 'str',
        'zone_id': 'str'
    }

    attribute_map = {
        'business_status': 'BusinessStatus',
        'creation_time': 'CreationTime',
        'deleted_time': 'DeletedTime',
        'description': 'Description',
        'network_interface_id': 'NetworkInterfaceId',
        'network_interface_ip': 'NetworkInterfaceIp',
        'private_link_gateway_id': 'PrivateLinkGatewayId',
        'private_link_gateway_name': 'PrivateLinkGatewayName',
        'request_id': 'RequestId',
        'status': 'Status',
        'subnet_id': 'SubnetId',
        'update_time': 'UpdateTime',
        'vpc_id': 'VpcId',
        'zone_id': 'ZoneId'
    }

    def __init__(self, business_status=None, creation_time=None, deleted_time=None, description=None, network_interface_id=None, network_interface_ip=None, private_link_gateway_id=None, private_link_gateway_name=None, request_id=None, status=None, subnet_id=None, update_time=None, vpc_id=None, zone_id=None, _configuration=None):  # noqa: E501
        """DescribePrivateLinkGatewayAttributesResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._business_status = None
        self._creation_time = None
        self._deleted_time = None
        self._description = None
        self._network_interface_id = None
        self._network_interface_ip = None
        self._private_link_gateway_id = None
        self._private_link_gateway_name = None
        self._request_id = None
        self._status = None
        self._subnet_id = None
        self._update_time = None
        self._vpc_id = None
        self._zone_id = None
        self.discriminator = None

        if business_status is not None:
            self.business_status = business_status
        if creation_time is not None:
            self.creation_time = creation_time
        if deleted_time is not None:
            self.deleted_time = deleted_time
        if description is not None:
            self.description = description
        if network_interface_id is not None:
            self.network_interface_id = network_interface_id
        if network_interface_ip is not None:
            self.network_interface_ip = network_interface_ip
        if private_link_gateway_id is not None:
            self.private_link_gateway_id = private_link_gateway_id
        if private_link_gateway_name is not None:
            self.private_link_gateway_name = private_link_gateway_name
        if request_id is not None:
            self.request_id = request_id
        if status is not None:
            self.status = status
        if subnet_id is not None:
            self.subnet_id = subnet_id
        if update_time is not None:
            self.update_time = update_time
        if vpc_id is not None:
            self.vpc_id = vpc_id
        if zone_id is not None:
            self.zone_id = zone_id

    @property
    def business_status(self):
        """Gets the business_status of this DescribePrivateLinkGatewayAttributesResponse.  # noqa: E501


        :return: The business_status of this DescribePrivateLinkGatewayAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._business_status

    @business_status.setter
    def business_status(self, business_status):
        """Sets the business_status of this DescribePrivateLinkGatewayAttributesResponse.


        :param business_status: The business_status of this DescribePrivateLinkGatewayAttributesResponse.  # noqa: E501
        :type: str
        """

        self._business_status = business_status

    @property
    def creation_time(self):
        """Gets the creation_time of this DescribePrivateLinkGatewayAttributesResponse.  # noqa: E501


        :return: The creation_time of this DescribePrivateLinkGatewayAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._creation_time

    @creation_time.setter
    def creation_time(self, creation_time):
        """Sets the creation_time of this DescribePrivateLinkGatewayAttributesResponse.


        :param creation_time: The creation_time of this DescribePrivateLinkGatewayAttributesResponse.  # noqa: E501
        :type: str
        """

        self._creation_time = creation_time

    @property
    def deleted_time(self):
        """Gets the deleted_time of this DescribePrivateLinkGatewayAttributesResponse.  # noqa: E501


        :return: The deleted_time of this DescribePrivateLinkGatewayAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._deleted_time

    @deleted_time.setter
    def deleted_time(self, deleted_time):
        """Sets the deleted_time of this DescribePrivateLinkGatewayAttributesResponse.


        :param deleted_time: The deleted_time of this DescribePrivateLinkGatewayAttributesResponse.  # noqa: E501
        :type: str
        """

        self._deleted_time = deleted_time

    @property
    def description(self):
        """Gets the description of this DescribePrivateLinkGatewayAttributesResponse.  # noqa: E501


        :return: The description of this DescribePrivateLinkGatewayAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this DescribePrivateLinkGatewayAttributesResponse.


        :param description: The description of this DescribePrivateLinkGatewayAttributesResponse.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def network_interface_id(self):
        """Gets the network_interface_id of this DescribePrivateLinkGatewayAttributesResponse.  # noqa: E501


        :return: The network_interface_id of this DescribePrivateLinkGatewayAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._network_interface_id

    @network_interface_id.setter
    def network_interface_id(self, network_interface_id):
        """Sets the network_interface_id of this DescribePrivateLinkGatewayAttributesResponse.


        :param network_interface_id: The network_interface_id of this DescribePrivateLinkGatewayAttributesResponse.  # noqa: E501
        :type: str
        """

        self._network_interface_id = network_interface_id

    @property
    def network_interface_ip(self):
        """Gets the network_interface_ip of this DescribePrivateLinkGatewayAttributesResponse.  # noqa: E501


        :return: The network_interface_ip of this DescribePrivateLinkGatewayAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._network_interface_ip

    @network_interface_ip.setter
    def network_interface_ip(self, network_interface_ip):
        """Sets the network_interface_ip of this DescribePrivateLinkGatewayAttributesResponse.


        :param network_interface_ip: The network_interface_ip of this DescribePrivateLinkGatewayAttributesResponse.  # noqa: E501
        :type: str
        """

        self._network_interface_ip = network_interface_ip

    @property
    def private_link_gateway_id(self):
        """Gets the private_link_gateway_id of this DescribePrivateLinkGatewayAttributesResponse.  # noqa: E501


        :return: The private_link_gateway_id of this DescribePrivateLinkGatewayAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._private_link_gateway_id

    @private_link_gateway_id.setter
    def private_link_gateway_id(self, private_link_gateway_id):
        """Sets the private_link_gateway_id of this DescribePrivateLinkGatewayAttributesResponse.


        :param private_link_gateway_id: The private_link_gateway_id of this DescribePrivateLinkGatewayAttributesResponse.  # noqa: E501
        :type: str
        """

        self._private_link_gateway_id = private_link_gateway_id

    @property
    def private_link_gateway_name(self):
        """Gets the private_link_gateway_name of this DescribePrivateLinkGatewayAttributesResponse.  # noqa: E501


        :return: The private_link_gateway_name of this DescribePrivateLinkGatewayAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._private_link_gateway_name

    @private_link_gateway_name.setter
    def private_link_gateway_name(self, private_link_gateway_name):
        """Sets the private_link_gateway_name of this DescribePrivateLinkGatewayAttributesResponse.


        :param private_link_gateway_name: The private_link_gateway_name of this DescribePrivateLinkGatewayAttributesResponse.  # noqa: E501
        :type: str
        """

        self._private_link_gateway_name = private_link_gateway_name

    @property
    def request_id(self):
        """Gets the request_id of this DescribePrivateLinkGatewayAttributesResponse.  # noqa: E501


        :return: The request_id of this DescribePrivateLinkGatewayAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._request_id

    @request_id.setter
    def request_id(self, request_id):
        """Sets the request_id of this DescribePrivateLinkGatewayAttributesResponse.


        :param request_id: The request_id of this DescribePrivateLinkGatewayAttributesResponse.  # noqa: E501
        :type: str
        """

        self._request_id = request_id

    @property
    def status(self):
        """Gets the status of this DescribePrivateLinkGatewayAttributesResponse.  # noqa: E501


        :return: The status of this DescribePrivateLinkGatewayAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this DescribePrivateLinkGatewayAttributesResponse.


        :param status: The status of this DescribePrivateLinkGatewayAttributesResponse.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def subnet_id(self):
        """Gets the subnet_id of this DescribePrivateLinkGatewayAttributesResponse.  # noqa: E501


        :return: The subnet_id of this DescribePrivateLinkGatewayAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._subnet_id

    @subnet_id.setter
    def subnet_id(self, subnet_id):
        """Sets the subnet_id of this DescribePrivateLinkGatewayAttributesResponse.


        :param subnet_id: The subnet_id of this DescribePrivateLinkGatewayAttributesResponse.  # noqa: E501
        :type: str
        """

        self._subnet_id = subnet_id

    @property
    def update_time(self):
        """Gets the update_time of this DescribePrivateLinkGatewayAttributesResponse.  # noqa: E501


        :return: The update_time of this DescribePrivateLinkGatewayAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this DescribePrivateLinkGatewayAttributesResponse.


        :param update_time: The update_time of this DescribePrivateLinkGatewayAttributesResponse.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    @property
    def vpc_id(self):
        """Gets the vpc_id of this DescribePrivateLinkGatewayAttributesResponse.  # noqa: E501


        :return: The vpc_id of this DescribePrivateLinkGatewayAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._vpc_id

    @vpc_id.setter
    def vpc_id(self, vpc_id):
        """Sets the vpc_id of this DescribePrivateLinkGatewayAttributesResponse.


        :param vpc_id: The vpc_id of this DescribePrivateLinkGatewayAttributesResponse.  # noqa: E501
        :type: str
        """

        self._vpc_id = vpc_id

    @property
    def zone_id(self):
        """Gets the zone_id of this DescribePrivateLinkGatewayAttributesResponse.  # noqa: E501


        :return: The zone_id of this DescribePrivateLinkGatewayAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._zone_id

    @zone_id.setter
    def zone_id(self, zone_id):
        """Sets the zone_id of this DescribePrivateLinkGatewayAttributesResponse.


        :param zone_id: The zone_id of this DescribePrivateLinkGatewayAttributesResponse.  # noqa: E501
        :type: str
        """

        self._zone_id = zone_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribePrivateLinkGatewayAttributesResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribePrivateLinkGatewayAttributesResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribePrivateLinkGatewayAttributesResponse):
            return True

        return self.to_dict() != other.to_dict()
