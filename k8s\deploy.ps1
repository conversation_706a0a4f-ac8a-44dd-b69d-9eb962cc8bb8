# PowerShell 脚本 - 一键部署到 Kubernetes
param(
    [switch]$Build = $false,
    [switch]$Clean = $false,
    [string]$Namespace = "paper-services"
)

$ErrorActionPreference = "Stop"

Write-Host "=== Paper Services K8s 部署脚本 ===" -ForegroundColor Green

# 检查 kubectl 是否可用
try {
    kubectl version --client | Out-Null
} catch {
    Write-Error "kubectl 未安装或不可用，请先安装 kubectl"
    exit 1
}

# 检查 Kubernetes 集群是否可用
try {
    kubectl cluster-info | Out-Null
    Write-Host "✓ Kubernetes 集群连接正常" -ForegroundColor Green
} catch {
    Write-Error "无法连接到 Kubernetes 集群，请确保 Docker Desktop 的 Kubernetes 已启用或 Minikube 正在运行"
    exit 1
}

# 清理现有部署
if ($Clean) {
    Write-Host "清理现有部署..." -ForegroundColor Yellow
    kubectl delete namespace $Namespace --ignore-not-found=true
    Write-Host "等待命名空间删除完成..." -ForegroundColor Yellow
    Start-Sleep -Seconds 10
}

# 构建镜像
if ($Build) {
    Write-Host "构建 Docker 镜像..." -ForegroundColor Yellow
    & "$PSScriptRoot\build-images.ps1"
    if ($LASTEXITCODE -ne 0) {
        Write-Error "镜像构建失败"
        exit 1
    }
}

# 创建命名空间
Write-Host "创建命名空间..." -ForegroundColor Yellow
kubectl apply -f "$PSScriptRoot\namespace.yaml"

# 部署存储
Write-Host "部署存储..." -ForegroundColor Yellow
kubectl apply -f "$PSScriptRoot\storage-persistentvolumeclaim.yaml" -n $Namespace

# 部署服务配置
Write-Host "部署服务配置..." -ForegroundColor Yellow
kubectl apply -f "$PSScriptRoot\services-config.yaml" -n $Namespace

# 部署各个服务
Write-Host "部署 paper-editor-api..." -ForegroundColor Yellow
kubectl apply -f "$PSScriptRoot\paper-editor-api-deployment.yaml" -n $Namespace

Write-Host "部署 paper-node-service..." -ForegroundColor Yellow
kubectl apply -f "$PSScriptRoot\paper-node-service-deployment.yaml" -n $Namespace

Write-Host "部署 paper-py-service..." -ForegroundColor Yellow
kubectl apply -f "$PSScriptRoot\paper-py-service-deployment.yaml" -n $Namespace

Write-Host "部署 lunwen-generate-ui..." -ForegroundColor Yellow
kubectl apply -f "$PSScriptRoot\lunwen-generate-ui-deployment.yaml" -n $Namespace

# 部署 paper-api-service (如果存在)
if (Test-Path "$PSScriptRoot\paper-api-service-deplyment.yml") {
    Write-Host "部署 paper-api-service..." -ForegroundColor Yellow
    kubectl apply -f "$PSScriptRoot\paper-api-service-deplyment.yml" -n $Namespace
}

# 等待 Pod 启动
Write-Host "等待 Pod 启动..." -ForegroundColor Yellow
kubectl wait --for=condition=ready pod -l app=paper-editor-api -n $Namespace --timeout=300s
kubectl wait --for=condition=ready pod -l app=lunwen-generate-ui -n $Namespace --timeout=300s

Write-Host "`n=== 部署完成 ===" -ForegroundColor Green
Write-Host "查看服务状态:" -ForegroundColor Cyan
kubectl get pods,svc -n $Namespace

Write-Host "`n访问地址:" -ForegroundColor Cyan
Write-Host "前端 UI: http://localhost:30000" -ForegroundColor White
Write-Host "Editor API: http://localhost:8890" -ForegroundColor White

Write-Host "`n有用的命令:" -ForegroundColor Cyan
Write-Host "查看 Pod 状态: kubectl get pods -n $Namespace" -ForegroundColor White
Write-Host "查看服务状态: kubectl get svc -n $Namespace" -ForegroundColor White
Write-Host "查看 Pod 日志: kubectl logs -f <pod-name> -n $Namespace" -ForegroundColor White
Write-Host "进入 Pod: kubectl exec -it <pod-name> -n $Namespace -- /bin/sh" -ForegroundColor White
