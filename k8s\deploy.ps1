# PowerShell 脚本 - 服务隔离架构部署（默认架构）
param(
    [switch]$Build = $false,
    [switch]$Clean = $false,
    [string]$Namespace = "paper-services"
)

$ErrorActionPreference = "Stop"

Write-Host "=== Paper Services K8s 服务隔离架构部署 ===" -ForegroundColor Green

# 检查 kubectl 是否可用
try {
    kubectl version --client | Out-Null
} catch {
    Write-Error "kubectl 未安装或不可用，请先安装 kubectl"
    exit 1
}

# 检查 Kubernetes 集群是否可用
try {
    kubectl cluster-info | Out-Null
    Write-Host "✓ Kubernetes 集群连接正常" -ForegroundColor Green
} catch {
    Write-Error "无法连接到 Kubernetes 集群，请确保 Docker Desktop 的 Kubernetes 已启用"
    exit 1
}

# 清理现有部署
if ($Clean) {
    Write-Host "清理现有部署..." -ForegroundColor Yellow
    kubectl delete namespace $Namespace --ignore-not-found=true
    Write-Host "等待命名空间删除完成..." -ForegroundColor Yellow
    Start-Sleep -Seconds 10
}

# 构建镜像
if ($Build) {
    Write-Host "构建 Docker 镜像..." -ForegroundColor Yellow
    & "$PSScriptRoot\build-images.ps1"
    if ($LASTEXITCODE -ne 0) {
        Write-Error "镜像构建失败"
        exit 1
    }
}

# 创建命名空间
Write-Host "创建命名空间..." -ForegroundColor Yellow
kubectl apply -f "$PSScriptRoot\namespace.yaml"

# 部署独立存储
Write-Host "部署服务隔离存储..." -ForegroundColor Yellow
kubectl apply -f "$PSScriptRoot\storage.yaml"

# 部署独立基础设施
Write-Host "部署服务隔离基础设施..." -ForegroundColor Yellow
kubectl apply -f "$PSScriptRoot\infrastructure.yaml"

# 等待基础设施启动
Write-Host "等待基础设施启动..." -ForegroundColor Yellow
Write-Host "  - 等待 Paper Editor 数据库..." -ForegroundColor Cyan
kubectl wait --for=condition=ready pod -l app=paper-editor-postgres -n $Namespace --timeout=300s
Write-Host "  - 等待 Paper Editor Redis..." -ForegroundColor Cyan
kubectl wait --for=condition=ready pod -l app=paper-editor-redis -n $Namespace --timeout=300s
Write-Host "  - 等待 Dify 数据库..." -ForegroundColor Cyan
kubectl wait --for=condition=ready pod -l app=dify-postgres -n $Namespace --timeout=300s
Write-Host "  - 等待 Dify Redis..." -ForegroundColor Cyan
kubectl wait --for=condition=ready pod -l app=dify-redis -n $Namespace --timeout=300s

# 部署服务配置
Write-Host "部署服务配置..." -ForegroundColor Yellow
kubectl apply -f "$PSScriptRoot\services-config.yaml"

# 部署应用服务
Write-Host "部署应用服务..." -ForegroundColor Yellow
kubectl apply -f "$PSScriptRoot\paper-editor-api-deployment.yaml"
kubectl apply -f "$PSScriptRoot\paper-node-service-deployment.yaml"
kubectl apply -f "$PSScriptRoot\paper-py-service-deployment.yaml"
kubectl apply -f "$PSScriptRoot\lunwen-generate-ui-deployment.yaml"
kubectl apply -f "$PSScriptRoot\dify-deployment.yaml"

# 部署 paper-api-service (如果存在)
if (Test-Path "$PSScriptRoot\paper-api-service-deplyment.yml") {
    Write-Host "部署 paper-api-service..." -ForegroundColor Yellow
    kubectl apply -f "$PSScriptRoot\paper-api-service-deplyment.yml"
}

# 部署 Ingress
Write-Host "部署 Ingress..." -ForegroundColor Yellow
kubectl apply -f "$PSScriptRoot\ingress.yaml"

# 等待应用服务启动
Write-Host "等待应用服务启动..." -ForegroundColor Yellow
kubectl wait --for=condition=ready pod -l app=paper-editor-api -n $Namespace --timeout=300s
kubectl wait --for=condition=ready pod -l app=lunwen-generate-ui -n $Namespace --timeout=300s
kubectl wait --for=condition=ready pod -l app=dify-api -n $Namespace --timeout=300s

Write-Host "`n=== 服务隔离架构部署完成 ===" -ForegroundColor Green
Write-Host "查看服务状态:" -ForegroundColor Cyan
kubectl get pods,svc -n $Namespace

Write-Host "`n🏗️ 服务隔离架构说明:" -ForegroundColor Cyan
Write-Host "每个服务都有独立的数据库和缓存实例：" -ForegroundColor White
Write-Host "├── Paper Editor API" -ForegroundColor White
Write-Host "│   ├── paper-editor-postgres (独立数据库)" -ForegroundColor Gray
Write-Host "│   └── paper-editor-redis (独立缓存)" -ForegroundColor Gray
Write-Host "├── Dify AI Platform" -ForegroundColor White
Write-Host "│   ├── dify-postgres (独立数据库)" -ForegroundColor Gray
Write-Host "│   └── dify-redis (独立缓存)" -ForegroundColor Gray
Write-Host "├── Paper Node Service (独立临时存储)" -ForegroundColor White
Write-Host "└── Paper Python Service (独立数据存储)" -ForegroundColor White

Write-Host "`n访问地址:" -ForegroundColor Cyan
Write-Host "论文生成 UI: http://localhost:30000" -ForegroundColor White
Write-Host "Dify AI 平台: http://localhost:30001" -ForegroundColor White

Write-Host "`n有用的命令:" -ForegroundColor Cyan
Write-Host "查看所有资源: kubectl get all -n $Namespace" -ForegroundColor White
Write-Host "查看存储: kubectl get pvc -n $Namespace" -ForegroundColor White
Write-Host "查看服务日志: kubectl logs -f deployment/<service-name> -n $Namespace" -ForegroundColor White
