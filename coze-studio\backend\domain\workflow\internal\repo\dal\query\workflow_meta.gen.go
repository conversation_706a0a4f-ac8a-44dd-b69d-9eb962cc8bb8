// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/coze-dev/coze-studio/backend/domain/workflow/internal/repo/dal/model"
)

func newWorkflowMeta(db *gorm.DB, opts ...gen.DOOption) workflowMeta {
	_workflowMeta := workflowMeta{}

	_workflowMeta.workflowMetaDo.UseDB(db, opts...)
	_workflowMeta.workflowMetaDo.UseModel(&model.WorkflowMeta{})

	tableName := _workflowMeta.workflowMetaDo.TableName()
	_workflowMeta.ALL = field.NewAsterisk(tableName)
	_workflowMeta.ID = field.NewInt64(tableName, "id")
	_workflowMeta.Name = field.NewString(tableName, "name")
	_workflowMeta.Description = field.NewString(tableName, "description")
	_workflowMeta.IconURI = field.NewString(tableName, "icon_uri")
	_workflowMeta.Status = field.NewInt32(tableName, "status")
	_workflowMeta.ContentType = field.NewInt32(tableName, "content_type")
	_workflowMeta.Mode = field.NewInt32(tableName, "mode")
	_workflowMeta.CreatedAt = field.NewInt64(tableName, "created_at")
	_workflowMeta.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_workflowMeta.DeletedAt = field.NewField(tableName, "deleted_at")
	_workflowMeta.CreatorID = field.NewInt64(tableName, "creator_id")
	_workflowMeta.Tag = field.NewInt32(tableName, "tag")
	_workflowMeta.AuthorID = field.NewInt64(tableName, "author_id")
	_workflowMeta.SpaceID = field.NewInt64(tableName, "space_id")
	_workflowMeta.UpdaterID = field.NewInt64(tableName, "updater_id")
	_workflowMeta.SourceID = field.NewInt64(tableName, "source_id")
	_workflowMeta.AppID = field.NewInt64(tableName, "app_id")
	_workflowMeta.LatestVersion = field.NewString(tableName, "latest_version")
	_workflowMeta.LatestVersionTs = field.NewInt64(tableName, "latest_version_ts")

	_workflowMeta.fillFieldMap()

	return _workflowMeta
}

// workflowMeta The workflow metadata table,used to record the basic metadata of workflow
type workflowMeta struct {
	workflowMetaDo

	ALL             field.Asterisk
	ID              field.Int64  // workflow id
	Name            field.String // workflow name
	Description     field.String // workflow description
	IconURI         field.String // icon uri
	Status          field.Int32  // 0: Not published, 1: Published
	ContentType     field.Int32  // 0 Users 1 Official
	Mode            field.Int32  // 0:workflow, 3:chat_flow
	CreatedAt       field.Int64  // create time in millisecond
	UpdatedAt       field.Int64  // update time in millisecond
	DeletedAt       field.Field  // delete time in millisecond
	CreatorID       field.Int64  // user id for creator
	Tag             field.Int32  // template tag: Tag: 1=All, 2=Hot, 3=Information, 4=Music, 5=Picture, 6=UtilityTool, 7=Life, 8=Traval, 9=Network, 10=System, 11=Movie, 12=Office, 13=Shopping, 14=Education, 15=Health, 16=Social, 17=Entertainment, 18=Finance, 100=Hidden
	AuthorID        field.Int64  // Original author user ID
	SpaceID         field.Int64  // space id
	UpdaterID       field.Int64  // User ID for updating metadata
	SourceID        field.Int64  // Workflow ID of source
	AppID           field.Int64  // app id
	LatestVersion   field.String // the version of the most recent publish
	LatestVersionTs field.Int64  // create time of latest version

	fieldMap map[string]field.Expr
}

func (w workflowMeta) Table(newTableName string) *workflowMeta {
	w.workflowMetaDo.UseTable(newTableName)
	return w.updateTableName(newTableName)
}

func (w workflowMeta) As(alias string) *workflowMeta {
	w.workflowMetaDo.DO = *(w.workflowMetaDo.As(alias).(*gen.DO))
	return w.updateTableName(alias)
}

func (w *workflowMeta) updateTableName(table string) *workflowMeta {
	w.ALL = field.NewAsterisk(table)
	w.ID = field.NewInt64(table, "id")
	w.Name = field.NewString(table, "name")
	w.Description = field.NewString(table, "description")
	w.IconURI = field.NewString(table, "icon_uri")
	w.Status = field.NewInt32(table, "status")
	w.ContentType = field.NewInt32(table, "content_type")
	w.Mode = field.NewInt32(table, "mode")
	w.CreatedAt = field.NewInt64(table, "created_at")
	w.UpdatedAt = field.NewInt64(table, "updated_at")
	w.DeletedAt = field.NewField(table, "deleted_at")
	w.CreatorID = field.NewInt64(table, "creator_id")
	w.Tag = field.NewInt32(table, "tag")
	w.AuthorID = field.NewInt64(table, "author_id")
	w.SpaceID = field.NewInt64(table, "space_id")
	w.UpdaterID = field.NewInt64(table, "updater_id")
	w.SourceID = field.NewInt64(table, "source_id")
	w.AppID = field.NewInt64(table, "app_id")
	w.LatestVersion = field.NewString(table, "latest_version")
	w.LatestVersionTs = field.NewInt64(table, "latest_version_ts")

	w.fillFieldMap()

	return w
}

func (w *workflowMeta) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := w.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (w *workflowMeta) fillFieldMap() {
	w.fieldMap = make(map[string]field.Expr, 19)
	w.fieldMap["id"] = w.ID
	w.fieldMap["name"] = w.Name
	w.fieldMap["description"] = w.Description
	w.fieldMap["icon_uri"] = w.IconURI
	w.fieldMap["status"] = w.Status
	w.fieldMap["content_type"] = w.ContentType
	w.fieldMap["mode"] = w.Mode
	w.fieldMap["created_at"] = w.CreatedAt
	w.fieldMap["updated_at"] = w.UpdatedAt
	w.fieldMap["deleted_at"] = w.DeletedAt
	w.fieldMap["creator_id"] = w.CreatorID
	w.fieldMap["tag"] = w.Tag
	w.fieldMap["author_id"] = w.AuthorID
	w.fieldMap["space_id"] = w.SpaceID
	w.fieldMap["updater_id"] = w.UpdaterID
	w.fieldMap["source_id"] = w.SourceID
	w.fieldMap["app_id"] = w.AppID
	w.fieldMap["latest_version"] = w.LatestVersion
	w.fieldMap["latest_version_ts"] = w.LatestVersionTs
}

func (w workflowMeta) clone(db *gorm.DB) workflowMeta {
	w.workflowMetaDo.ReplaceConnPool(db.Statement.ConnPool)
	return w
}

func (w workflowMeta) replaceDB(db *gorm.DB) workflowMeta {
	w.workflowMetaDo.ReplaceDB(db)
	return w
}

type workflowMetaDo struct{ gen.DO }

type IWorkflowMetaDo interface {
	gen.SubQuery
	Debug() IWorkflowMetaDo
	WithContext(ctx context.Context) IWorkflowMetaDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IWorkflowMetaDo
	WriteDB() IWorkflowMetaDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IWorkflowMetaDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IWorkflowMetaDo
	Not(conds ...gen.Condition) IWorkflowMetaDo
	Or(conds ...gen.Condition) IWorkflowMetaDo
	Select(conds ...field.Expr) IWorkflowMetaDo
	Where(conds ...gen.Condition) IWorkflowMetaDo
	Order(conds ...field.Expr) IWorkflowMetaDo
	Distinct(cols ...field.Expr) IWorkflowMetaDo
	Omit(cols ...field.Expr) IWorkflowMetaDo
	Join(table schema.Tabler, on ...field.Expr) IWorkflowMetaDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IWorkflowMetaDo
	RightJoin(table schema.Tabler, on ...field.Expr) IWorkflowMetaDo
	Group(cols ...field.Expr) IWorkflowMetaDo
	Having(conds ...gen.Condition) IWorkflowMetaDo
	Limit(limit int) IWorkflowMetaDo
	Offset(offset int) IWorkflowMetaDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IWorkflowMetaDo
	Unscoped() IWorkflowMetaDo
	Create(values ...*model.WorkflowMeta) error
	CreateInBatches(values []*model.WorkflowMeta, batchSize int) error
	Save(values ...*model.WorkflowMeta) error
	First() (*model.WorkflowMeta, error)
	Take() (*model.WorkflowMeta, error)
	Last() (*model.WorkflowMeta, error)
	Find() ([]*model.WorkflowMeta, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.WorkflowMeta, err error)
	FindInBatches(result *[]*model.WorkflowMeta, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.WorkflowMeta) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IWorkflowMetaDo
	Assign(attrs ...field.AssignExpr) IWorkflowMetaDo
	Joins(fields ...field.RelationField) IWorkflowMetaDo
	Preload(fields ...field.RelationField) IWorkflowMetaDo
	FirstOrInit() (*model.WorkflowMeta, error)
	FirstOrCreate() (*model.WorkflowMeta, error)
	FindByPage(offset int, limit int) (result []*model.WorkflowMeta, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IWorkflowMetaDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (w workflowMetaDo) Debug() IWorkflowMetaDo {
	return w.withDO(w.DO.Debug())
}

func (w workflowMetaDo) WithContext(ctx context.Context) IWorkflowMetaDo {
	return w.withDO(w.DO.WithContext(ctx))
}

func (w workflowMetaDo) ReadDB() IWorkflowMetaDo {
	return w.Clauses(dbresolver.Read)
}

func (w workflowMetaDo) WriteDB() IWorkflowMetaDo {
	return w.Clauses(dbresolver.Write)
}

func (w workflowMetaDo) Session(config *gorm.Session) IWorkflowMetaDo {
	return w.withDO(w.DO.Session(config))
}

func (w workflowMetaDo) Clauses(conds ...clause.Expression) IWorkflowMetaDo {
	return w.withDO(w.DO.Clauses(conds...))
}

func (w workflowMetaDo) Returning(value interface{}, columns ...string) IWorkflowMetaDo {
	return w.withDO(w.DO.Returning(value, columns...))
}

func (w workflowMetaDo) Not(conds ...gen.Condition) IWorkflowMetaDo {
	return w.withDO(w.DO.Not(conds...))
}

func (w workflowMetaDo) Or(conds ...gen.Condition) IWorkflowMetaDo {
	return w.withDO(w.DO.Or(conds...))
}

func (w workflowMetaDo) Select(conds ...field.Expr) IWorkflowMetaDo {
	return w.withDO(w.DO.Select(conds...))
}

func (w workflowMetaDo) Where(conds ...gen.Condition) IWorkflowMetaDo {
	return w.withDO(w.DO.Where(conds...))
}

func (w workflowMetaDo) Order(conds ...field.Expr) IWorkflowMetaDo {
	return w.withDO(w.DO.Order(conds...))
}

func (w workflowMetaDo) Distinct(cols ...field.Expr) IWorkflowMetaDo {
	return w.withDO(w.DO.Distinct(cols...))
}

func (w workflowMetaDo) Omit(cols ...field.Expr) IWorkflowMetaDo {
	return w.withDO(w.DO.Omit(cols...))
}

func (w workflowMetaDo) Join(table schema.Tabler, on ...field.Expr) IWorkflowMetaDo {
	return w.withDO(w.DO.Join(table, on...))
}

func (w workflowMetaDo) LeftJoin(table schema.Tabler, on ...field.Expr) IWorkflowMetaDo {
	return w.withDO(w.DO.LeftJoin(table, on...))
}

func (w workflowMetaDo) RightJoin(table schema.Tabler, on ...field.Expr) IWorkflowMetaDo {
	return w.withDO(w.DO.RightJoin(table, on...))
}

func (w workflowMetaDo) Group(cols ...field.Expr) IWorkflowMetaDo {
	return w.withDO(w.DO.Group(cols...))
}

func (w workflowMetaDo) Having(conds ...gen.Condition) IWorkflowMetaDo {
	return w.withDO(w.DO.Having(conds...))
}

func (w workflowMetaDo) Limit(limit int) IWorkflowMetaDo {
	return w.withDO(w.DO.Limit(limit))
}

func (w workflowMetaDo) Offset(offset int) IWorkflowMetaDo {
	return w.withDO(w.DO.Offset(offset))
}

func (w workflowMetaDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IWorkflowMetaDo {
	return w.withDO(w.DO.Scopes(funcs...))
}

func (w workflowMetaDo) Unscoped() IWorkflowMetaDo {
	return w.withDO(w.DO.Unscoped())
}

func (w workflowMetaDo) Create(values ...*model.WorkflowMeta) error {
	if len(values) == 0 {
		return nil
	}
	return w.DO.Create(values)
}

func (w workflowMetaDo) CreateInBatches(values []*model.WorkflowMeta, batchSize int) error {
	return w.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (w workflowMetaDo) Save(values ...*model.WorkflowMeta) error {
	if len(values) == 0 {
		return nil
	}
	return w.DO.Save(values)
}

func (w workflowMetaDo) First() (*model.WorkflowMeta, error) {
	if result, err := w.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.WorkflowMeta), nil
	}
}

func (w workflowMetaDo) Take() (*model.WorkflowMeta, error) {
	if result, err := w.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.WorkflowMeta), nil
	}
}

func (w workflowMetaDo) Last() (*model.WorkflowMeta, error) {
	if result, err := w.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.WorkflowMeta), nil
	}
}

func (w workflowMetaDo) Find() ([]*model.WorkflowMeta, error) {
	result, err := w.DO.Find()
	return result.([]*model.WorkflowMeta), err
}

func (w workflowMetaDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.WorkflowMeta, err error) {
	buf := make([]*model.WorkflowMeta, 0, batchSize)
	err = w.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (w workflowMetaDo) FindInBatches(result *[]*model.WorkflowMeta, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return w.DO.FindInBatches(result, batchSize, fc)
}

func (w workflowMetaDo) Attrs(attrs ...field.AssignExpr) IWorkflowMetaDo {
	return w.withDO(w.DO.Attrs(attrs...))
}

func (w workflowMetaDo) Assign(attrs ...field.AssignExpr) IWorkflowMetaDo {
	return w.withDO(w.DO.Assign(attrs...))
}

func (w workflowMetaDo) Joins(fields ...field.RelationField) IWorkflowMetaDo {
	for _, _f := range fields {
		w = *w.withDO(w.DO.Joins(_f))
	}
	return &w
}

func (w workflowMetaDo) Preload(fields ...field.RelationField) IWorkflowMetaDo {
	for _, _f := range fields {
		w = *w.withDO(w.DO.Preload(_f))
	}
	return &w
}

func (w workflowMetaDo) FirstOrInit() (*model.WorkflowMeta, error) {
	if result, err := w.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.WorkflowMeta), nil
	}
}

func (w workflowMetaDo) FirstOrCreate() (*model.WorkflowMeta, error) {
	if result, err := w.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.WorkflowMeta), nil
	}
}

func (w workflowMetaDo) FindByPage(offset int, limit int) (result []*model.WorkflowMeta, count int64, err error) {
	result, err = w.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = w.Offset(-1).Limit(-1).Count()
	return
}

func (w workflowMetaDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = w.Count()
	if err != nil {
		return
	}

	err = w.Offset(offset).Limit(limit).Scan(result)
	return
}

func (w workflowMetaDo) Scan(result interface{}) (err error) {
	return w.DO.Scan(result)
}

func (w workflowMetaDo) Delete(models ...*model.WorkflowMeta) (result gen.ResultInfo, err error) {
	return w.DO.Delete(models)
}

func (w *workflowMetaDo) withDO(do gen.Dao) *workflowMetaDo {
	w.DO = *do.(*gen.DO)
	return w
}
