// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"gorm.io/gorm"
)

const TableNameKnowledgeDocumentReview = "knowledge_document_review"

// KnowledgeDocumentReview Document slice preview info
type KnowledgeDocumentReview struct {
	ID           int64          `gorm:"column:id;primaryKey;comment:id" json:"id"`                                                // id
	KnowledgeID  int64          `gorm:"column:knowledge_id;not null;comment:knowledge id" json:"knowledge_id"`                    // knowledge id
	SpaceID      int64          `gorm:"column:space_id;not null;comment:space id" json:"space_id"`                                // space id
	Name         string         `gorm:"column:name;not null;comment:name" json:"name"`                                            // name
	Type         string         `gorm:"column:type;not null;default:0;comment:document type" json:"type"`                         // document type
	URI          string         `gorm:"column:uri;comment:uri" json:"uri"`                                                        // uri
	FormatType   int32          `gorm:"column:format_type;not null;comment:0 text, 1 table, 2 images" json:"format_type"`         // 0 text, 1 table, 2 images
	Status       int32          `gorm:"column:status;not null;comment:0 Processing 1 Completed 2 Failed 3 Expired" json:"status"` // 0 Processing 1 Completed 2 Failed 3 Expired
	ChunkRespURI string         `gorm:"column:chunk_resp_uri;comment:pre-sliced uri" json:"chunk_resp_uri"`                       // pre-sliced uri
	DeletedAt    gorm.DeletedAt `gorm:"column:deleted_at;comment:Delete Time" json:"deleted_at"`                                  // Delete Time
	CreatedAt    int64          `gorm:"column:created_at;not null;comment:Create Time in Milliseconds" json:"created_at"`         // Create Time in Milliseconds
	UpdatedAt    int64          `gorm:"column:updated_at;not null;comment:Update Time in Milliseconds" json:"updated_at"`         // Update Time in Milliseconds
	CreatorID    int64          `gorm:"column:creator_id;not null;comment:creator id" json:"creator_id"`                          // creator id
}

// TableName KnowledgeDocumentReview's table name
func (*KnowledgeDocumentReview) TableName() string {
	return TableNameKnowledgeDocumentReview
}
