// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameSingleAgentPublish = "single_agent_publish"

// SingleAgentPublish Bot connector and release version info
type SingleAgentPublish struct {
	ID           int64   `gorm:"column:id;primaryKey;autoIncrement:true;comment:id" json:"id"`                                          // id
	AgentID      int64   `gorm:"column:agent_id;not null;comment:agent_id" json:"agent_id"`                                             // agent_id
	PublishID    string  `gorm:"column:publish_id;not null;comment:publish id" json:"publish_id"`                                       // publish id
	ConnectorIds []int64 `gorm:"column:connector_ids;comment:connector_ids;serializer:json" json:"connector_ids"`                       // connector_ids
	Version      string  `gorm:"column:version;not null;comment:Agent Version" json:"version"`                                          // Agent Version
	PublishInfo  *string `gorm:"column:publish_info;comment:publish info" json:"publish_info"`                                          // publish info
	PublishTime  int64   `gorm:"column:publish_time;not null;comment:publish time" json:"publish_time"`                                 // publish time
	CreatedAt    int64   `gorm:"column:created_at;not null;autoCreateTime:milli;comment:Create Time in Milliseconds" json:"created_at"` // Create Time in Milliseconds
	UpdatedAt    int64   `gorm:"column:updated_at;not null;autoUpdateTime:milli;comment:Update Time in Milliseconds" json:"updated_at"` // Update Time in Milliseconds
	CreatorID    int64   `gorm:"column:creator_id;not null;comment:creator id" json:"creator_id"`                                       // creator id
	Status       int32   `gorm:"column:status;not null;comment:Status 0: In use 1: Delete 3: Disabled" json:"status"`                   // Status 0: In use 1: Delete 3: Disabled
	Extra        *string `gorm:"column:extra;comment:extra" json:"extra"`                                                               // extra
}

// TableName SingleAgentPublish's table name
func (*SingleAgentPublish) TableName() string {
	return TableNameSingleAgentPublish
}
