// CSS Variables for Light Mode
const lightModeVariables = {
  background: '255, 255, 255',
  foreground: '28, 28, 35',
  icon: '221, 0%, 10%',
  'icon-dark': '221, 0%, 10%',
  'icon-gray': '221, 0%, 90%',
  'black-7': '0, 0, 0',
  'black-6': '16, 16, 16',
  'black-5': '54, 61, 77',
  'black-4': '102, 102, 104',
  'black-3': '149, 149, 151',
  'black-2': '173, 173, 175',
  'black-1': '220, 220, 222',
  'white-6': '209, 209, 211',
  'white-5': '219, 219, 220',
  'white-4': '228, 228, 230',
  'white-3': '238, 238, 239',
  'white-2': '248, 248, 248',
  'white-1': '255, 255, 255',
  'coze-fg-revert': '255, 255, 255',
  'coze-fg-white': '255, 255, 255',
  'coze-fg-7': '255, 255, 255',
  'coze-fg-6': '255, 255, 255',
  'coze-fg-5': '255, 255, 255',
  'coze-fg-4': '8, 13, 30',
  'coze-fg-3': '15, 21, 40',
  'coze-fg-2': '32, 41, 69',
  'coze-fg-1': '55, 67, 106',
  // TODO: need to remove bg9
  'coze-bg-9': '6, 7, 9',
  'coze-bg-8': '68, 83, 130',
  'coze-bg-7': '75, 90, 140',
  'coze-bg-6': '82, 100, 154',
  'coze-bg-5': '87, 104, 161',
  'coze-bg-4': '90, 108, 167',
  'coze-bg-3': '255, 255, 255',
  'coze-bg-2': '252, 252, 255',
  'coze-bg-1': '247, 247, 252',
  'coze-bg-0': '240, 240, 247',
  'coze-stroke-5': '82, 100, 154',
  'coze-stroke-6': '68, 83, 130',
  'coze-stroke-7': '55, 67, 106',
  'coze-stroke-opaque': '226, 228, 239',
  'coze-mask-5': '0, 0, 0',
  'coze-shadow-0': '0, 0, 0',
  'coze-brand-50': '102, 108, 255',
  'coze-brand-30': '163, 166, 255',
  'coze-brand-7': '65, 43, 255',
  'coze-brand-6': '69, 56, 255',
  'coze-brand-5': '81, 71, 255',
  'coze-brand-3': '150, 159, 255',
  'coze-brand-2': '161, 170, 255',
  'coze-brand-1': '171, 181, 255',
  'coze-brand-0': '181, 191, 255',
  'coze-red-7': '222, 22, 39',
  'coze-red-6': '227, 36, 52',
  'coze-red-5': '229, 50, 65',
  'coze-red-3': '250, 138, 148',
  'coze-red-2': '252, 149, 157',
  'coze-red-1': '255, 163, 171',
  'coze-red-0': '255, 173, 180',
  'coze-yellow-50': '242, 182, 0',
  'coze-yellow-30': '245, 184, 0',
  'coze-yellow-7': '229, 104, 0',
  'coze-yellow-6': '242, 109, 0',
  'coze-yellow-5': '255, 115, 0',
  'coze-yellow-3': '242, 161, 94',
  'coze-yellow-2': '242, 169, 109',
  'coze-yellow-1': '240, 174, 120',
  'coze-yellow-0': '239, 179, 130',
  'coze-green-7': '0, 161, 54',
  'coze-green-6': '0, 168, 56',
  'coze-green-5': '0, 178, 60',
  'coze-green-3': '79, 201, 120',
  'coze-green-2': '93, 207, 131',
  'coze-green-1': '105, 209, 140',
  'coze-green-0': '116, 212, 149',
  'coze-orange-5': '199, 66, 0',
  'coze-orange-3': '242, 148, 65',
  'coze-emerald-5': '0, 128, 74',
  'coze-emerald-3': '41, 204, 114',
  'coze-emerald-50': '0, 184, 122',
  'coze-emerald-30': '54, 209, 158',
  'coze-emerald-20': '66, 214, 165',
  'coze-emerald-10': '103, 229, 188',
  'coze-cyan-5': '0, 124, 135',
  'coze-cyan-3': '47, 196, 189',
  'coze-cyan-50': '0, 163, 163',
  'coze-cyan-30': '62, 199, 199',
  'coze-cyan-20': '73, 204, 204',
  'coze-cyan-10': '84, 209, 209',
  'coze-blue-5': '43, 87, 217',
  'coze-blue-3': '120, 170, 250',
  'coze-blue-50': '61, 121, 242',
  'coze-blue-30': '131, 172, 252',
  'coze-blue-20': '141, 178, 252',
  'coze-blue-10': '153, 187, 255',
  'coze-purple-7': '148, 0, 222',
  'coze-purple-6': '157, 0, 235',
  'coze-purple-5': '167, 0, 250',
  'coze-purple-3': '213, 128, 255',
  'coze-purple-2': '218, 145, 255',
  'coze-purple-1': '224, 163, 255',
  'coze-purple-50': '192, 66, 255',
  'coze-purple-30': '217, 143, 255',
  'coze-purple-20': '221, 153, 255',
  'coze-purple-10': '224, 163, 255',
  'coze-magenta-5': '209, 0, 157',
  'coze-magenta-3': '245, 120, 197',
  'coze-magenta-50': '242, 48, 177',
  'coze-magenta-30': '252, 134, 213',
  'coze-magenta-20': '252, 144, 216',
  'coze-magenta-10': '255, 158, 222',
  'coze-alternative-50': '191, 229, 0',
  'coze-alternative-30': '175, 209, 0',
  'coze-1080': '1080px',
  'coze-800': '800px',
  'coze-640': '640px',
  'coze-480': '480px',
  'coze-320': '320px',
  'coze-240': '240px',
  'coze-200': '200px',
  'coze-160': '160px',
  'coze-120': '120px',
  'coze-96': '96px',
  'coze-80': '80px',
  'coze-64': '64px',
  'coze-48': '48px',
  'coze-40': '40px',
  'coze-38': '38px',
  'coze-36': '36px',
  'coze-32': '32px',
  'coze-30': '30px',
  'coze-28': '28px',
  'coze-26': '26px',
  'coze-24': '24px',
  'coze-22': '22px',
  'coze-20': '20px',
  'coze-18': '18px',
  'coze-16': '16px',
  'coze-15': '15px',
  'coze-14': '14px',
  'coze-12': '12px',
  'coze-10': '10px',
  'coze-9': '9px',
  'coze-8': '8px',
  'coze-6': '6px',
  'coze-5': '5px',
  'coze-4': '4px',
  'coze-3': '3px',
  'coze-2': '2px',
  'coze-1': '1px',
  // TODO: rspress cannot be compiled, and it needs to be processed by some tools. It is not used at present. Temporary comment processing
  // 'coze-0.5': '0.5px',
  'coze-0-5': '0.5px',

  /* alpha */
  'coze-fg-1-alpha': '0.38',
  'coze-fg-2-alpha': '0.62',
  'coze-fg-3-alpha': '0.82',
  'coze-fg-4-alpha': '0.9',
  'coze-fg-5-alpha': '1',
  'coze-fg-6-alpha': '1',
  'coze-fg-7-alpha': '1',
  'coze-fg-revert-alpha': '1',
  'coze-fg-white-alpha': '1',
  'coze-bg-0-alpha': '1',
  'coze-bg-1-alpha': '1',
  'coze-bg-2-alpha': '1',
  'coze-bg-3-alpha': '1',
  'coze-bg-4-alpha': '0.04',
  'coze-bg-5-alpha': '0.08',
  'coze-bg-6-alpha': '0.13',
  'coze-bg-7-alpha': '0.19',
  'coze-bg-8-alpha': '0.25',
  // TODO: need to remove bg9
  'coze-bg-9-alpha': '0.16',
  'coze-stroke-5-alpha': '0.13',
  'coze-stroke-6-alpha': '0.25',
  'coze-stroke-7-alpha': '0.38',
  'coze-brand-0-alpha': '0.23',
  'coze-brand-1-alpha': '0.3',
  'coze-brand-2-alpha': '0.38',
  'coze-brand-3-alpha': '0.45',
  'coze-brand-5-alpha': '1',
  'coze-brand-6-alpha': '1',
  'coze-brand-7-alpha': '1',
  'coze-brand-30-alpha': '0.45',
  'coze-brand-50-alpha': '1',
  'coze-red-0-alpha': '0.23',
  'coze-red-1-alpha': '0.3',
  'coze-red-2-alpha': '0.38',
  'coze-red-3-alpha': '0.45',
  'coze-yellow-50-alpha': '1',
  'coze-yellow-30-alpha': '0.45',
  'coze-yellow-0-alpha': '0.23',
  'coze-yellow-1-alpha': '0.3',
  'coze-yellow-2-alpha': '0.38',
  'coze-yellow-3-alpha': '0.45',
  'coze-yellow-5-alpha': '1',
  'coze-yellow-6-alpha': '1',
  'coze-yellow-7-alpha': '1',
  'coze-green-0-alpha': '0.23',
  'coze-green-1-alpha': '0.3',
  'coze-green-2-alpha': '0.38',
  'coze-green-3-alpha': '0.45',
  'coze-green-5-alpha': '1',
  'coze-green-6-alpha': '1',
  'coze-green-7-alpha': '1',
  'coze-orange-5-alpha': '1',
  'coze-orange-3-alpha': '0.45',
  'coze-emerald-5-alpha': '1',
  'coze-emerald-3-alpha': '0.45',
  'coze-emerald-50-alpha': '1',
  'coze-emerald-30-alpha': '0.55',
  'coze-emerald-20-alpha': '0.45',
  'coze-emerald-10-alpha': '0.2',
  'coze-cyan-5-alpha': '1',
  'coze-cyan-3-alpha': '0.45',
  'coze-cyan-50-alpha': '1',
  'coze-cyan-30-alpha': '0.45',
  'coze-cyan-20-alpha': '0.38',
  'coze-cyan-10-alpha': '0.3',
  'coze-blue-5-alpha': '1',
  'coze-blue-3-alpha': '0.45',
  'coze-blue-50-alpha': '1',
  'coze-blue-30-alpha': '0.45',
  'coze-blue-20-alpha': '0.38',
  'coze-blue-10-alpha': '0.3',
  'coze-purple-7-alpha': '1',
  'coze-purple-6-alpha': '1',
  'coze-purple-5-alpha': '1',
  'coze-purple-3-alpha': '0.45',
  'coze-purple-2-alpha': '0.38',
  'coze-purple-1-alpha': '0.3',
  'coze-purple-50-alpha': '1',
  'coze-purple-30-alpha': '0.45',
  'coze-purple-20-alpha': '0.38',
  'coze-purple-10-alpha': '0.3',
  'coze-magenta-5-alpha': '1',
  'coze-magenta-3-alpha': '0.45',
  'coze-magenta-50-alpha': '1',
  'coze-magenta-30-alpha': '0.45',
  'coze-magenta-20-alpha': '0.38',
  'coze-magenta-10-alpha': '0.3',
  'coze-alternative-30-alpha': '0.45',
  'coze-alternative-50-alpha': '1',
};

module.exports = lightModeVariables;
