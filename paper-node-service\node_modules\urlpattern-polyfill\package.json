{"name": "urlpattern-polyfill", "version": "10.0.0", "description": "Polyfill for the URLPattern API", "repository": {"type": "git", "url": "https://github.com/kenchris/urlpattern-polyfill"}, "type": "module", "main": "./index.cjs", "module": "./index.js", "types": "./dist/index.d.ts", "exports": {"./urlpattern": {"types": "./dist/index.d.ts", "import": "./dist/urlpattern.js", "require": "./dist/urlpattern.cjs"}, ".": {"types": "./dist/index.d.ts", "import": "./index.js", "require": "./index.cjs"}}, "tags": ["url", "url<PERSON><PERSON>n", "url-pattern", "browser", "node", "polyfill", "w3c", "wicg"], "files": ["dist", "index.js", "index.cjs"], "devDependencies": {"@ava/typescript": "^4.0.0", "ava": "^5.3.0", "esbuild": "^0.17.19", "rimraf": "^5.0.1", "typescript": "^5.1.3", "wireit": "^0.9.5"}, "ava": {"files": ["test/**/*", "!test/wpt"], "typescript": {"rewritePaths": {"src/": "dist/"}, "compile": false}}, "scripts": {"build": "wireit", "sync-wpt": "wireit", "test": "wireit", "manual-test": "wireit", "publish-dev": "wireit", "publish-patch": "wireit", "publish-major": "wireit"}, "wireit": {"build:esm": {"command": "esbuild --bundle --format=esm src/url-pattern.ts --outfile=dist/urlpattern.js --minify --target=es2022", "output": ["dist/urlpattern.js"], "files": ["src/**/*"]}, "build:cjs": {"command": "esbuild --bundle --format=cjs src/url-pattern.ts --outfile=dist/urlpattern.cjs --minify --target=es2022", "output": ["dist/urlpattern.cjs"], "files": ["src/**/*"]}, "copyTypeFiles": {"command": "cp ./src/index.d.ts ./src/types.d.ts ./dist", "output": ["dist/index.d.ts", "dist/types.d.ts"], "dependencies": ["build:esm", "build:cjs"]}, "build": {"dependencies": ["copyTypeFiles"]}, "prepFakeNodeModules": {"command": "rm -rf node_modules/urlpattern-polyfill; ln -s $(pwd) node_modules/urlpattern-polyfill"}, "sync-wpt": {"command": "cd test && wget http://wpt.live/urlpattern/resources/urlpatterntestdata.json && wget http://wpt.live/urlpattern/resources/urlpattern-compare-test-data.json"}, "test": {"command": "ava --timeout=60s", "watch": "test/**/*", "files": ["test/**/*"], "dependencies": ["prepFakeNodeModules", "build"]}, "manual-test": {"command": "npx http-server -o /index.html -p 4203", "dependencies": ["test"]}, "publish-dev": {"command": "VERSION=${npm_package_version%-*}-dev.`git rev-parse --short HEAD` && npm version --no-git-tag-version $VERSION && npm publish --tag dev", "dependencies": ["test"]}, "publish-patch": {"command": "npm version patch && npm publish", "dependencies": ["test"]}, "publish-major": {"command": "npm version major && npm publish", "dependencies": ["test"]}}, "author": "", "license": "MIT", "dependencies": {}}