/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { RouterProvider, createBrowserRouter } from 'react-router-dom';
import { useEffect } from 'react';

import slardar from '@slardar/web';
import { reporter } from '@coze-arch/logger';

import { Page1 } from './pages/page1';
import { MainPage } from './pages/main';

const router = createBrowserRouter([
  {
    path: '/',
    children: [
      { path: 'page1', element: <Page1 /> },
      { path: '', element: <MainPage /> },
      { path: '*', element: <div>404</div> },
    ],
  },
]);

export function App() {
  useEffect(() => {
    reporter.info({ message: 'Ok fine' });
    reporter.init(slardar);
  }, []);

  return <RouterProvider router={router} />;
}
