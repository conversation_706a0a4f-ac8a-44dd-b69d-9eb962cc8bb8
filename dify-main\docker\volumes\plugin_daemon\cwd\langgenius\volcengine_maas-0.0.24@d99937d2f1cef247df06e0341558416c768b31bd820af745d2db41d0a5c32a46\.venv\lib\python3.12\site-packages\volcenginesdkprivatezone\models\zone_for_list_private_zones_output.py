# coding: utf-8

"""
    private_zone

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ZoneForListPrivateZonesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'created_at': 'str',
        'last_operator': 'str',
        'line_mode': 'int',
        'project_name': 'str',
        'record_count': 'int',
        'recursion_mode': 'bool',
        'region': 'list[str]',
        'remark': 'str',
        'tags': 'list[TagForListPrivateZonesOutput]',
        'updated_at': 'str',
        'zid': 'int',
        'zone_name': 'str'
    }

    attribute_map = {
        'created_at': 'CreatedAt',
        'last_operator': 'LastOperator',
        'line_mode': 'LineMode',
        'project_name': 'ProjectName',
        'record_count': 'RecordCount',
        'recursion_mode': 'RecursionMode',
        'region': 'Region',
        'remark': 'Remark',
        'tags': 'Tags',
        'updated_at': 'UpdatedAt',
        'zid': 'ZID',
        'zone_name': 'ZoneName'
    }

    def __init__(self, created_at=None, last_operator=None, line_mode=None, project_name=None, record_count=None, recursion_mode=None, region=None, remark=None, tags=None, updated_at=None, zid=None, zone_name=None, _configuration=None):  # noqa: E501
        """ZoneForListPrivateZonesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._created_at = None
        self._last_operator = None
        self._line_mode = None
        self._project_name = None
        self._record_count = None
        self._recursion_mode = None
        self._region = None
        self._remark = None
        self._tags = None
        self._updated_at = None
        self._zid = None
        self._zone_name = None
        self.discriminator = None

        if created_at is not None:
            self.created_at = created_at
        if last_operator is not None:
            self.last_operator = last_operator
        if line_mode is not None:
            self.line_mode = line_mode
        if project_name is not None:
            self.project_name = project_name
        if record_count is not None:
            self.record_count = record_count
        if recursion_mode is not None:
            self.recursion_mode = recursion_mode
        if region is not None:
            self.region = region
        if remark is not None:
            self.remark = remark
        if tags is not None:
            self.tags = tags
        if updated_at is not None:
            self.updated_at = updated_at
        if zid is not None:
            self.zid = zid
        if zone_name is not None:
            self.zone_name = zone_name

    @property
    def created_at(self):
        """Gets the created_at of this ZoneForListPrivateZonesOutput.  # noqa: E501


        :return: The created_at of this ZoneForListPrivateZonesOutput.  # noqa: E501
        :rtype: str
        """
        return self._created_at

    @created_at.setter
    def created_at(self, created_at):
        """Sets the created_at of this ZoneForListPrivateZonesOutput.


        :param created_at: The created_at of this ZoneForListPrivateZonesOutput.  # noqa: E501
        :type: str
        """

        self._created_at = created_at

    @property
    def last_operator(self):
        """Gets the last_operator of this ZoneForListPrivateZonesOutput.  # noqa: E501


        :return: The last_operator of this ZoneForListPrivateZonesOutput.  # noqa: E501
        :rtype: str
        """
        return self._last_operator

    @last_operator.setter
    def last_operator(self, last_operator):
        """Sets the last_operator of this ZoneForListPrivateZonesOutput.


        :param last_operator: The last_operator of this ZoneForListPrivateZonesOutput.  # noqa: E501
        :type: str
        """

        self._last_operator = last_operator

    @property
    def line_mode(self):
        """Gets the line_mode of this ZoneForListPrivateZonesOutput.  # noqa: E501


        :return: The line_mode of this ZoneForListPrivateZonesOutput.  # noqa: E501
        :rtype: int
        """
        return self._line_mode

    @line_mode.setter
    def line_mode(self, line_mode):
        """Sets the line_mode of this ZoneForListPrivateZonesOutput.


        :param line_mode: The line_mode of this ZoneForListPrivateZonesOutput.  # noqa: E501
        :type: int
        """

        self._line_mode = line_mode

    @property
    def project_name(self):
        """Gets the project_name of this ZoneForListPrivateZonesOutput.  # noqa: E501


        :return: The project_name of this ZoneForListPrivateZonesOutput.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this ZoneForListPrivateZonesOutput.


        :param project_name: The project_name of this ZoneForListPrivateZonesOutput.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def record_count(self):
        """Gets the record_count of this ZoneForListPrivateZonesOutput.  # noqa: E501


        :return: The record_count of this ZoneForListPrivateZonesOutput.  # noqa: E501
        :rtype: int
        """
        return self._record_count

    @record_count.setter
    def record_count(self, record_count):
        """Sets the record_count of this ZoneForListPrivateZonesOutput.


        :param record_count: The record_count of this ZoneForListPrivateZonesOutput.  # noqa: E501
        :type: int
        """

        self._record_count = record_count

    @property
    def recursion_mode(self):
        """Gets the recursion_mode of this ZoneForListPrivateZonesOutput.  # noqa: E501


        :return: The recursion_mode of this ZoneForListPrivateZonesOutput.  # noqa: E501
        :rtype: bool
        """
        return self._recursion_mode

    @recursion_mode.setter
    def recursion_mode(self, recursion_mode):
        """Sets the recursion_mode of this ZoneForListPrivateZonesOutput.


        :param recursion_mode: The recursion_mode of this ZoneForListPrivateZonesOutput.  # noqa: E501
        :type: bool
        """

        self._recursion_mode = recursion_mode

    @property
    def region(self):
        """Gets the region of this ZoneForListPrivateZonesOutput.  # noqa: E501


        :return: The region of this ZoneForListPrivateZonesOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._region

    @region.setter
    def region(self, region):
        """Sets the region of this ZoneForListPrivateZonesOutput.


        :param region: The region of this ZoneForListPrivateZonesOutput.  # noqa: E501
        :type: list[str]
        """

        self._region = region

    @property
    def remark(self):
        """Gets the remark of this ZoneForListPrivateZonesOutput.  # noqa: E501


        :return: The remark of this ZoneForListPrivateZonesOutput.  # noqa: E501
        :rtype: str
        """
        return self._remark

    @remark.setter
    def remark(self, remark):
        """Sets the remark of this ZoneForListPrivateZonesOutput.


        :param remark: The remark of this ZoneForListPrivateZonesOutput.  # noqa: E501
        :type: str
        """

        self._remark = remark

    @property
    def tags(self):
        """Gets the tags of this ZoneForListPrivateZonesOutput.  # noqa: E501


        :return: The tags of this ZoneForListPrivateZonesOutput.  # noqa: E501
        :rtype: list[TagForListPrivateZonesOutput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this ZoneForListPrivateZonesOutput.


        :param tags: The tags of this ZoneForListPrivateZonesOutput.  # noqa: E501
        :type: list[TagForListPrivateZonesOutput]
        """

        self._tags = tags

    @property
    def updated_at(self):
        """Gets the updated_at of this ZoneForListPrivateZonesOutput.  # noqa: E501


        :return: The updated_at of this ZoneForListPrivateZonesOutput.  # noqa: E501
        :rtype: str
        """
        return self._updated_at

    @updated_at.setter
    def updated_at(self, updated_at):
        """Sets the updated_at of this ZoneForListPrivateZonesOutput.


        :param updated_at: The updated_at of this ZoneForListPrivateZonesOutput.  # noqa: E501
        :type: str
        """

        self._updated_at = updated_at

    @property
    def zid(self):
        """Gets the zid of this ZoneForListPrivateZonesOutput.  # noqa: E501


        :return: The zid of this ZoneForListPrivateZonesOutput.  # noqa: E501
        :rtype: int
        """
        return self._zid

    @zid.setter
    def zid(self, zid):
        """Sets the zid of this ZoneForListPrivateZonesOutput.


        :param zid: The zid of this ZoneForListPrivateZonesOutput.  # noqa: E501
        :type: int
        """

        self._zid = zid

    @property
    def zone_name(self):
        """Gets the zone_name of this ZoneForListPrivateZonesOutput.  # noqa: E501


        :return: The zone_name of this ZoneForListPrivateZonesOutput.  # noqa: E501
        :rtype: str
        """
        return self._zone_name

    @zone_name.setter
    def zone_name(self, zone_name):
        """Sets the zone_name of this ZoneForListPrivateZonesOutput.


        :param zone_name: The zone_name of this ZoneForListPrivateZonesOutput.  # noqa: E501
        :type: str
        """

        self._zone_name = zone_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ZoneForListPrivateZonesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ZoneForListPrivateZonesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ZoneForListPrivateZonesOutput):
            return True

        return self.to_dict() != other.to_dict()
