{"name": "ts-dedent", "version": "2.2.0", "description": "TypeScript package which smartly trims and strips indentation from multi-line strings", "author": "<PERSON><PERSON> <<EMAIL>>", "main": "./dist/index.js", "module": "./esm/index.js", "jsnext:main": "./dist/index.js", "typings": "./dist/index.d.ts", "license": "MIT", "files": ["dist", "esm", "src"], "repository": {"type": "git", "url": "https://github.com/tamino-martinius/node-ts-dedent.git"}, "keywords": ["dedent", "deindent", "indentation", "multi-line string", "multiline strings", "template literals", "template strings", "ts", "typescript", "es6", "harmony"], "engines": {"node": ">=6.10"}, "scripts": {"coverage": "rm -rf coverage* && jest --coverage", "pretest": "npm run lint", "test": "jest", "lint": "eslint .", "watch": "tsc -w", "ci": "npm run coverage && codecov", "compile": "rm -rf dist/* && rm -rf esm/* && tsc --module commonjs --outdir dist && tsc --module es6 --outdir esm", "preversion": "npm run compile && git add ."}, "devDependencies": {"@types/jest": "^26.0.24", "@typescript-eslint/eslint-plugin": "^4.28.5", "@typescript-eslint/parser": "^4.28.5", "codecov": "^3.8.3", "eslint": "^7.32.0", "jest": "^27.0.6", "ts-jest": "^27.0.4", "typescript": "~4.3.5"}, "jest": {"transform": {".ts": "ts-jest"}, "testRegex": "\\.(test|spec)\\.ts$", "moduleFileExtensions": ["ts", "tsx", "js", "json"]}}