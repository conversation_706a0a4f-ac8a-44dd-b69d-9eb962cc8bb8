// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNamePromptResource = "prompt_resource"

// PromptResource prompt_resource
type PromptResource struct {
	ID          int64  `gorm:"column:id;primaryKey;autoIncrement:true;comment:id" json:"id"`                                          // id
	SpaceID     int64  `gorm:"column:space_id;not null;comment:space id" json:"space_id"`                                             // space id
	Name        string `gorm:"column:name;not null;comment:name" json:"name"`                                                         // name
	Description string `gorm:"column:description;not null;comment:description" json:"description"`                                    // description
	PromptText  string `gorm:"column:prompt_text;comment:prompt text" json:"prompt_text"`                                             // prompt text
	Status      int32  `gorm:"column:status;not null;comment:status, 0 is invalid, 1 is valid" json:"status"`                         // status, 0 is invalid, 1 is valid
	CreatorID   int64  `gorm:"column:creator_id;not null;comment:creator id" json:"creator_id"`                                       // creator id
	CreatedAt   int64  `gorm:"column:created_at;not null;autoCreateTime:milli;comment:Create Time in Milliseconds" json:"created_at"` // Create Time in Milliseconds
	UpdatedAt   int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli;comment:Update Time in Milliseconds" json:"updated_at"` // Update Time in Milliseconds
}

// TableName PromptResource's table name
func (*PromptResource) TableName() string {
	return TableNamePromptResource
}
