// Code generated by thriftgo (0.4.1). DO NOT EDIT.

package project_memory

import (
	"database/sql"
	"database/sql/driver"
	"fmt"
	"github.com/apache/thrift/lib/go/thrift"
	"github.com/coze-dev/coze-studio/backend/api/model/base"
)

type VariableType int64

const (
	VariableType_KVVariable   VariableType = 1
	VariableType_ListVariable VariableType = 2
)

func (p VariableType) String() string {
	switch p {
	case VariableType_KVVariable:
		return "KVVariable"
	case VariableType_ListVariable:
		return "ListVariable"
	}
	return "<UNSET>"
}

func VariableTypeFromString(s string) (VariableType, error) {
	switch s {
	case "KVVariable":
		return VariableType_KVVariable, nil
	case "ListVariable":
		return VariableType_ListVariable, nil
	}
	return VariableType(0), fmt.Errorf("not a valid VariableType string")
}

func VariableTypePtr(v VariableType) *VariableType { return &v }
func (p *VariableType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = VariableType(result.Int64)
	return
}

func (p *VariableType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type VariableChannel int64

const (
	VariableChannel_Custom   VariableChannel = 1
	VariableChannel_System   VariableChannel = 2
	VariableChannel_Location VariableChannel = 3
	VariableChannel_Feishu   VariableChannel = 4
	// project variable
	VariableChannel_APP VariableChannel = 5
)

func (p VariableChannel) String() string {
	switch p {
	case VariableChannel_Custom:
		return "Custom"
	case VariableChannel_System:
		return "System"
	case VariableChannel_Location:
		return "Location"
	case VariableChannel_Feishu:
		return "Feishu"
	case VariableChannel_APP:
		return "APP"
	}
	return "<UNSET>"
}

func VariableChannelFromString(s string) (VariableChannel, error) {
	switch s {
	case "Custom":
		return VariableChannel_Custom, nil
	case "System":
		return VariableChannel_System, nil
	case "Location":
		return VariableChannel_Location, nil
	case "Feishu":
		return VariableChannel_Feishu, nil
	case "APP":
		return VariableChannel_APP, nil
	}
	return VariableChannel(0), fmt.Errorf("not a valid VariableChannel string")
}

func VariableChannelPtr(v VariableChannel) *VariableChannel { return &v }
func (p *VariableChannel) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = VariableChannel(result.Int64)
	return
}

func (p *VariableChannel) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type VariableConnector int64

const (
	VariableConnector_Bot     VariableConnector = 1
	VariableConnector_Project VariableConnector = 2
)

func (p VariableConnector) String() string {
	switch p {
	case VariableConnector_Bot:
		return "Bot"
	case VariableConnector_Project:
		return "Project"
	}
	return "<UNSET>"
}

func VariableConnectorFromString(s string) (VariableConnector, error) {
	switch s {
	case "Bot":
		return VariableConnector_Bot, nil
	case "Project":
		return VariableConnector_Project, nil
	}
	return VariableConnector(0), fmt.Errorf("not a valid VariableConnector string")
}

func VariableConnectorPtr(v VariableConnector) *VariableConnector { return &v }
func (p *VariableConnector) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = VariableConnector(result.Int64)
	return
}

func (p *VariableConnector) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type Variable struct {
	Keyword      string          `thrift:"Keyword,1" form:"Keyword" json:"Keyword" query:"Keyword"`
	DefaultValue string          `thrift:"DefaultValue,2" form:"DefaultValue" json:"DefaultValue" query:"DefaultValue"`
	VariableType VariableType    `thrift:"VariableType,3" form:"VariableType" json:"VariableType" query:"VariableType"`
	Channel      VariableChannel `thrift:"Channel,4" form:"Channel" json:"Channel" query:"Channel"`
	Description  string          `thrift:"Description,5" form:"Description" json:"Description" query:"Description"`
	Enable       bool            `thrift:"Enable,6" form:"Enable" json:"Enable" query:"Enable"`
	//effective channel
	EffectiveChannelList []string `thrift:"EffectiveChannelList,7,optional" form:"EffectiveChannelList" json:"EffectiveChannelList,omitempty" query:"EffectiveChannelList"`
	//New and old data will have schemas, except for project variables, the default is string.
	Schema     string `thrift:"Schema,8" form:"Schema" json:"Schema" query:"Schema"`
	IsReadOnly bool   `thrift:"IsReadOnly,9" form:"IsReadOnly" json:"IsReadOnly" query:"IsReadOnly"`
}

func NewVariable() *Variable {
	return &Variable{}
}

func (p *Variable) InitDefault() {
}

func (p *Variable) GetKeyword() (v string) {
	return p.Keyword
}

func (p *Variable) GetDefaultValue() (v string) {
	return p.DefaultValue
}

func (p *Variable) GetVariableType() (v VariableType) {
	return p.VariableType
}

func (p *Variable) GetChannel() (v VariableChannel) {
	return p.Channel
}

func (p *Variable) GetDescription() (v string) {
	return p.Description
}

func (p *Variable) GetEnable() (v bool) {
	return p.Enable
}

var Variable_EffectiveChannelList_DEFAULT []string

func (p *Variable) GetEffectiveChannelList() (v []string) {
	if !p.IsSetEffectiveChannelList() {
		return Variable_EffectiveChannelList_DEFAULT
	}
	return p.EffectiveChannelList
}

func (p *Variable) GetSchema() (v string) {
	return p.Schema
}

func (p *Variable) GetIsReadOnly() (v bool) {
	return p.IsReadOnly
}

var fieldIDToName_Variable = map[int16]string{
	1: "Keyword",
	2: "DefaultValue",
	3: "VariableType",
	4: "Channel",
	5: "Description",
	6: "Enable",
	7: "EffectiveChannelList",
	8: "Schema",
	9: "IsReadOnly",
}

func (p *Variable) IsSetEffectiveChannelList() bool {
	return p.EffectiveChannelList != nil
}

func (p *Variable) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_Variable[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *Variable) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Keyword = _field
	return nil
}
func (p *Variable) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DefaultValue = _field
	return nil
}
func (p *Variable) ReadField3(iprot thrift.TProtocol) error {

	var _field VariableType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = VariableType(v)
	}
	p.VariableType = _field
	return nil
}
func (p *Variable) ReadField4(iprot thrift.TProtocol) error {

	var _field VariableChannel
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = VariableChannel(v)
	}
	p.Channel = _field
	return nil
}
func (p *Variable) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Description = _field
	return nil
}
func (p *Variable) ReadField6(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Enable = _field
	return nil
}
func (p *Variable) ReadField7(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.EffectiveChannelList = _field
	return nil
}
func (p *Variable) ReadField8(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Schema = _field
	return nil
}
func (p *Variable) ReadField9(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.IsReadOnly = _field
	return nil
}

func (p *Variable) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("Variable"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *Variable) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Keyword", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Keyword); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *Variable) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DefaultValue", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DefaultValue); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *Variable) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("VariableType", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.VariableType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *Variable) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Channel", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.Channel)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *Variable) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Description", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Description); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *Variable) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Enable", thrift.BOOL, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.Enable); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *Variable) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetEffectiveChannelList() {
		if err = oprot.WriteFieldBegin("EffectiveChannelList", thrift.LIST, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.EffectiveChannelList)); err != nil {
			return err
		}
		for _, v := range p.EffectiveChannelList {
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}
func (p *Variable) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Schema", thrift.STRING, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Schema); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}
func (p *Variable) writeField9(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("IsReadOnly", thrift.BOOL, 9); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.IsReadOnly); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *Variable) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Variable(%+v)", *p)

}

type GroupVariableInfo struct {
	GroupName      string               `thrift:"GroupName,1" form:"GroupName" json:"GroupName" query:"GroupName"`
	GroupDesc      string               `thrift:"GroupDesc,2" form:"GroupDesc" json:"GroupDesc" query:"GroupDesc"`
	GroupExtDesc   string               `thrift:"GroupExtDesc,3" form:"GroupExtDesc" json:"GroupExtDesc" query:"GroupExtDesc"`
	VarInfoList    []*Variable          `thrift:"VarInfoList,4" form:"VarInfoList" json:"VarInfoList" query:"VarInfoList"`
	SubGroupList   []*GroupVariableInfo `thrift:"SubGroupList,5" form:"SubGroupList" json:"SubGroupList" query:"SubGroupList"`
	IsReadOnly     bool                 `thrift:"IsReadOnly,6" form:"IsReadOnly" json:"IsReadOnly" query:"IsReadOnly"`
	DefaultChannel *VariableChannel     `thrift:"DefaultChannel,7,optional" form:"DefaultChannel" json:"DefaultChannel,omitempty" query:"DefaultChannel"`
}

func NewGroupVariableInfo() *GroupVariableInfo {
	return &GroupVariableInfo{}
}

func (p *GroupVariableInfo) InitDefault() {
}

func (p *GroupVariableInfo) GetGroupName() (v string) {
	return p.GroupName
}

func (p *GroupVariableInfo) GetGroupDesc() (v string) {
	return p.GroupDesc
}

func (p *GroupVariableInfo) GetGroupExtDesc() (v string) {
	return p.GroupExtDesc
}

func (p *GroupVariableInfo) GetVarInfoList() (v []*Variable) {
	return p.VarInfoList
}

func (p *GroupVariableInfo) GetSubGroupList() (v []*GroupVariableInfo) {
	return p.SubGroupList
}

func (p *GroupVariableInfo) GetIsReadOnly() (v bool) {
	return p.IsReadOnly
}

var GroupVariableInfo_DefaultChannel_DEFAULT VariableChannel

func (p *GroupVariableInfo) GetDefaultChannel() (v VariableChannel) {
	if !p.IsSetDefaultChannel() {
		return GroupVariableInfo_DefaultChannel_DEFAULT
	}
	return *p.DefaultChannel
}

var fieldIDToName_GroupVariableInfo = map[int16]string{
	1: "GroupName",
	2: "GroupDesc",
	3: "GroupExtDesc",
	4: "VarInfoList",
	5: "SubGroupList",
	6: "IsReadOnly",
	7: "DefaultChannel",
}

func (p *GroupVariableInfo) IsSetDefaultChannel() bool {
	return p.DefaultChannel != nil
}

func (p *GroupVariableInfo) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GroupVariableInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *GroupVariableInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.GroupName = _field
	return nil
}
func (p *GroupVariableInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.GroupDesc = _field
	return nil
}
func (p *GroupVariableInfo) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.GroupExtDesc = _field
	return nil
}
func (p *GroupVariableInfo) ReadField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*Variable, 0, size)
	values := make([]Variable, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.VarInfoList = _field
	return nil
}
func (p *GroupVariableInfo) ReadField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*GroupVariableInfo, 0, size)
	values := make([]GroupVariableInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.SubGroupList = _field
	return nil
}
func (p *GroupVariableInfo) ReadField6(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.IsReadOnly = _field
	return nil
}
func (p *GroupVariableInfo) ReadField7(iprot thrift.TProtocol) error {

	var _field *VariableChannel
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := VariableChannel(v)
		_field = &tmp
	}
	p.DefaultChannel = _field
	return nil
}

func (p *GroupVariableInfo) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GroupVariableInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GroupVariableInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("GroupName", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.GroupName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *GroupVariableInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("GroupDesc", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.GroupDesc); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *GroupVariableInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("GroupExtDesc", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.GroupExtDesc); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *GroupVariableInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("VarInfoList", thrift.LIST, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.VarInfoList)); err != nil {
		return err
	}
	for _, v := range p.VarInfoList {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *GroupVariableInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SubGroupList", thrift.LIST, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.SubGroupList)); err != nil {
		return err
	}
	for _, v := range p.SubGroupList {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *GroupVariableInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("IsReadOnly", thrift.BOOL, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.IsReadOnly); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *GroupVariableInfo) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetDefaultChannel() {
		if err = oprot.WriteFieldBegin("DefaultChannel", thrift.I32, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.DefaultChannel)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *GroupVariableInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GroupVariableInfo(%+v)", *p)

}

type GetProjectVariableListReq struct {
	ProjectID string     `thrift:"ProjectID,1" form:"ProjectID" json:"ProjectID" query:"ProjectID"`
	UserID    int64      `thrift:"UserID,2" form:"UserID" json:"UserID" query:"UserID"`
	Version   int64      `thrift:"version,3" form:"version" json:"version,string" query:"version"`
	Base      *base.Base `thrift:"Base,255,optional" form:"Base" json:"Base,omitempty" query:"Base"`
}

func NewGetProjectVariableListReq() *GetProjectVariableListReq {
	return &GetProjectVariableListReq{}
}

func (p *GetProjectVariableListReq) InitDefault() {
}

func (p *GetProjectVariableListReq) GetProjectID() (v string) {
	return p.ProjectID
}

func (p *GetProjectVariableListReq) GetUserID() (v int64) {
	return p.UserID
}

func (p *GetProjectVariableListReq) GetVersion() (v int64) {
	return p.Version
}

var GetProjectVariableListReq_Base_DEFAULT *base.Base

func (p *GetProjectVariableListReq) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return GetProjectVariableListReq_Base_DEFAULT
	}
	return p.Base
}

var fieldIDToName_GetProjectVariableListReq = map[int16]string{
	1:   "ProjectID",
	2:   "UserID",
	3:   "version",
	255: "Base",
}

func (p *GetProjectVariableListReq) IsSetBase() bool {
	return p.Base != nil
}

func (p *GetProjectVariableListReq) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetProjectVariableListReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *GetProjectVariableListReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ProjectID = _field
	return nil
}
func (p *GetProjectVariableListReq) ReadField2(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UserID = _field
	return nil
}
func (p *GetProjectVariableListReq) ReadField3(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Version = _field
	return nil
}
func (p *GetProjectVariableListReq) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *GetProjectVariableListReq) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetProjectVariableListReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetProjectVariableListReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ProjectID", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ProjectID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *GetProjectVariableListReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("UserID", thrift.I64, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.UserID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *GetProjectVariableListReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("version", thrift.I64, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.Version); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *GetProjectVariableListReq) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *GetProjectVariableListReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetProjectVariableListReq(%+v)", *p)

}

type GetProjectVariableListResp struct {
	VariableList []*Variable          `thrift:"VariableList,1" form:"VariableList" json:"VariableList" query:"VariableList"`
	CanEdit      bool                 `thrift:"CanEdit,2" form:"CanEdit" json:"CanEdit" query:"CanEdit"`
	GroupConf    []*GroupVariableInfo `thrift:"GroupConf,3" form:"GroupConf" json:"GroupConf" query:"GroupConf"`
	Code         int64                `thrift:"code,253,required" form:"code,required" json:"code,required" query:"code,required"`
	Msg          string               `thrift:"msg,254,required" form:"msg,required" json:"msg,required" query:"msg,required"`
	BaseResp     *base.BaseResp       `thrift:"BaseResp,255,required" form:"BaseResp,required" json:"BaseResp,required" query:"BaseResp,required"`
}

func NewGetProjectVariableListResp() *GetProjectVariableListResp {
	return &GetProjectVariableListResp{}
}

func (p *GetProjectVariableListResp) InitDefault() {
}

func (p *GetProjectVariableListResp) GetVariableList() (v []*Variable) {
	return p.VariableList
}

func (p *GetProjectVariableListResp) GetCanEdit() (v bool) {
	return p.CanEdit
}

func (p *GetProjectVariableListResp) GetGroupConf() (v []*GroupVariableInfo) {
	return p.GroupConf
}

func (p *GetProjectVariableListResp) GetCode() (v int64) {
	return p.Code
}

func (p *GetProjectVariableListResp) GetMsg() (v string) {
	return p.Msg
}

var GetProjectVariableListResp_BaseResp_DEFAULT *base.BaseResp

func (p *GetProjectVariableListResp) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return GetProjectVariableListResp_BaseResp_DEFAULT
	}
	return p.BaseResp
}

var fieldIDToName_GetProjectVariableListResp = map[int16]string{
	1:   "VariableList",
	2:   "CanEdit",
	3:   "GroupConf",
	253: "code",
	254: "msg",
	255: "BaseResp",
}

func (p *GetProjectVariableListResp) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *GetProjectVariableListResp) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetCode bool = false
	var issetMsg bool = false
	var issetBaseResp bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 253:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField253(iprot); err != nil {
					goto ReadFieldError
				}
				issetCode = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 254:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField254(iprot); err != nil {
					goto ReadFieldError
				}
				issetMsg = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
				issetBaseResp = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetCode {
		fieldId = 253
		goto RequiredFieldNotSetError
	}

	if !issetMsg {
		fieldId = 254
		goto RequiredFieldNotSetError
	}

	if !issetBaseResp {
		fieldId = 255
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetProjectVariableListResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_GetProjectVariableListResp[fieldId]))
}

func (p *GetProjectVariableListResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*Variable, 0, size)
	values := make([]Variable, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.VariableList = _field
	return nil
}
func (p *GetProjectVariableListResp) ReadField2(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CanEdit = _field
	return nil
}
func (p *GetProjectVariableListResp) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*GroupVariableInfo, 0, size)
	values := make([]GroupVariableInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.GroupConf = _field
	return nil
}
func (p *GetProjectVariableListResp) ReadField253(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Code = _field
	return nil
}
func (p *GetProjectVariableListResp) ReadField254(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Msg = _field
	return nil
}
func (p *GetProjectVariableListResp) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *GetProjectVariableListResp) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetProjectVariableListResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField253(oprot); err != nil {
			fieldId = 253
			goto WriteFieldError
		}
		if err = p.writeField254(oprot); err != nil {
			fieldId = 254
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetProjectVariableListResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("VariableList", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.VariableList)); err != nil {
		return err
	}
	for _, v := range p.VariableList {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *GetProjectVariableListResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CanEdit", thrift.BOOL, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.CanEdit); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *GetProjectVariableListResp) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("GroupConf", thrift.LIST, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.GroupConf)); err != nil {
		return err
	}
	for _, v := range p.GroupConf {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *GetProjectVariableListResp) writeField253(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("code", thrift.I64, 253); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.Code); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 253 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 253 end error: ", p), err)
}
func (p *GetProjectVariableListResp) writeField254(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("msg", thrift.STRING, 254); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Msg); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 254 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 254 end error: ", p), err)
}
func (p *GetProjectVariableListResp) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.BaseResp.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *GetProjectVariableListResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetProjectVariableListResp(%+v)", *p)

}

type UpdateProjectVariableReq struct {
	ProjectID    string      `thrift:"ProjectID,1" form:"ProjectID" json:"ProjectID" query:"ProjectID"`
	UserID       int64       `thrift:"UserID,2" form:"UserID" json:"UserID" query:"UserID"`
	VariableList []*Variable `thrift:"VariableList,3" form:"VariableList" json:"VariableList" query:"VariableList"`
	Base         *base.Base  `thrift:"Base,255,optional" form:"Base" json:"Base,omitempty" query:"Base"`
}

func NewUpdateProjectVariableReq() *UpdateProjectVariableReq {
	return &UpdateProjectVariableReq{}
}

func (p *UpdateProjectVariableReq) InitDefault() {
}

func (p *UpdateProjectVariableReq) GetProjectID() (v string) {
	return p.ProjectID
}

func (p *UpdateProjectVariableReq) GetUserID() (v int64) {
	return p.UserID
}

func (p *UpdateProjectVariableReq) GetVariableList() (v []*Variable) {
	return p.VariableList
}

var UpdateProjectVariableReq_Base_DEFAULT *base.Base

func (p *UpdateProjectVariableReq) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return UpdateProjectVariableReq_Base_DEFAULT
	}
	return p.Base
}

var fieldIDToName_UpdateProjectVariableReq = map[int16]string{
	1:   "ProjectID",
	2:   "UserID",
	3:   "VariableList",
	255: "Base",
}

func (p *UpdateProjectVariableReq) IsSetBase() bool {
	return p.Base != nil
}

func (p *UpdateProjectVariableReq) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UpdateProjectVariableReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *UpdateProjectVariableReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ProjectID = _field
	return nil
}
func (p *UpdateProjectVariableReq) ReadField2(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UserID = _field
	return nil
}
func (p *UpdateProjectVariableReq) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*Variable, 0, size)
	values := make([]Variable, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.VariableList = _field
	return nil
}
func (p *UpdateProjectVariableReq) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *UpdateProjectVariableReq) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("UpdateProjectVariableReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *UpdateProjectVariableReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ProjectID", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ProjectID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *UpdateProjectVariableReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("UserID", thrift.I64, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.UserID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *UpdateProjectVariableReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("VariableList", thrift.LIST, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.VariableList)); err != nil {
		return err
	}
	for _, v := range p.VariableList {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *UpdateProjectVariableReq) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *UpdateProjectVariableReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateProjectVariableReq(%+v)", *p)

}

type UpdateProjectVariableResp struct {
	Code     int64          `thrift:"code,253,required" form:"code,required" json:"code,required" query:"code,required"`
	Msg      string         `thrift:"msg,254,required" form:"msg,required" json:"msg,required" query:"msg,required"`
	BaseResp *base.BaseResp `thrift:"BaseResp,255,required" form:"BaseResp,required" json:"BaseResp,required" query:"BaseResp,required"`
}

func NewUpdateProjectVariableResp() *UpdateProjectVariableResp {
	return &UpdateProjectVariableResp{}
}

func (p *UpdateProjectVariableResp) InitDefault() {
}

func (p *UpdateProjectVariableResp) GetCode() (v int64) {
	return p.Code
}

func (p *UpdateProjectVariableResp) GetMsg() (v string) {
	return p.Msg
}

var UpdateProjectVariableResp_BaseResp_DEFAULT *base.BaseResp

func (p *UpdateProjectVariableResp) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return UpdateProjectVariableResp_BaseResp_DEFAULT
	}
	return p.BaseResp
}

var fieldIDToName_UpdateProjectVariableResp = map[int16]string{
	253: "code",
	254: "msg",
	255: "BaseResp",
}

func (p *UpdateProjectVariableResp) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *UpdateProjectVariableResp) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetCode bool = false
	var issetMsg bool = false
	var issetBaseResp bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 253:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField253(iprot); err != nil {
					goto ReadFieldError
				}
				issetCode = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 254:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField254(iprot); err != nil {
					goto ReadFieldError
				}
				issetMsg = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
				issetBaseResp = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetCode {
		fieldId = 253
		goto RequiredFieldNotSetError
	}

	if !issetMsg {
		fieldId = 254
		goto RequiredFieldNotSetError
	}

	if !issetBaseResp {
		fieldId = 255
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UpdateProjectVariableResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_UpdateProjectVariableResp[fieldId]))
}

func (p *UpdateProjectVariableResp) ReadField253(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Code = _field
	return nil
}
func (p *UpdateProjectVariableResp) ReadField254(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Msg = _field
	return nil
}
func (p *UpdateProjectVariableResp) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *UpdateProjectVariableResp) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("UpdateProjectVariableResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField253(oprot); err != nil {
			fieldId = 253
			goto WriteFieldError
		}
		if err = p.writeField254(oprot); err != nil {
			fieldId = 254
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *UpdateProjectVariableResp) writeField253(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("code", thrift.I64, 253); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.Code); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 253 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 253 end error: ", p), err)
}
func (p *UpdateProjectVariableResp) writeField254(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("msg", thrift.STRING, 254); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Msg); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 254 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 254 end error: ", p), err)
}
func (p *UpdateProjectVariableResp) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.BaseResp.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *UpdateProjectVariableResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateProjectVariableResp(%+v)", *p)

}

type GetMemoryVariableMetaReq struct {
	ConnectorID   string            `thrift:"ConnectorID,1" form:"ConnectorID" json:"ConnectorID" query:"ConnectorID"`
	ConnectorType VariableConnector `thrift:"ConnectorType,2" form:"ConnectorType" json:"ConnectorType" query:"ConnectorType"`
	Version       *string           `thrift:"version,3,optional" form:"version" json:"version,omitempty" query:"version"`
	Base          *base.Base        `thrift:"Base,255,optional" form:"Base" json:"Base,omitempty" query:"Base"`
}

func NewGetMemoryVariableMetaReq() *GetMemoryVariableMetaReq {
	return &GetMemoryVariableMetaReq{}
}

func (p *GetMemoryVariableMetaReq) InitDefault() {
}

func (p *GetMemoryVariableMetaReq) GetConnectorID() (v string) {
	return p.ConnectorID
}

func (p *GetMemoryVariableMetaReq) GetConnectorType() (v VariableConnector) {
	return p.ConnectorType
}

var GetMemoryVariableMetaReq_Version_DEFAULT string

func (p *GetMemoryVariableMetaReq) GetVersion() (v string) {
	if !p.IsSetVersion() {
		return GetMemoryVariableMetaReq_Version_DEFAULT
	}
	return *p.Version
}

var GetMemoryVariableMetaReq_Base_DEFAULT *base.Base

func (p *GetMemoryVariableMetaReq) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return GetMemoryVariableMetaReq_Base_DEFAULT
	}
	return p.Base
}

var fieldIDToName_GetMemoryVariableMetaReq = map[int16]string{
	1:   "ConnectorID",
	2:   "ConnectorType",
	3:   "version",
	255: "Base",
}

func (p *GetMemoryVariableMetaReq) IsSetVersion() bool {
	return p.Version != nil
}

func (p *GetMemoryVariableMetaReq) IsSetBase() bool {
	return p.Base != nil
}

func (p *GetMemoryVariableMetaReq) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetMemoryVariableMetaReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *GetMemoryVariableMetaReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ConnectorID = _field
	return nil
}
func (p *GetMemoryVariableMetaReq) ReadField2(iprot thrift.TProtocol) error {

	var _field VariableConnector
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = VariableConnector(v)
	}
	p.ConnectorType = _field
	return nil
}
func (p *GetMemoryVariableMetaReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Version = _field
	return nil
}
func (p *GetMemoryVariableMetaReq) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *GetMemoryVariableMetaReq) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetMemoryVariableMetaReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetMemoryVariableMetaReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ConnectorID", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ConnectorID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *GetMemoryVariableMetaReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ConnectorType", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.ConnectorType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *GetMemoryVariableMetaReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetVersion() {
		if err = oprot.WriteFieldBegin("version", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Version); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *GetMemoryVariableMetaReq) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *GetMemoryVariableMetaReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetMemoryVariableMetaReq(%+v)", *p)

}

// It should be the rpc interface for workflow, no authentication is required, VariableChannel
type GetMemoryVariableMetaResp struct {
	VariableMap map[VariableChannel][]*Variable `thrift:"VariableMap,1" form:"VariableMap" json:"VariableMap" query:"VariableMap"`
	Code        int64                           `thrift:"code,253,required" form:"code,required" json:"code,required" query:"code,required"`
	Msg         string                          `thrift:"msg,254,required" form:"msg,required" json:"msg,required" query:"msg,required"`
	BaseResp    *base.BaseResp                  `thrift:"BaseResp,255,required" form:"BaseResp,required" json:"BaseResp,required" query:"BaseResp,required"`
}

func NewGetMemoryVariableMetaResp() *GetMemoryVariableMetaResp {
	return &GetMemoryVariableMetaResp{}
}

func (p *GetMemoryVariableMetaResp) InitDefault() {
}

func (p *GetMemoryVariableMetaResp) GetVariableMap() (v map[VariableChannel][]*Variable) {
	return p.VariableMap
}

func (p *GetMemoryVariableMetaResp) GetCode() (v int64) {
	return p.Code
}

func (p *GetMemoryVariableMetaResp) GetMsg() (v string) {
	return p.Msg
}

var GetMemoryVariableMetaResp_BaseResp_DEFAULT *base.BaseResp

func (p *GetMemoryVariableMetaResp) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return GetMemoryVariableMetaResp_BaseResp_DEFAULT
	}
	return p.BaseResp
}

var fieldIDToName_GetMemoryVariableMetaResp = map[int16]string{
	1:   "VariableMap",
	253: "code",
	254: "msg",
	255: "BaseResp",
}

func (p *GetMemoryVariableMetaResp) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *GetMemoryVariableMetaResp) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetCode bool = false
	var issetMsg bool = false
	var issetBaseResp bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 253:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField253(iprot); err != nil {
					goto ReadFieldError
				}
				issetCode = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 254:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField254(iprot); err != nil {
					goto ReadFieldError
				}
				issetMsg = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
				issetBaseResp = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetCode {
		fieldId = 253
		goto RequiredFieldNotSetError
	}

	if !issetMsg {
		fieldId = 254
		goto RequiredFieldNotSetError
	}

	if !issetBaseResp {
		fieldId = 255
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetMemoryVariableMetaResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_GetMemoryVariableMetaResp[fieldId]))
}

func (p *GetMemoryVariableMetaResp) ReadField1(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[VariableChannel][]*Variable, size)
	for i := 0; i < size; i++ {
		var _key VariableChannel
		if v, err := iprot.ReadI32(); err != nil {
			return err
		} else {
			_key = VariableChannel(v)
		}
		_, size, err := iprot.ReadListBegin()
		if err != nil {
			return err
		}
		_val := make([]*Variable, 0, size)
		values := make([]Variable, size)
		for i := 0; i < size; i++ {
			_elem := &values[i]
			_elem.InitDefault()

			if err := _elem.Read(iprot); err != nil {
				return err
			}

			_val = append(_val, _elem)
		}
		if err := iprot.ReadListEnd(); err != nil {
			return err
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.VariableMap = _field
	return nil
}
func (p *GetMemoryVariableMetaResp) ReadField253(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Code = _field
	return nil
}
func (p *GetMemoryVariableMetaResp) ReadField254(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Msg = _field
	return nil
}
func (p *GetMemoryVariableMetaResp) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *GetMemoryVariableMetaResp) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetMemoryVariableMetaResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField253(oprot); err != nil {
			fieldId = 253
			goto WriteFieldError
		}
		if err = p.writeField254(oprot); err != nil {
			fieldId = 254
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetMemoryVariableMetaResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("VariableMap", thrift.MAP, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteMapBegin(thrift.I32, thrift.LIST, len(p.VariableMap)); err != nil {
		return err
	}
	for k, v := range p.VariableMap {
		if err := oprot.WriteI32(int32(k)); err != nil {
			return err
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(v)); err != nil {
			return err
		}
		for _, v := range v {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
	}
	if err := oprot.WriteMapEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *GetMemoryVariableMetaResp) writeField253(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("code", thrift.I64, 253); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.Code); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 253 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 253 end error: ", p), err)
}
func (p *GetMemoryVariableMetaResp) writeField254(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("msg", thrift.STRING, 254); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Msg); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 254 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 254 end error: ", p), err)
}
func (p *GetMemoryVariableMetaResp) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.BaseResp.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *GetMemoryVariableMetaResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetMemoryVariableMetaResp(%+v)", *p)

}
